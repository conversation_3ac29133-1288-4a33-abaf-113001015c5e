﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using HolyBless.Permissions;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using System.Linq.Dynamic.Core;
using HolyBless.Entities.Books;
using HolyBless.Books.Dtos;
using HolyBless.Buckets;
using HolyBless.Services;

namespace HolyBless.Books;

[Authorize(HolyBlessPermissions.EBooks.Default)]
public class EBookAppService : ReadOnlyEBookAppService, IEBookAppService
{
    private readonly IRepository<EBook, int> _repository;

    public EBookAppService(
        IRepository<EBook, int> repository,
        IRepository<Chapter, int> chapterRepository,
        IRepository<ChapterToArticle> chapterToArticleRepository,
        IRequestContextService requestContextService,
        ICachedFileUrlAppService cachedFileUrlAppService
        )
        : base(repository, chapterRepository, chapterToArticleRepository, requestContextService, cachedFileUrlAppService)
    {
        _repository = repository;
    }

    [Authorize(HolyBlessPermissions.EBooks.Create)]
    public async Task<EBookDto> CreateAsync(CreateUpdateEBookDto input)
    {
        var book = ObjectMapper.Map<CreateUpdateEBookDto, EBook>(input);
        await _repository.InsertAsync(book);
        return ObjectMapper.Map<EBook, EBookDto>(book);
    }

    [Authorize(HolyBlessPermissions.EBooks.Edit)]
    public async Task<EBookDto> UpdateAsync(int id, CreateUpdateEBookDto input)
    {
        var book = await _repository.GetAsync(id);
        ObjectMapper.Map(input, book);
        await _repository.UpdateAsync(book);
        return ObjectMapper.Map<EBook, EBookDto>(book);
    }

    public async Task<EBookDto> GetAsync(int id)
    {
        var eBook = await _repository.GetAsync(id);
        var rt = ObjectMapper.Map<EBook, EBookDto>(eBook);
        await FillThumbnailUrl(rt);
        return rt;
    }

    public async Task<PagedResultDto<EBookDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        var queryable = await _repository.GetQueryableAsync();
        var query = queryable
            .OrderBy(e => e.Weight)
            .ThenBy(e => e.Title)
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount);

        var eBooks = await AsyncExecuter.ToListAsync(query);
        var totalCount = await AsyncExecuter.CountAsync(queryable);

        var rtList = ObjectMapper.Map<List<EBook>, List<EBookDto>>(eBooks);
        await FillThumbnailUrls(rtList);
        return new PagedResultDto<EBookDto>(
            totalCount,
            rtList
        );
    }

    /// <summary>
    /// [Admin] Get all eBooks without pagination, filtered by language code.
    /// </summary>
    /// <param name="languageCode">Optional language code to filter eBooks. If null, uses all languages.</param>
    /// <returns></returns>
    public async Task<List<EBookDto>> GetAllEBooksAsync(string? languageCode = null)
    {
        var queryable = await _repository.GetQueryableAsync();
        var query = queryable
            .WhereIf(!string.IsNullOrEmpty(languageCode), x => x.LanguageCode != null && x.LanguageCode.ToLower() == languageCode!.ToLower())
            .OrderBy(x => x.Weight);
        var eBooks = await AsyncExecuter.ToListAsync(query);
        var rtList = ObjectMapper.Map<List<EBook>, List<EBookDto>>(eBooks);
        await FillThumbnailUrls(rtList);
        return rtList;
    }

    /// <summary>
    /// Usage Admin page: Link an eBook to a channel.
    /// </summary>
    /// <param name="channelId"></param>
    /// <param name="eBookId"></param>
    /// <returns></returns>
    //[Authorize(HolyBlessPermissions.EBooks.Default)]
    public async Task LinkToChannel(int channelId, int eBookId)
    {
        var eBook = await _repository.GetAsync(eBookId);
        eBook.ChannelId = channelId;
        await _repository.UpdateAsync(eBook, true);
    }

    /// <summary>
    /// Usage Admin page: Link multiple eBooks to a channel.
    /// </summary>
    /// <param name="channelId"></param>
    /// <param name="eBookIds"></param>
    /// <returns></returns>
    //[Authorize(HolyBlessPermissions.EBooks.Default)]
    public async Task LinkToChannel(int channelId, List<int> eBookIds)
    {
        var eBooks = await _repository.GetListAsync(x => eBookIds.Contains(x.Id));
        foreach (var eBook in eBooks)
        {
            eBook.ChannelId = channelId;
        }
        await _repository.UpdateManyAsync(eBooks, autoSave: true);
    }

    /// <summary>
    /// Admin page: Unlink an eBook from a channel (Set ChannelId to null)
    /// </summary>
    /// <param name="eBookId"></param>
    /// <returns></returns>
    public async Task UnlinkFromChannel(int eBookId)
    {
        var eBook = await _repository.GetAsync(eBookId);
        eBook.ChannelId = null;
        await _repository.UpdateAsync(eBook, true);
    }

    /// <summary>
    /// Admin page: Unlink multiple eBooks from their channels (Set ChannelId to null)
    /// </summary>
    /// <param name="eBookIds"></param>
    /// <returns></returns>
    public async Task UnlinkFromChannel(List<int> eBookIds)
    {
        var eBooks = await _repository.GetListAsync(x => eBookIds.Contains(x.Id));
        foreach (var eBook in eBooks)
        {
            eBook.ChannelId = null;
        }
        await _repository.UpdateManyAsync(eBooks, autoSave: true);
    }

    [Authorize(HolyBlessPermissions.EBooks.Delete)]
    public async Task DeleteAsync(int id)
    {
        await _repository.DeleteAsync(id);
    }
}