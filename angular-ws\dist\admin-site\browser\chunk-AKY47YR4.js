import{a as fe}from"./chunk-DZLXCZWK.js";import{a as L}from"./chunk-33OP5RZM.js";import{a as ye,b as we}from"./chunk-G4FA4R76.js";import{a as he}from"./chunk-SPDIY6EA.js";import{a as De}from"./chunk-WSOPBUUS.js";import{a as oe,b as re,c as se,d as de,e as pe,f as me,g as ce,h as ue}from"./chunk-JBJGPYJN.js";import{b as ae}from"./chunk-E6VTCYRR.js";import{a as ie,b as le,c as W}from"./chunk-W4NOLTEH.js";import{A as te,B as ne,I as Ce,J as ge,K as be,p as X,q as Y,r as Z,s as K,t as $,u as ee}from"./chunk-NSHV5XN7.js";import{Ba as A,C as G,E as J,F as Q,H as U,gb as _e}from"./chunk-GXYB24H2.js";import{$a as s,$b as E,Bb as H,Ca as _,Db as M,Eb as V,Fb as a,Gb as o,Hb as f,Lb as S,Qb as w,Rb as b,Zb as u,a as P,b as F,bc as h,cc as C,dc as g,ec as j,fc as T,ga as D,hc as N,jb as O,na as p,oa as m,oc as z,pb as x,qc as q,rc as k,wb as c,zb as v}from"./chunk-AKB6GSCA.js";var Se=(e=>(e[e.Audio=0]="Audio",e[e.Video=1]="Video",e))(Se||{}),B=_e(Se);var Me=()=>({"min-width":"50rem"}),Te=()=>({width:"25rem"}),Ve=(d,i)=>({type:d,selectData:i}),ve=(d,i)=>i.field;function Ee(d,i){if(d&1&&(a(0,"th",20)(1,"div",22),u(2),f(3,"p-columnFilter",23),o()()),d&2){let e=i.$implicit;s(2),E(" ",e.header," "),s(),c("field",e.field)}}function ke(d,i){if(d&1&&(a(0,"tr"),f(1,"th",18),a(2,"th",19),f(3,"p-tableHeaderCheckbox"),o(),M(4,Ee,4,2,"th",20,ve),f(6,"th",21),o()),d&2){let e=i.$implicit;s(4),V(e)}}function Ae(d,i){if(d&1&&(a(0,"td"),u(1),z(2,"tableDisplay"),o()),d&2){let e=i.$implicit,t=b().$implicit;s(),E(" ",q(2,1,t[e.field],N(4,Ve,e.type,e.selectData))," ")}}function We(d,i){if(d&1){let e=S();a(0,"tr",24)(1,"td"),f(2,"span",25),o(),a(3,"td"),f(4,"p-tableCheckbox",26),o(),M(5,Ae,3,7,"td",null,ve),a(7,"td")(8,"p-button",27),w("onClick",function(){let n=p(e).$implicit,l=b();return m(l.handleEdit(n))}),o(),a(9,"p-button",28),w("onClick",function(n){let l=p(e).$implicit,r=b();return m(r.handleDelete(n,l))}),o()()()}if(d&2){let e=i.$implicit,t=i.columns,n=i.rowIndex;c("pReorderableRow",n),s(4),c("value",e),s(),V(t),s(3),c("text",!0),s(),c("text",!0)}}function Le(d,i){if(d&1){let e=S();a(0,"div",12)(1,"div",16)(2,"label"),u(3,"Title"),o(),a(4,"input",29),g("ngModelChange",function(n){p(e);let l=b();return C(l.selectData().title,n)||(l.selectData().title=n),m(n)}),o()(),a(5,"div",16)(6,"label"),u(7,"Type"),o(),a(8,"p-select",30),g("ngModelChange",function(n){p(e);let l=b();return C(l.selectData().albumType,n)||(l.selectData().albumType=n),m(n)}),o()(),a(9,"div",16)(10,"label"),u(11,"Channel"),o(),a(12,"p-treeselect",17),g("ngModelChange",function(n){p(e);let l=b();return C(l.selectData().channel,n)||(l.selectData().channel=n),m(n)}),o()(),a(13,"div",16)(14,"label"),u(15,"Thumbnail"),o(),a(16,"input",29),g("ngModelChange",function(n){p(e);let l=b();return C(l.selectData().thumbnailFileId,n)||(l.selectData().thumbnailFileId=n),m(n)}),o()(),a(17,"div",16)(18,"label"),u(19,"Spoken language"),o(),a(20,"p-select",30),g("ngModelChange",function(n){p(e);let l=b();return C(l.selectData().spokenLangCode,n)||(l.selectData().spokenLangCode=n),m(n)}),o()(),a(21,"div",16)(22,"label"),u(23,"Description"),o(),a(24,"input",29),g("ngModelChange",function(n){p(e);let l=b();return C(l.selectData().description,n)||(l.selectData().description=n),m(n)}),o()()()}if(d&2){let e=b();s(4),h("ngModel",e.selectData().title),s(4),c("options",e.albumTypeOptions),h("ngModel",e.selectData().albumType),s(4),h("ngModel",e.selectData().channel),c("options",e.channelTree()),s(4),h("ngModel",e.selectData().thumbnailFileId),s(4),c("options",e.spokenLangList),h("ngModel",e.selectData().spokenLangCode),s(4),h("ngModel",e.selectData().description)}}var xe=class d{constructor(){this.#e=D(fe);this.confirmationService=D(A);this.#t=D(he);this.langOptions=[{label:"zh-Hans"},{label:"zh-Hant"},{label:"en"}];this.lang=_("zh-Hans");this.columns=[{field:"id",header:"ID"},{field:"title",header:"Title"},{field:"albumType",header:"Type",type:"select",selectData:B},{field:"channelId",header:"Channel"},{field:"spokenLangCode",header:"Spoken language",type:"select",selectData:L}];this.first=_(0);this.rows=_(10);this.totalRecords=_(0);this.data=_([]);this.selectedRows=_([]);this.albumTypeOptions=B;this.spokenLangList=L;this.channelTree=_([]);this.channelMap=new Map;this.addToChannelVisible=_(!1);this.selectChannel=_(null);this.contentCategoryOptions=De;this.selectData=_(null);this.mode=_("create")}#e;#t;ngOnInit(){this.loadData(),this.loadChannelData()}loadData(){this.#e.getAllAlbums(this.lang()).subscribe({next:i=>{this.data.set(i||[])}})}loadChannelData(){this.#t.getAllChannels(this.lang()).subscribe({next:i=>{this.channelTree.set(this.buildTreeData(i,void 0,void 0,void 0,this.channelMap))}})}buildTreeData(i,e="parentChannelId",t="id",n="name",l=new Map){let r=[];return i.forEach(y=>{l.set(y[t],F(P({},y),{key:y[t],label:y[n],children:[]}))}),i.forEach(y=>{let R=l.get(y[t]);if(!y[e])r.push(R);else{let I=l.get(y[e]);I&&I.children.push(R)}}),r}onPageChange(i){this.first.set(i.first),this.loadData()}handleEdit(i){let e=W(i);e.channelId&&(e.channel=this.channelMap.get(e.channelId)),e.deliveryDate&&(e.deliveryDate=new Date(e.deliveryDate)),this.selectData.set(e),this.mode.set("edit")}handleCreate(){this.selectData.set({}),this.mode.set("create")}updateSelectData(){let i=W(this.selectData());i.channel&&(i.channelId=i.channel.id,delete i.channel),this.mode()==="edit"?this.#e.update(i.id,i).subscribe({next:e=>{this.loadData(),this.selectData.set(null)}}):this.#e.create(i).subscribe({next:e=>{this.loadData(),this.selectData.set(null)}})}handleDelete(i,e){this.confirmationService.confirm({target:i.currentTarget,message:"Do you want to delete this record?",icon:"pi pi-info-circle",rejectButtonProps:{label:"Cancel",severity:"secondary",outlined:!0},acceptButtonProps:{label:"Delete",severity:"danger"},accept:()=>{this.#e.delete(e.id).subscribe({next:t=>{this.loadData()}})}})}handleAddToChannel(){this.addToChannelVisible.set(!0)}changeChannel(){let i=this.selectedRows().map(e=>e.id);this.#e.linkToChannelByChannelIdAndAlbumIds(this.selectChannel().id,i).subscribe({next:e=>{this.loadData(),this.selectChannel.set(null),this.selectedRows.set([]),this.addToChannelVisible.set(!1)}})}static{this.\u0275fac=function(e){return new(e||d)}}static{this.\u0275cmp=O({type:d,selectors:[["app-album"]],features:[j([A])],decls:32,vars:26,consts:[["header",""],["body",""],[1,"p-4","flex","flex-col"],[1,"flex","justify-between"],["optionLabel","label","optionValue","label",3,"ngModelChange","onChange","options","ngModel"],[1,"flex","gap-2"],["label","Create",3,"onClick","text"],["severity","danger","label","Delete",3,"text"],["label","Add to channel",3,"onClick","text"],[1,"flex-1","overflow-auto"],[3,"selectionChange","value","columns","reorderableColumns","tableStyle","selection"],[3,"visibleChange","header","modal","visible"],[1,"form"],[1,"w-full","grid","grid-cols-2","gap-2","mt-4"],["styleClass","w-full","severity","secondary"],["styleClass","w-full",3,"onClick"],[1,"form-item"],["containerStyleClass","w-full","appendTo","body",3,"ngModelChange","ngModel","options"],[2,"width","3rem"],[2,"width","4rem"],["pReorderableColumn","",2,"width","4rem"],[2,"width","8rem"],[1,"flex","items-center"],["type","text","display","menu",3,"field"],[3,"pReorderableRow"],["pReorderableRowHandle","",1,"pi","pi-bars"],[3,"value"],["styleClass","!rounded-full","icon","pi pi-pencil",3,"onClick","text"],["styleClass","!rounded-full","icon","pi pi-trash","severity","danger",3,"onClick","text"],["type","text","pInputText","",3,"ngModelChange","ngModel"],["optionLabel","key","optionValue","value","appendTo","body",3,"ngModelChange","options","ngModel"]],template:function(e,t){if(e&1){let n=S();a(0,"div",2)(1,"div",3)(2,"p-selectbutton",4),g("ngModelChange",function(r){return p(n),C(t.lang,r)||(t.lang=r),m(r)}),w("onChange",function(){return p(n),m(t.loadData())}),o(),a(3,"div",5)(4,"p-button",6),w("onClick",function(){return p(n),m(t.handleCreate())}),o(),f(5,"p-button",7),a(6,"p-button",8),w("onClick",function(){return p(n),m(t.handleAddToChannel())}),o()()(),a(7,"div",9)(8,"p-table",10),g("selectionChange",function(r){return p(n),C(t.selectedRows,r)||(t.selectedRows=r),m(r)}),x(9,ke,7,0,"ng-template",null,0,k)(11,We,10,4,"ng-template",null,1,k),o()()(),f(13,"p-confirmpopup"),a(14,"p-dialog",11),g("visibleChange",function(r){return p(n),C(t.selectData,r)||(t.selectData=r),m(r)}),x(15,Le,25,9,"div",12),a(16,"div",13)(17,"p-button",14),u(18,"Cancel"),o(),a(19,"p-button",15),w("onClick",function(){return p(n),m(t.updateSelectData())}),u(20," Confirm "),o()()(),a(21,"p-dialog",11),g("visibleChange",function(r){return p(n),C(t.addToChannelVisible,r)||(t.addToChannelVisible=r),m(r)}),a(22,"div",12)(23,"div",16)(24,"label"),u(25,"Channel"),o(),a(26,"p-treeselect",17),g("ngModelChange",function(r){return p(n),C(t.selectChannel,r)||(t.selectChannel=r),m(r)}),o()()(),a(27,"div",13)(28,"p-button",14),u(29,"Cancel"),o(),a(30,"p-button",15),w("onClick",function(){return p(n),m(t.changeChannel())}),u(31," Confirm "),o()()()}e&2&&(s(2),c("options",t.langOptions),h("ngModel",t.lang),s(2),c("text",!0),s(),c("text",!0),s(),c("text",!0),s(2),c("value",t.data())("columns",t.columns)("reorderableColumns",!0)("tableStyle",T(23,Me)),h("selection",t.selectedRows),s(6),v(T(24,Te)),c("header",t.mode()==="edit"?"Edit":"Create")("modal",!0),h("visible",t.selectData),s(),H(t.selectData()?15:-1),s(6),v(T(25,Te)),c("header","Add to channel")("modal",!0),h("visible",t.addToChannelVisible),s(5),h("ngModel",t.selectChannel),c("options",t.channelTree()))},dependencies:[Y,X,le,ie,U,G,J,Q,ee,$,K,Z,ne,te,ae,ge,Ce,be,ue,oe,re,se,de,pe,me,ce,we,ye],encapsulation:2})}};export{xe as AlbumComponent};
