import{a as se,b as de,c as pe,d as ce}from"./chunk-BWAA6Q23.js";import{a as re}from"./chunk-Y47MM6IF.js";import{a as Y}from"./chunk-UY6TY564.js";import{a as oe,b as ne}from"./chunk-OU2HA4VM.js";import"./chunk-XDQMAYN7.js";import{a as ie}from"./chunk-SXZHXKH7.js";import{a as ae}from"./chunk-F6EN6KXA.js";import{a as ee}from"./chunk-MMY5JCBC.js";import{c as X}from"./chunk-UG6YMQF5.js";import{c as Q}from"./chunk-KDA4DUCX.js";import"./chunk-OJMUFUAX.js";import{a as te}from"./chunk-HVFBHGMG.js";import{a as le}from"./chunk-KMQZ7S6W.js";import"./chunk-DORW4MUZ.js";import"./chunk-FQ6ETY5F.js";import{A as K,z as J}from"./chunk-7BEB76UR.js";import"./chunk-TUEICD7X.js";import"./chunk-6VVQJ5MN.js";import"./chunk-SWDY6RKD.js";import{c as H,d as Z}from"./chunk-C42ROIQ5.js";import"./chunk-EO3QMPVM.js";import"./chunk-GXIFCJEC.js";import{c as q,d as G}from"./chunk-P3PQXH52.js";import{ba as $}from"./chunk-TRW3QJR4.js";import"./chunk-BMA7WWEI.js";import{E as j,H as A,N as U,g as O,h as W,i as L,k as z}from"./chunk-O4TW5EFJ.js";import{$b as b,Bb as R,Fb as n,Gb as a,Hb as h,Kb as I,Lb as v,Qb as C,Rb as u,S as M,Za as r,_b as D,a as T,ac as y,bc as V,dc as F,ea as p,ec as N,fc as P,ib as k,la as c,ma as m,ob as g,qc as w,rc as S,sc as B,uc as x,wb as d,yb as E,za as _}from"./chunk-6TTFYGC3.js";import"./chunk-QWWW7GFA.js";function ue(o,t){if(o&1&&h(0,"app-mobile-catagory-files-drawer",5),o&2){let e=u(),i=D(19);d("leftTemplate",i)("showRightIcon",!1)("leftDrawerVisible",e.leftDrawerVisible)}}function fe(o,t){o&1&&I(0)}function _e(o,t){if(o&1&&g(0,fe,1,0,"ng-container",13),o&2){u();let e=D(19);d("ngTemplateOutlet",e)}}function ge(o,t){if(o&1&&(n(0,"tr")(1,"th",14),h(2,"p-tableHeaderCheckbox"),a(),h(3,"th",14),n(4,"th"),b(5),a(),n(6,"th"),b(7),a(),h(8,"th"),a()),o&2){let e=u();r(5),y(e.i18nService.translate("name")),r(2),y(e.i18nService.translate("lastModifiedTime"))}}function he(o,t){if(o&1){let e=v();n(0,"tr")(1,"td"),h(2,"p-tableCheckbox",15),a(),n(3,"td"),b(4),a(),n(5,"td"),b(6),w(7,"removeExtension"),a(),n(8,"td"),b(9),w(10,"date"),a(),n(11,"td")(12,"div",7),h(13,"p-button",16),n(14,"p-button",17),C("onClick",function(){let l=c(e).$implicit,s=u();return m(s.playVideo(l))}),a()()()()}if(o&2){let e=t.$implicit,i=t.rowIndex;r(2),d("value",e),r(2),y(i+1),r(2),y(e.title||S(7,8,e.fileName)),r(3),V(" ",B(10,10,e.lastModificationTime,"yyyy-MM-dd")," "),r(4),d("outlined",!0)("appDownload",e.fileUrl)("downloadName",e.fileName),r(),d("outlined",!0)}}function Ce(o,t){}function be(o,t){if(o&1){let e=v();n(0,"p",7),b(1),n(2,"i",20),C("click",function(l){let s=c(e).$implicit,f=u(2);return m(f.folderDownload(l,s))}),a()()}if(o&2){let e=t.$implicit;r(),V(" ",e.label," ")}}function we(o,t){if(o&1){let e=v();n(0,"p-tree",18),w(1,"async"),P("selectionChange",function(l){c(e);let s=u();return N(s.selectedNode,l)||(s.selectedNode=l),m(l)}),C("onNodeSelect",function(l){c(e);let s=u();return m(s.loadFiles(l.node))}),g(2,be,3,1,"ng-template",19),a()}if(o&2){let e=u();d("value",e.files())("styleClass",`${S(1,4,e.mobileService.isMobile)?"w-full":"w-[20rem]"} h-full`),F("selection",e.selectedNode),d("virtualScroll",!0)}}var me=class o{constructor(){this.MediaType=Q;this.first=_(0);this.rows=_(10);this.totalRecords=_(0);this.#e=p(re);this.#t=p(le);this.route=p(j);this.router=p(A);this.i18nService=p(J);this.subs=new T;this.loadingService=p(K);this.playerService=p(X);this.downloadService=p(ae);this.selectedProducts=[];this.mobileService=p(te);this.leftDrawerVisible=_(!1);this.files=_([]);this.products=_([])}#e;#t;ngOnInit(){this.route.params.subscribe(t=>{let e=t.folderId;this.loadFolderDetails(e),e&&this.changeLanguage(e)})}changeLanguage(t){this.subs.unsubscribe(),this.subs=new T;let e=this.i18nService.language$.pipe(M(1)).subscribe(i=>{this.#t.getMatchedChannelByVirtualFolderId(t).subscribe({next:l=>{if(!l){this.router.navigateByUrl("/landing");return}this.router.navigateByUrl(`/virtual-folder/list/${l.id}`)}})});this.subs.add(e)}loadFolderDetails(t){t&&(this.loadingService.show(),this.#e.getVirtualFolderTree(t).subscribe({next:e=>{this.files.set(e.map(i=>this.buildTreeNode(i))),this.loadingService.hide()},error:e=>{console.error("\u83B7\u53D6\u6587\u4EF6\u5939\u8BE6\u60C5\u5931\u8D25:",e),this.loadingService.hide()}}))}buildTreeNode(t){return{key:t.id+"",label:t.folderName,data:t,type:"folder",children:t.children.map(e=>this.buildTreeNode(e))}}loadFiles(t){t.key&&(this.selectedNode=t,this.loadingService.show(),this.leftDrawerVisible.set(!1),this.#e.getFolderFiles({folderId:+t.key,skipCount:this.first(),maxResultCount:this.rows()}).subscribe({next:e=>{this.products.set(e.items||[]),this.totalRecords.set(e.totalCount||0),this.loadingService.hide()},error:e=>{console.error("\u83B7\u53D6\u6587\u7AE0\u8BE6\u60C5\u5931\u8D25:",e),this.loadingService.hide()}}))}onPageChange(t){this.first.set(t.first),this.rows.set(t.rows),this.loadFiles(this.selectedNode)}playVideo(t){this.playerService.playVideo(t)}folderDownload(t,e){t.stopPropagation(),console.log("Download",e)}ngOnDestroy(){this.subs.unsubscribe()}static{this.\u0275fac=function(e){return new(e||o)}}static{this.\u0275cmp=k({type:o,selectors:[["app-net-disk"]],decls:20,vars:16,consts:[["header",""],["body",""],["footer",""],["treeTemplate",""],[1,"flex","flex-1","flex-col","md:flex-row"],[3,"leftTemplate","showRightIcon","leftDrawerVisible"],[1,"p-6","flex-1","overflow-y-auto",2,"height","calc(100vh - 5rem)"],[1,"flex","items-center","gap-2"],["icon","pi pi-cloud-download",3,"onClick","label","outlined"],["icon","pi pi-play-circle",3,"onClick","label","outlined"],["size","small","stripedRows","",3,"selectionChange","value","selection"],[1,"flex","justify-end","mt-4"],[3,"onPageChange","rows","totalRecords","first"],[4,"ngTemplateOutlet"],[2,"width","2rem"],[3,"value"],["size","small","icon","pi pi-cloud-download",3,"outlined","appDownload","downloadName"],["size","small","icon","pi pi-play-circle",3,"onClick","outlined"],["selectionMode","single","virtualScrollItemSize","36",3,"selectionChange","onNodeSelect","value","styleClass","selection","virtualScroll"],["pTemplate","folder"],[1,"pi","pi-cloud-download","folder-download",3,"click"]],template:function(e,i){if(e&1){let l=v();n(0,"div",4),g(1,ue,1,3,"app-mobile-catagory-files-drawer",5),w(2,"async"),g(3,_e,1,1,"ng-container"),n(4,"div",6),w(5,"async"),n(6,"div",7)(7,"p-button",8),C("onClick",function(){return c(l),m(i.downloadService.batchDownloadAsZip(i.selectedProducts))}),a(),n(8,"p-button",9),C("onClick",function(){return c(l),m(i.playerService.multiPlayVideo(i.selectedProducts))}),a()(),n(9,"p-table",10),P("selectionChange",function(f){return c(l),N(i.selectedProducts,f)||(i.selectedProducts=f),m(f)}),g(10,ge,9,2,"ng-template",null,0,x)(12,he,15,13,"ng-template",null,1,x)(14,Ce,0,0,"ng-template",null,2,x),a(),n(16,"div",11)(17,"p-paginator",12),C("onPageChange",function(f){return c(l),m(i.onPageChange(f))}),a()()()(),g(18,we,3,6,"ng-template",null,3,x)}e&2&&(r(),R(S(2,12,i.mobileService.isMobile)?1:3),r(3),E("w-screen",S(5,14,i.mobileService.isMobile)),r(3),d("label",i.i18nService.translate("download"))("outlined",!0),r(),d("label",i.i18nService.translate("play"))("outlined",!0),r(),d("value",i.products()),F("selection",i.selectedProducts),r(8),d("rows",10)("totalRecords",i.totalRecords())("first",i.first()))},dependencies:[z,O,W,L,U,ne,oe,$,G,q,ce,se,de,pe,Y,Z,H,ee,ie],styles:["[_nghost-%COMP%]{flex:1;display:flex}[_nghost-%COMP%]     .p-virtualscroller{height:100%!important}"]})}};export{me as FolderDetailComponent};
