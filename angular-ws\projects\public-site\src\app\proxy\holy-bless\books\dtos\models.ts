import type { AuditedEntityDto } from '@abp/ng.core';

export interface ChapterTreeDto {
  id: number;
  eBookId: number;
  parentChapterId?: number;
  title?: string;
  content?: string;
  weight: number;
  views: number;
  isRoot: boolean;
  articleCount: number;
  children: ChapterTreeDto[];
}

export interface EBookDto extends AuditedEntityDto<number> {
  title?: string;
  description?: string;
  weight: number;
  channelId?: number;
  thumbnailFileId?: number;
  thumbnailUrl?: string;
  views: number;
  likes: number;
  languageCode?: string;
}
