import{a as j}from"./chunk-7GRECUP3.js";import{a as N}from"./chunk-IQKOEMCW.js";import{a as V}from"./chunk-KMQZ7S6W.js";import{A as L,z as H}from"./chunk-7BEB76UR.js";import{a as O,b as $}from"./chunk-HMYHKKL3.js";import{d as P}from"./chunk-C42ROIQ5.js";import"./chunk-EO3QMPVM.js";import"./chunk-GXIFCJEC.js";import{c as D,d as U}from"./chunk-P3PQXH52.js";import{Aa as z,ba as F}from"./chunk-TRW3QJR4.js";import"./chunk-BMA7WWEI.js";import{E as I,H as R,N as T,k as w}from"./chunk-O4TW5EFJ.js";import{$b as S,Db as E,Eb as y,Fb as l,Gb as r,Hb as p,Lb as B,Qb as u,Rb as c,S as f,Va as _,Xa as k,Za as s,a as d,ac as M,ea as n,ib as v,la as g,ma as h,ob as x,wb as a,za as C}from"./chunk-6TTFYGC3.js";import"./chunk-QWWW7GFA.js";var A=(i,t)=>t.id;function q(i,t){if(i&1&&p(0,"img",6),i&2){let e=c().$implicit;a("src",e.thumbnailUrl,_)("alt",e.title+" \u5C01\u9762\u56FE\u7247")}}function G(i,t){if(i&1&&(l(0,"div",7)(1,"h3",8),S(2),r()()),i&2){let e=c().$implicit;s(2),M(e.title)}}function J(i,t){if(i&1){let e=B();l(0,"div",9)(1,"p-button",10),u("click",function(){g(e);let b=c().$implicit,m=c();return h(m.openBook(b.id))}),r(),p(2,"p-button",11),r()}i&2&&(s(),a("outlined",!0),s(),a("outlined",!0))}function K(i,t){if(i&1&&(l(0,"p-card",2),x(1,q,1,2,"ng-template",3)(2,G,3,1,"ng-template",4)(3,J,3,2,"ng-template",5),r()),i&2){let e=t.$implicit;a("id","card-"+e.id)}}var W=class i{constructor(){this.route=n(I);this.#e=n(j);this.router=n(R);this.totalRecords=0;this.rows=10;this.first=0;this._isMobile=!1;this.collectionId=null;this.cardItems=C([]);this.channelIdContentCodeService=n(N);this.subs=new d;this.i18nService=n(H);this.#t=n(V);this.loadingService=n(L);this.checkMobile()}#e;#t;ngOnInit(){this.route.params.subscribe({next:t=>{let e=t.channelId;e&&(this.loadEBooks(e),this.changeLanguage(e))}})}changeLanguage(t){this.subs.unsubscribe(),this.subs=new d;let e=this.i18nService.language$.pipe(f(1)).subscribe(()=>{this.#t.getMatchedChannel(t).subscribe({next:o=>{if(!o){this.router.navigateByUrl("/landing");return}o.id!==t&&this.router.navigateByUrl(`/ebooks/ebook-card/${o.id}`)}})});this.subs.add(e)}loadEBooks(t){t&&(this.loadingService.show(),this.#e.getEBooksByChannelId(t).subscribe({next:e=>{this.cardItems.set(e),this.totalRecords=e.length,this.loadingService.hide()},error:e=>{console.error("\u83B7\u53D6\u7535\u5B50\u4E66\u6570\u636E\u5931\u8D25:",e),this.loadingService.hide()}}))}get isMobile(){return this._isMobile}get rowsPerPageOptions(){return this.isMobile?void 0:[10,20,50]}checkMobile(){this._isMobile=window.innerWidth<=768}openBook(t){t&&this.router.navigateByUrl(`/ebooks/book-detail/${t}`)}onPageChange(t){this.first=t.first,this.rows=t.rows,console.log("\u9875\u9762\u53D8\u5316:",t)}onResize(t){this.checkMobile()}ngOnDestroy(){this.subs.unsubscribe()}static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275cmp=v({type:i,selectors:[["app-ebook-card"]],hostBindings:function(e,o){e&1&&u("resize",function(m){return o.onResize(m)},!1,k)},decls:4,vars:0,consts:[[1,"flex","flex-col","p-6"],[1,"grid","grid-cols-2","md:grid-cols-3","lg:grid-cols-6","gap-6","mb-8"],["styleClass","card-item",3,"id"],["pTemplate","header"],["pTemplate","content"],["pTemplate","footer"],[1,"rounded-t-xl","w-56",3,"src","alt"],[1,"flex","cursor-pointer"],[1,"text-lg","font-semibold","mb-2"],[1,"flex","justify-between","items-center"],["size","small","label","\u9605\u8BFB","icon","pi pi-book",3,"click","outlined"],["size","small","label","\u542C\u4E66","icon","pi pi-bookmark",3,"outlined"]],template:function(e,o){e&1&&(l(0,"div",0)(1,"div",1),E(2,K,4,1,"p-card",2,A),r()()),e&2&&(s(2),y(o.cardItems()))},dependencies:[w,T,$,O,F,z,P,U,D],styles:["[_nghost-%COMP%]     p-card{display:flex}"]})}};export{W as EbookCardComponent};
