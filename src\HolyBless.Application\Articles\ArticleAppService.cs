using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using HolyBless.Articles.Dtos;
using HolyBless.Buckets;
using HolyBless.Domain.Interfaces;
using HolyBless.Entities.Articles;
using HolyBless.Entities.Books;
using HolyBless.Entities.Buckets;
using HolyBless.Entities.Collections;
using HolyBless.Entities.Tags;
using HolyBless.Permissions;
using HolyBless.Services;
using HolyBless.Tags.Dtos;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.Articles
{
    [Authorize]
    public class ArticleAppService : ReadOnlyArticleAppService, IArticleAppService
    {
        public ArticleAppService(
            IArticleRepository articleRepository,
            IRepository<Article, int> repository,
            IRepository<ArticleToTag> articleToTagRepository,
            IRepository<Tag, int> tagRepository,
            IRepository<TeacherArticleLink> teacherArticleLinkRepository,
            IRepository<CollectionToArticle> collectionToArticleRepository,
            IRepository<ChapterToArticle> chapterToArticleRepository,
            IRepository<ArticleFile, int> articleFileRepository,
            IRepository<BucketFile, int> bucketFileRepository,
            IRequestContextService requestContextService,
            ICachedFileUrlAppService cachedFileUrlAppService
            )
            : base(articleRepository, repository, articleToTagRepository, tagRepository, teacherArticleLinkRepository, collectionToArticleRepository, chapterToArticleRepository, requestContextService, cachedFileUrlAppService)
        {
        }

        [Authorize(HolyBlessPermissions.Articles.Create)]
        public async Task<ArticleDto> CreateAsync(CreateUpdateArticleDto input)
        {
            var article = ObjectMapper.Map<CreateUpdateArticleDto, Article>(input);
            article = await _repository.InsertAsync(article, autoSave: true);
            return ObjectMapper.Map<Article, ArticleDto>(article);
        }

        [Authorize(HolyBlessPermissions.Articles.Edit)]
        public async Task<ArticleDto> UpdateAsync(int id, CreateUpdateArticleDto input)
        {
            var article = await _repository.GetAsync(id);
            ObjectMapper.Map(input, article);
            article = await _repository.UpdateAsync(article, autoSave: true);
            return ObjectMapper.Map<Article, ArticleDto>(article);
        }

        [Authorize(HolyBlessPermissions.Articles.Delete)]
        public async Task DeleteAsync(int id)
        {
            await _repository.DeleteAsync(id);
            var articleTags = await _articleToTagRepository.GetListAsync(x => x.ArticleId == id);
            if (articleTags.Count > 0)
            {
                await _articleToTagRepository.DeleteManyAsync(articleTags);
            }
        }

        [Authorize(HolyBlessPermissions.Articles.Edit)]
        public async Task<List<ArticleToTagDto>> AddTagsAsync(int articleId, List<int> tagIds)
        {
            // Verify article exists
            var article = await _repository.GetAsync(articleId);
            if (article == null)
            {
                throw new EntityNotFoundException(typeof(Article), articleId);
            }

            // Verify all tags exist
            var existingTagIds = await _tagRepository.GetListAsync();
            var validTagIds = existingTagIds.Select(t => t.Id).ToList();
            var invalidTagIds = tagIds.Where(id => !validTagIds.Contains(id)).ToList();

            if (invalidTagIds.Any())
            {
                throw new UserFriendlyException(L["InvalidTagIds", string.Join(", ", invalidTagIds)]);
            }

            var articleTags = tagIds.Select(tagId => new ArticleToTag
            {
                ArticleId = articleId,
                TagId = tagId
            }).ToList();

            // Check for existing tags to avoid duplicates
            var queryable = await _articleToTagRepository.GetQueryableAsync();
            var existingTags = await queryable
                .Where(x => x.ArticleId == articleId && tagIds.Contains(x.TagId))
                .ToListAsync();

            // Filter out existing tags
            articleTags = articleTags
                .Where(at => !existingTags.Any(et => et.TagId == at.TagId))
                .ToList();

            if (articleTags.Any())
            {
                await _articleToTagRepository.InsertManyAsync(articleTags, autoSave: true);
            }

            return ObjectMapper.Map<List<ArticleToTag>, List<ArticleToTagDto>>(articleTags);
        }

        public async Task RemoveTagsFromArticle(int articleId, List<int> tagIds)
        {
            var articleTags = await _articleToTagRepository.GetListAsync(
                x => x.ArticleId == articleId && tagIds.Contains(x.TagId));

            await _articleToTagRepository.DeleteManyAsync(articleTags);
        }

        [Authorize(HolyBlessPermissions.Articles.Edit)]
        public async Task AddTeacherArticleLinks(int studentArticleId, List<TeacherArticleLinkDto> teacherLinks)
        {
            // Get all existing links for this student article
            var existingLinks = await _teacherArticleLinkRepository.GetListAsync(x => x.StudentArticleId == studentArticleId);
            foreach (var linkDto in teacherLinks)
            {
                var existing = existingLinks.FirstOrDefault(x => x.TeacherArticleId == linkDto.TeacherArticleId);
                if (existing == null)
                {
                    await _teacherArticleLinkRepository.InsertAsync(new TeacherArticleLink
                    {
                        StudentArticleId = studentArticleId,
                        TeacherArticleId = linkDto.TeacherArticleId,
                        Weight = linkDto.Weight
                    }, autoSave: true);
                }
                else
                {
                    existing.Weight = linkDto.Weight;
                    await _teacherArticleLinkRepository.UpdateAsync(existing, autoSave: true);
                }
            }
        }

        /// <summary>
        /// [Admin]
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        /// <exception cref="EntityNotFoundException"></exception>
        public async Task<ArticleDto> GetAsync(int id)
        {
            var queryable = await _repository.GetQueryableAsync();
            var article = await queryable
                .Include(x => x.ArticleToTags)
                .Include(x => x.ArticleFiles)
                    .ThenInclude(af => af.BucketFile)
                .FirstOrDefaultAsync(x => x.Id == id);

            if (article == null)
            {
                throw new EntityNotFoundException(typeof(Article), id);
            }

            var rt = ObjectMapper.Map<Article, ArticleDto>(article);
            await FillThumbnailUrl(rt);
            return rt;
        }

        /// <summary>
        /// [Admin] Paged List with optional search condition: LanguageCode
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<ArticleDto>> GetListAsync(ArticleAdminSearchDto input)
        {
            var queryable = await _repository.GetQueryableAsync();
            var query = queryable
                .Include(x => x.ArticleToTags)
                .WhereIf(!string.IsNullOrWhiteSpace(input.LanguageCode), x => x.LanguageCode == input.LanguageCode)
                // ArticleFiles are not included when retrieving a list to optimize performance
                .OrderBy(input.Sorting ?? "Title")
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount);

            var articles = await AsyncExecuter.ToListAsync(query);
            var totalCount = await AsyncExecuter.CountAsync(queryable);

            var rtList = ObjectMapper.Map<List<Article>, List<ArticleDto>>(articles);
            await FillThumbnailUrls(rtList);
            return new PagedResultDto<ArticleDto>(
                totalCount,
                rtList
            );
        }

        public async Task<List<TagDto>> GetTagsAsync(int articleId)
        {
            // Verify article exists
            var article = await _repository.GetAsync(articleId);
            if (article == null)
            {
                throw new EntityNotFoundException(typeof(Article), articleId);
            }

            // Get article tags using a join
            var articleTagsQueryable = await _articleToTagRepository.GetQueryableAsync();
            var tagsQueryable = await _tagRepository.GetQueryableAsync();

            var query = from articleTag in articleTagsQueryable
                        join tag in tagsQueryable on articleTag.TagId equals tag.Id
                        where articleTag.ArticleId == articleId
                        select tag;

            // Include ContentCode to properly map to TagDto
            query = query.Include(t => t.ContentCode);

            var tags = await AsyncExecuter.ToListAsync(query);

            return ObjectMapper.Map<List<Tag>, List<TagDto>>(tags);
        }

        public async Task<PagedResultDto<TeacherArticleLinkDto>> GetTeacherArticleLinksAsync(int studentArticleId, int skipCount, int maxResultCount, string? sorting = null)
        {
            var queryable = await _teacherArticleLinkRepository.GetQueryableAsync();
            var query = queryable
                .Where(x => x.StudentArticleId == studentArticleId)
                .OrderBy(sorting ?? nameof(TeacherArticleLink.Weight));
            var items = await AsyncExecuter.ToListAsync(
                query.Skip(skipCount).Take(maxResultCount)
            );
            var totalCount = await AsyncExecuter.CountAsync(query);
            var dtos = ObjectMapper.Map<List<TeacherArticleLink>, List<TeacherArticleLinkDto>>(items);
            return new PagedResultDto<TeacherArticleLinkDto>(totalCount, dtos);
        }

        [Authorize(HolyBlessPermissions.Articles.Edit)]
        public async Task LinkToCollection(int collectionId, int articleId)
        {
            var collectionToFile = await _collectionToArticleRepository.FirstOrDefaultAsync(x => x.CollectionId == collectionId && x.ArticleId == articleId);
            //Already linked, do nothing
            if (collectionToFile != null) return;

            var queryable = await _collectionToArticleRepository.GetQueryableAsync();
            var max = await queryable.Where(x => x.CollectionId == collectionId)
                .MaxAsync(x => (int?)x.Weight) ?? 0;
            collectionToFile = new CollectionToArticle
            {
                CollectionId = collectionId,
                ArticleId = articleId,
                Weight = max + 1
            };
            await _collectionToArticleRepository.InsertAsync(collectionToFile, autoSave: true);
        }

        [Authorize(HolyBlessPermissions.Articles.Edit)]
        public async Task LinkToCollection(int collectionId, List<int> articleIds)
        {
 
            // Get existing links to avoid duplicates
            var existingLinks = await _collectionToArticleRepository.GetListAsync(
                x => x.CollectionId == collectionId && articleIds.Contains(x.ArticleId));
            var existingArticleIdsInCollection = existingLinks.Select(x => x.ArticleId).ToList();

            // Filter out articles that are already linked
            var newArticleIds = articleIds.Where(id => !existingArticleIdsInCollection.Contains(id)).ToList();

            if (newArticleIds.Count == 0)
            {
                return; // All articles are already linked
            }

            // Get the current max weight for proper ordering
            var queryable = await _collectionToArticleRepository.GetQueryableAsync();
            var maxWeight = await queryable.Where(x => x.CollectionId == collectionId)
                .MaxAsync(x => (int?)x.Weight) ?? 0;

            // Create new collection-to-article links
            var newLinks = new List<CollectionToArticle>();
            for (int i = 0; i < newArticleIds.Count; i++)
            {
                newLinks.Add(new CollectionToArticle
                {
                    CollectionId = collectionId,
                    ArticleId = newArticleIds[i],
                    Weight = maxWeight + i + 1
                });
            }

            await _collectionToArticleRepository.InsertManyAsync(newLinks, autoSave: true);
        }

        [Authorize(HolyBlessPermissions.Articles.Edit)]
        public async Task UnlinkFromCollection(int collectionId, int articleId)
        {
            var collectionLink = await _collectionToArticleRepository.FirstOrDefaultAsync(x => x.CollectionId == collectionId &&  x.ArticleId == articleId);

            if (collectionLink != null)
            {
                await _collectionToArticleRepository.DeleteAsync(collectionLink, autoSave: true);
            }
        }

        [Authorize(HolyBlessPermissions.Articles.Edit)]
        public async Task UnlinkFromCollection(int collectionId, List<int> articleIds)
        {
            // Remove all collection links for these articles
            var collectionLinks = await _collectionToArticleRepository.GetListAsync(x => x.CollectionId == collectionId && articleIds.Contains(x.ArticleId));

            if (collectionLinks.Count > 0)
            {
                await _collectionToArticleRepository.DeleteManyAsync(collectionLinks, autoSave: true);
            }
        }
    }
}