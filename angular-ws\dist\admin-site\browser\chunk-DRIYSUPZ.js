import{lb as s}from"./chunk-GXYB24H2.js";import{a as t,aa as l,fa as o}from"./chunk-AKB6GSCA.js";var d=class i{constructor(e){this.restService=e;this.apiName="Default";this.create=(e,r)=>this.restService.request({method:"POST",url:"/api/app/virtual-folder",body:e},t({apiName:this.apiName},r));this.delete=(e,r)=>this.restService.request({method:"DELETE",url:`/api/app/virtual-folder/${e}`},t({apiName:this.apiName},r));this.get=(e,r)=>this.restService.request({method:"GET",url:`/api/app/virtual-folder/${e}`},t({apiName:this.apiName},r));this.getAllFolderFileUrlsByFolderId=(e,r)=>this.restService.request({method:"GET",url:`/api/app/virtual-folder/folder-file-urls/${e}`},t({apiName:this.apiName},r));this.getAllFolderFilesByFolderId=(e,r)=>this.restService.request({method:"GET",url:`/api/app/virtual-folder/folder-files/${e}`},t({apiName:this.apiName},r));this.getAllVirtualFolders=(e,r)=>this.restService.request({method:"GET",url:"/api/app/virtual-folder/virtual-folders",params:{languageCode:e}},t({apiName:this.apiName},r));this.getFolderFiles=(e,r)=>this.restService.request({method:"GET",url:"/api/app/virtual-folder/folder-files",params:{folderId:e.folderId,sorting:e.sorting,skipCount:e.skipCount,maxResultCount:e.maxResultCount}},t({apiName:this.apiName},r));this.getFolderTreeJsonByRootFolderId=(e,r)=>this.restService.request({method:"GET",responseType:"text",url:`/api/app/virtual-folder/folder-tree-json/${e}`},t({apiName:this.apiName},r));this.getList=(e,r)=>this.restService.request({method:"GET",url:"/api/app/virtual-folder",params:{channelId:e.channelId,sorting:e.sorting,skipCount:e.skipCount,maxResultCount:e.maxResultCount}},t({apiName:this.apiName},r));this.getVirtualFolderTree=(e,r)=>this.restService.request({method:"GET",url:`/api/app/virtual-folder/virtual-folder-tree/${e}`},t({apiName:this.apiName},r));this.linkToChannelByChannelIdAndVirtualFolderId=(e,r,a)=>this.restService.request({method:"POST",url:"/api/app/virtual-folder/link-to-channel",params:{channelId:e,virtualFolderId:r}},t({apiName:this.apiName},a));this.linkToChannelByChannelIdAndVirtualFolderIds=(e,r,a)=>this.restService.request({method:"POST",url:`/api/app/virtual-folder/link-to-channel/${e}`,body:r},t({apiName:this.apiName},a));this.moveVirtualFolder=(e,r,a,u)=>this.restService.request({method:"POST",url:"/api/app/virtual-folder/move-virtual-folder",params:{folderId:e,toParentId:r,beforeId:a}},t({apiName:this.apiName},u));this.unlinkFromChannelByVirtualFolderId=(e,r)=>this.restService.request({method:"POST",url:`/api/app/virtual-folder/unlink-from-channel/${e}`},t({apiName:this.apiName},r));this.unlinkFromChannelByVirtualFolderIds=(e,r)=>this.restService.request({method:"POST",url:"/api/app/virtual-folder/unlink-from-channel",body:e},t({apiName:this.apiName},r));this.update=(e,r,a)=>this.restService.request({method:"PUT",url:`/api/app/virtual-folder/${e}`,body:r},t({apiName:this.apiName},a))}static{this.\u0275fac=function(r){return new(r||i)(o(s))}}static{this.\u0275prov=l({token:i,factory:i.\u0275fac,providedIn:"root"})}};export{d as a};
