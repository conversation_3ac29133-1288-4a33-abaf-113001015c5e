﻿<Project Sdk="Microsoft.NET.Sdk">
  <Import Project="..\..\common.props" />
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Serilog.Extensions.Logging" Version="9.0.1" />
    <PackageReference Include="Serilog.Sinks.Async" Version="2.1.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.4" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Volo.Abp.Autofac" Version="9.2.3" />
    <ProjectReference Include="..\HolyBless.EntityFrameworkCore\HolyBless.EntityFrameworkCore.csproj" />
    <ProjectReference Include="..\HolyBless.Application.Contracts\HolyBless.Application.Contracts.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="Logs\**" />
    <Content Remove="Logs\**" />
    <EmbeddedResource Remove="Logs\**" />
    <None Remove="Logs\**" />
  </ItemGroup>
  <ItemGroup>
    <None Remove="appsettings.json" />
    <Content Include="appsettings.json">
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <None Remove="appsettings.secrets.json" />
    <Content Include="appsettings.secrets.json">
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
	<ItemGroup>
		<None Update="appsettings.DEV.json">
		  <DependentUpon>appsettings.json</DependentUpon>
		</None>
		<None Update="appsettings.DEV.Admin.json">
			<DependentUpon>appsettings.DEV.json</DependentUpon>
		</None>
		<None Update="appsettings.Development.json">
		  <DependentUpon>appsettings.json</DependentUpon>
		</None>
		<None Update="appsettings.QA.json">
			<DependentUpon>appsettings.json</DependentUpon>
		</None>
	</ItemGroup>

</Project>