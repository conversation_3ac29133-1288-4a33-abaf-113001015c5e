import type { AuditedEntityDto, PagedAndSortedResultRequestDto } from '@abp/ng.core';
import type { MediaType } from '../../enums/media-type.enum';

export interface VirtualFolderDto extends AuditedEntityDto<number> {
  parentFolderId?: number;
  folderName?: string;
  languageCode?: string;
  spokenLangCode?: string;
  views: number;
  weight: number;
  channelId?: number;
}

export interface VirtualFolderFileDto {
  fileId: number;
  fileName?: string;
  title?: string;
  fileUrl?: string;
  mediaType?: MediaType;
  lastModificationTime?: string;
}

export interface VirtualFolderFileSearchDto extends PagedAndSortedResultRequestDto {
  folderId?: number;
}

export interface VirtualFolderTreeDto {
  id: number;
  folderName?: string;
  parentFolderId?: number;
  views: number;
  weight: number;
  channelId?: number;
  isRoot: boolean;
  children: VirtualFolderTreeDto[];
}
