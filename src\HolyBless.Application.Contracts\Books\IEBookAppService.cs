﻿using HolyBless.Books.Dtos;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace HolyBless.Books;

public interface IEBookAppService : IReadOnlyEBookAppService
{
    Task<EBookDto> CreateAsync(CreateUpdateEBookDto input);
    Task<EBookDto> GetAsync(int id);
    Task<EBookDto> UpdateAsync(int id, CreateUpdateEBookDto input);
    Task DeleteAsync(int id);
    Task<PagedResultDto<EBookDto>> GetListAsync(PagedAndSortedResultRequestDto input);
   Task<List<EBookDto>> GetAllEBooksAsync(string? languageCode = null);

    Task LinkToChannel(int channelId, int eBookId);

    Task LinkToChannel(int channelId, List<int> eBookIds);

    Task UnlinkFromChannel(int eBookId);

    Task UnlinkFromChannel(List<int> eBookIds);
}