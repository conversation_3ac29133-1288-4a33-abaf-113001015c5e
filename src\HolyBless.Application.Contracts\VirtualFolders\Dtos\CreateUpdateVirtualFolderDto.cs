using System.ComponentModel.DataAnnotations;

namespace HolyBless.VirtualFolders
{
    public class CreateUpdateVirtualFolderDto
    {
        public int? ParentFolderId { get; set; }
        
        [Required]
        [StringLength(200)]
        public string FolderName { get; set; } = default!;
        
        [StringLength(10)]
        public string? LanguageCode { get; set; }
        
        [StringLength(10)]
        public string? SpokenLangCode { get; set; }
        
        public int Views { get; set; } = 0;
        public int Weight { get; set; } = 0;
        public int? ChannelId { get; set; }
    }
}