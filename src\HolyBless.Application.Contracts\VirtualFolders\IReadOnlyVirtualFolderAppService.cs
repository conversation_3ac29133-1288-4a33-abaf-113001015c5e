using HolyBless.VirtualFolders.Dtos;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace HolyBless.VirtualFolders
{
    public interface IReadOnlyVirtualFolderAppService : IApplicationService
    {
        Task<PagedResultDto<VirtualFolderDto>> GetListAsync(VirtualFolderSearchDto input);

        Task<PagedResultDto<VirtualFolderFileDto>> GetFolderFilesAsync(VirtualFolderFileSearchDto input);

        Task<List<string>> GetAllFolderFileUrls(int folderId);

        Task<List<VirtualFolderFileDto>> GetAllFolderFiles(int folderId);

        Task<List<VirtualFolderTreeDto>> GetVirtualFolderTreeAsync(int folderId);

    }
}