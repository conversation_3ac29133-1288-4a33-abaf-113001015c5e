import{lb as n}from"./chunk-GXYB24H2.js";import{a,aa as r,fa as s}from"./chunk-AKB6GSCA.js";var p=class i{constructor(e){this.restService=e;this.apiName="Default";this.create=(e,t)=>this.restService.request({method:"POST",url:"/api/app/e-book",body:e},a({apiName:this.apiName},t));this.delete=(e,t)=>this.restService.request({method:"DELETE",url:`/api/app/e-book/${e}`},a({apiName:this.apiName},t));this.get=(e,t)=>this.restService.request({method:"GET",url:`/api/app/e-book/${e}`},a({apiName:this.apiName},t));this.getAllEBooks=(e,t)=>this.restService.request({method:"GET",url:"/api/app/e-book/e-books",params:{languageCode:e}},a({apiName:this.apiName},t));this.getChapterTreeByEBookId=(e,t)=>this.restService.request({method:"GET",url:`/api/app/e-book/chapter-tree-by-eBook-id/${e}`},a({apiName:this.apiName},t));this.getEBooksByChannelId=(e,t)=>this.restService.request({method:"GET",url:`/api/app/e-book/e-books-by-channel-id/${e}`},a({apiName:this.apiName},t));this.getList=(e,t)=>this.restService.request({method:"GET",url:"/api/app/e-book",params:{sorting:e.sorting,skipCount:e.skipCount,maxResultCount:e.maxResultCount}},a({apiName:this.apiName},t));this.linkToChannelByChannelIdAndEBookId=(e,t,o)=>this.restService.request({method:"POST",url:"/api/app/e-book/link-to-channel",params:{channelId:e,eBookId:t}},a({apiName:this.apiName},o));this.linkToChannelByChannelIdAndEBookIds=(e,t,o)=>this.restService.request({method:"POST",url:`/api/app/e-book/link-to-channel/${e}`,body:t},a({apiName:this.apiName},o));this.unlinkFromChannelByEBookId=(e,t)=>this.restService.request({method:"POST",url:`/api/app/e-book/unlink-from-channel/${e}`},a({apiName:this.apiName},t));this.unlinkFromChannelByEBookIds=(e,t)=>this.restService.request({method:"POST",url:"/api/app/e-book/unlink-from-channel",body:e},a({apiName:this.apiName},t));this.update=(e,t,o)=>this.restService.request({method:"PUT",url:`/api/app/e-book/${e}`,body:t},a({apiName:this.apiName},o))}static{this.\u0275fac=function(t){return new(t||i)(s(n))}}static{this.\u0275prov=r({token:i,factory:i.\u0275fac,providedIn:"root"})}};export{p as a};
