import{a as ln}from"./chunk-F6EN6KXA.js";import{a as on}from"./chunk-MMY5JCBC.js";import{a as nt,b as en,c as tn}from"./chunk-UG6YMQF5.js";import"./chunk-KDA4DUCX.js";import{a as nn,b as ko}from"./chunk-OJMUFUAX.js";import{a as rn}from"./chunk-HVFBHGMG.js";import{a as sn}from"./chunk-IQKOEMCW.js";import{a as an}from"./chunk-KMQZ7S6W.js";import{b as go}from"./chunk-2NYX2BCD.js";import{a as Ji}from"./chunk-C2WU3NNP.js";import{A as yo,a as Ai,b as Ri,c as Hi,d as Ut,e as zi,f as $i,g as Ni,h as Ui,i as Vt,j as ji,l as bo,m as _o,n as tr,o as Ki,q as Wi,r as Qi,s as qi,t as Zi,u as Gi,v as vo,w as or,x as Yi,y as Xi,z as Ge}from"./chunk-7BEB76UR.js";import{a as Mi,b as Oi,c as Wo,d as Pi,e as Jo,f as it,g as Ze}from"./chunk-EO3QMPVM.js";import{a as Qo,b as Zo,c as Fi,d as Li,e as Yo,f as Xo,g as er}from"./chunk-GXIFCJEC.js";import{a as fo,b as Bt,c as ho,d as Dt}from"./chunk-P3PQXH52.js";import{Aa as Bi,Ca as Di,Da as mo,Ea as Vi,F as yi,G as Nt,I as ki,K as ut,L as wi,M as De,N as Tt,O as Ci,Q as Et,R as jo,U as co,W as Mt,Z as xi,_ as Ii,b as No,ba as Ve,c as ui,ca as z,d as Uo,da as uo,e as lo,fa as Ko,g as pi,ga as Ce,h as mi,ha as Si,i as fi,ia as Ti,ja as ue,ka as Ei,l as hi,la as qo,m as gi,na as Go,p as $t,q as Me,qa as qe,r as te,sa as Ot,t as bi,u as _i,ua as po,x as vi}from"./chunk-TRW3QJR4.js";import{c as zt,d as ye,f as ke,g as Ae,h as we}from"./chunk-BMA7WWEI.js";import{B as ni,D as ai,F as si,H as dt,I as ao,J as so,K as li,L as ci,M as di,N as St,a as wt,c as oe,d as Ct,e as fe,f as le,g as Ee,h as xt,k as R,l as he,n as ct,o as ei,p as ti,q as Qe,r as It,v as oi,w as no,x as ri,y as ii}from"./chunk-O4TW5EFJ.js";import{$ as ie,$b as A,A as Dr,Ab as W,B as mt,Bb as Be,C as $,Cb as Ue,D as Vr,Da as oo,Db as je,Dc as T,E as ft,Ea as Hr,Eb as Ke,Ec as G,Fb as u,G as Fr,Gb as p,Gc as ee,H as Lr,Ha as zr,Hb as w,Hc as rt,Ib as Q,J as Se,Jb as q,Kb as U,L as Jt,Lb as E,N as Pr,O as eo,Qb as v,Rb as l,Sb as kt,Tb as lt,U as Te,Ua as vt,Va as st,W as Fe,Wb as Z,X as tt,Xb as H,Y as Ar,Ya as ro,Yb as I,Z as ht,Za as d,Zb as S,_ as B,_b as X,a as xr,ab as $r,ac as _e,b as Ir,ba as gt,bb as Nr,bc as me,c as Yt,cb as pe,cc as Kr,da as N,db as Ur,dc as Wr,ea as y,ec as Qr,f as Xe,fa as ot,fc as qr,gc as ve,hc as We,i as Sr,ib as F,ic as O,j as et,jb as ne,jc as J,k as be,kc as Zr,l as Xt,la as h,lb as jr,lc as Gr,ma as g,mb as ae,na as to,o as Tr,oa as j,ob as f,q as Er,qa as bt,qc as Le,r as Ie,rc as Pe,s as Mr,sa as Rr,sb as yt,t as Or,ua as V,ub as zo,uc as de,va as $e,vb as k,wb as s,wc as Yr,xb as Y,xc as Xr,ya as _t,yb as io,yc as $o,z as Br,za as D,zb as Ne,zc as Jr}from"./chunk-6TTFYGC3.js";import{a as se,b as ze,g as Ho,h as xe}from"./chunk-QWWW7GFA.js";var rr="Service workers are disabled or not supported by this browser",Ft=class{serviceWorker;worker;registration;events;constructor(i,e){if(this.serviceWorker=i,!i)this.worker=this.events=this.registration=new Yt(o=>o.error(new tt(5601,!1)));else{let o=null,r=new Xe;this.worker=new Yt(_=>(o!==null&&_.next(o),r.subscribe(x=>_.next(x))));let n=()=>{let{controller:_}=i;_!==null&&(o=_,r.next(o))};i.addEventListener("controllerchange",n),n(),this.registration=this.worker.pipe(Te(()=>i.getRegistration()));let a=new Xe;this.events=a.asObservable();let c=_=>{let{data:x}=_;x?.type&&a.next(x)};i.addEventListener("message",c),e?.get(zo,null,{optional:!0})?.onDestroy(()=>{i.removeEventListener("controllerchange",n),i.removeEventListener("message",c)})}}postMessage(i,e){return new Promise(o=>{this.worker.pipe(Se(1)).subscribe(r=>{r.postMessage(se({action:i},e)),o()})})}postMessageWithOperation(i,e,o){let r=this.waitForOperationCompleted(o),n=this.postMessage(i,e);return Promise.all([n,r]).then(([,a])=>a)}generateNonce(){return Math.round(Math.random()*1e7)}eventsOfType(i){let e;return typeof i=="string"?e=o=>o.type===i:e=o=>i.includes(o.type),this.events.pipe($(e))}nextEventOfType(i){return this.eventsOfType(i).pipe(Se(1))}waitForOperationCompleted(i){return new Promise((e,o)=>{this.eventsOfType("OPERATION_COMPLETED").pipe($(r=>r.nonce===i),Se(1),Ie(r=>{if(r.result!==void 0)return r.result;throw new Error(r.error)})).subscribe({next:e,error:o})})}get isEnabled(){return!!this.serviceWorker}},Ps=(()=>{class t{sw;messages;notificationClicks;subscription;get isEnabled(){return this.sw.isEnabled}pushManager=null;subscriptionChanges=new Xe;constructor(e){if(this.sw=e,!e.isEnabled){this.messages=mt,this.notificationClicks=mt,this.subscription=mt;return}this.messages=this.sw.eventsOfType("PUSH").pipe(Ie(r=>r.data)),this.notificationClicks=this.sw.eventsOfType("NOTIFICATION_CLICK").pipe(Ie(r=>r.data)),this.pushManager=this.sw.registration.pipe(Ie(r=>r.pushManager));let o=this.pushManager.pipe(Te(r=>r.getSubscription()));this.subscription=new Yt(r=>{let n=o.subscribe(r),a=this.subscriptionChanges.subscribe(r);return()=>{n.unsubscribe(),a.unsubscribe()}})}requestSubscription(e){if(!this.sw.isEnabled||this.pushManager===null)return Promise.reject(new Error(rr));let o={userVisibleOnly:!0},r=this.decodeBase64(e.serverPublicKey.replace(/_/g,"/").replace(/-/g,"+")),n=new Uint8Array(new ArrayBuffer(r.length));for(let a=0;a<r.length;a++)n[a]=r.charCodeAt(a);return o.applicationServerKey=n,new Promise((a,c)=>{this.pushManager.pipe(Te(m=>m.subscribe(o)),Se(1)).subscribe({next:m=>{this.subscriptionChanges.next(m),a(m)},error:c})})}unsubscribe(){if(!this.sw.isEnabled)return Promise.reject(new Error(rr));let e=o=>{if(o===null)throw new tt(5602,!1);return o.unsubscribe().then(r=>{if(!r)throw new tt(5603,!1);this.subscriptionChanges.next(null)})};return new Promise((o,r)=>{this.subscription.pipe(Se(1),Te(e)).subscribe({next:o,error:r})})}decodeBase64(e){return atob(e)}static \u0275fac=function(o){return new(o||t)(N(Ft))};static \u0275prov=B({token:t,factory:t.\u0275fac})}return t})(),As=(()=>{class t{sw;versionUpdates;unrecoverable;get isEnabled(){return this.sw.isEnabled}constructor(e){if(this.sw=e,!e.isEnabled){this.versionUpdates=mt,this.unrecoverable=mt;return}this.versionUpdates=this.sw.eventsOfType(["VERSION_DETECTED","VERSION_INSTALLATION_FAILED","VERSION_READY","NO_NEW_VERSION_DETECTED"]),this.unrecoverable=this.sw.eventsOfType("UNRECOVERABLE_STATE")}checkForUpdate(){if(!this.sw.isEnabled)return Promise.reject(new Error(rr));let e=this.sw.generateNonce();return this.sw.postMessageWithOperation("CHECK_FOR_UPDATES",{nonce:e},e)}activateUpdate(){if(!this.sw.isEnabled)return Promise.reject(new tt(5601,!1));let e=this.sw.generateNonce();return this.sw.postMessageWithOperation("ACTIVATE_UPDATE",{nonce:e},e)}static \u0275fac=function(o){return new(o||t)(N(Ft))};static \u0275prov=B({token:t,factory:t.\u0275fac})}return t})();var dn=new gt("");function Rs(){let t=y(jt);if(!("serviceWorker"in navigator&&t.enabled!==!1))return;let i=y(dn),e=y($e),o=y(zo);e.runOutsideAngular(()=>{let r=navigator.serviceWorker,n=()=>r.controller?.postMessage({action:"INITIALIZE"});r.addEventListener("controllerchange",n),o.onDestroy(()=>{r.removeEventListener("controllerchange",n)})}),e.runOutsideAngular(()=>{let r,{registrationStrategy:n}=t;if(typeof n=="function")r=new Promise(a=>n().subscribe(()=>a()));else{let[a,...c]=(n||"registerWhenStable:30000").split(":");switch(a){case"registerImmediately":r=Promise.resolve();break;case"registerWithDelay":r=cn(+c[0]||0);break;case"registerWhenStable":r=Promise.race([o.whenStable(),cn(+c[0])]);break;default:throw new tt(5600,!1)}}r.then(()=>{o.destroyed||navigator.serviceWorker.register(i,{scope:t.scope}).catch(a=>console.error(Ar(5604,!1)))})})}function cn(t){return new Promise(i=>setTimeout(i,t))}function Hs(t,i){return new Ft(t.enabled!==!1?navigator.serviceWorker:void 0,i)}var jt=class{enabled;scope;registrationStrategy};function ir(t,i={}){return ot([Ps,As,{provide:dn,useValue:t},{provide:jt,useValue:i},{provide:Ft,useFactory:Hs,deps:[jt,bt]},yt(Rs)])}var zs="@",$s=(()=>{class t{doc;delegate;zone;animationType;moduleImpl;_rendererFactoryPromise=null;scheduler=null;injector=y(bt);loadingSchedulerFn=y(Ns,{optional:!0});_engine;constructor(e,o,r,n,a){this.doc=e,this.delegate=o,this.zone=r,this.animationType=n,this.moduleImpl=a}ngOnDestroy(){this._engine?.flush()}loadImpl(){let e=()=>this.moduleImpl??import("./chunk-22CJKMVJ.js").then(r=>r),o;return this.loadingSchedulerFn?o=this.loadingSchedulerFn(e):o=e(),o.catch(r=>{throw new tt(5300,!1)}).then(({\u0275createEngine:r,\u0275AnimationRendererFactory:n})=>{this._engine=r(this.animationType,this.doc);let a=new n(this.delegate,this._engine,this.zone);return this.delegate=a,a})}createRenderer(e,o){let r=this.delegate.createRenderer(e,o);if(r.\u0275type===0)return r;typeof r.throwOnSyntheticProps=="boolean"&&(r.throwOnSyntheticProps=!1);let n=new nr(r);return o?.data?.animation&&!this._rendererFactoryPromise&&(this._rendererFactoryPromise=this.loadImpl()),this._rendererFactoryPromise?.then(a=>{let c=a.createRenderer(e,o);n.use(c),this.scheduler??=this.injector.get(Rr,null,{optional:!0}),this.scheduler?.notify(10)}).catch(a=>{n.use(r)}),n}begin(){this.delegate.begin?.()}end(){this.delegate.end?.()}whenRenderingDone(){return this.delegate.whenRenderingDone?.()??Promise.resolve()}componentReplaced(e){this._engine?.flush(),this.delegate.componentReplaced?.(e)}static \u0275fac=function(o){Ur()};static \u0275prov=B({token:t,factory:t.\u0275fac})}return t})(),nr=class{delegate;replay=[];\u0275type=1;constructor(i){this.delegate=i}use(i){if(this.delegate=i,this.replay!==null){for(let e of this.replay)e(i);this.replay=null}}get data(){return this.delegate.data}destroy(){this.replay=null,this.delegate.destroy()}createElement(i,e){return this.delegate.createElement(i,e)}createComment(i){return this.delegate.createComment(i)}createText(i){return this.delegate.createText(i)}get destroyNode(){return this.delegate.destroyNode}appendChild(i,e){this.delegate.appendChild(i,e)}insertBefore(i,e,o,r){this.delegate.insertBefore(i,e,o,r)}removeChild(i,e,o){this.delegate.removeChild(i,e,o)}selectRootElement(i,e){return this.delegate.selectRootElement(i,e)}parentNode(i){return this.delegate.parentNode(i)}nextSibling(i){return this.delegate.nextSibling(i)}setAttribute(i,e,o,r){this.delegate.setAttribute(i,e,o,r)}removeAttribute(i,e,o){this.delegate.removeAttribute(i,e,o)}addClass(i,e){this.delegate.addClass(i,e)}removeClass(i,e){this.delegate.removeClass(i,e)}setStyle(i,e,o,r){this.delegate.setStyle(i,e,o,r)}removeStyle(i,e,o){this.delegate.removeStyle(i,e,o)}setProperty(i,e,o){this.shouldReplay(e)&&this.replay.push(r=>r.setProperty(i,e,o)),this.delegate.setProperty(i,e,o)}setValue(i,e){this.delegate.setValue(i,e)}listen(i,e,o,r){return this.shouldReplay(e)&&this.replay.push(n=>n.listen(i,e,o,r)),this.delegate.listen(i,e,o,r)}shouldReplay(i){return this.replay!==null&&i.startsWith(zs)}},Ns=new gt("");function un(t="animations"){return zr("NgAsyncAnimations"),ot([{provide:$r,useFactory:(i,e,o)=>new $s(i,e,o,t),deps:[wt,ei,$e]},{provide:Hr,useValue:t==="noop"?"NoopAnimations":"BrowserAnimations"}])}var pn=[{path:"",redirectTo:"/landing",pathMatch:"full"},{path:"landing",loadComponent:()=>import("./chunk-XWJI3K3S.js").then(t=>t.LandingComponent),title:"HolyBless - \u6B22\u8FCE\u9875"},{path:"home",loadChildren:()=>import("./chunk-B3C7MD66.js").then(t=>t.HOME_ROUTES),title:"HolyBless - \u4E3B\u7AD9"},{path:"ebooks",loadChildren:()=>import("./chunk-T2PK7ZFD.js").then(t=>t.EBOOKS_ROUTES),title:"HolyBless - \u7535\u5B50\u4E66"},{path:"virtual-folder",loadChildren:()=>import("./chunk-3OFTZ254.js").then(t=>t.VIRTUAL_FOLDER_ROUTES),title:"HolyBless - \u7F51\u76D8"},{path:"podcast",loadChildren:()=>import("./chunk-E45STWQ5.js").then(t=>t.PODCAST_ROUTES),title:"HolyBless - \u64AD\u5BA2"},{path:"search",loadChildren:()=>import("./chunk-MOLVTGG7.js").then(t=>t.SEARCH_ROUTES),title:"HolyBless - \u641C\u7D22"},{path:"help",loadChildren:()=>import("./chunk-SJDMJSHK.js").then(t=>t.HELP_ROUTES),title:"HolyBless - \u5E2E\u52A9\u4E2D\u5FC3"},{path:"**",loadComponent:()=>import("./chunk-A4RPYPS2.js").then(t=>t.NotFoundComponent),title:"HolyBless - \u9875\u9762\u672A\u627E\u5230"}];var mn={root:{transitionDuration:"{transition.duration}"},panel:{borderWidth:"0 0 1px 0",borderColor:"{content.border.color}"},header:{color:"{text.muted.color}",hoverColor:"{text.color}",activeColor:"{text.color}",padding:"1.125rem",fontWeight:"600",borderRadius:"0",borderWidth:"0",borderColor:"{content.border.color}",background:"{content.background}",hoverBackground:"{content.background}",activeBackground:"{content.background}",activeHoverBackground:"{content.background}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"},toggleIcon:{color:"{text.muted.color}",hoverColor:"{text.color}",activeColor:"{text.color}",activeHoverColor:"{text.color}"},first:{topBorderRadius:"{content.border.radius}",borderWidth:"0"},last:{bottomBorderRadius:"{content.border.radius}",activeBottomBorderRadius:"0"}},content:{borderWidth:"0",borderColor:"{content.border.color}",background:"{content.background}",color:"{text.color}",padding:"0 1.125rem 1.125rem 1.125rem"}};var fn={root:{background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledHoverBackground:"{form.field.filled.hover.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",invalidPlaceholderColor:"{form.field.invalid.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}"},overlay:{background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}"},list:{padding:"{list.padding}",gap:"{list.gap}"},option:{focusBackground:"{list.option.focus.background}",selectedBackground:"{list.option.selected.background}",selectedFocusBackground:"{list.option.selected.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",selectedColor:"{list.option.selected.color}",selectedFocusColor:"{list.option.selected.focus.color}",padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}"},optionGroup:{background:"{list.option.group.background}",color:"{list.option.group.color}",fontWeight:"{list.option.group.font.weight}",padding:"{list.option.group.padding}"},dropdown:{width:"2.5rem",sm:{width:"2rem"},lg:{width:"3rem"},borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.border.color}",activeBorderColor:"{form.field.border.color}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},chip:{borderRadius:"{border.radius.sm}"},emptyMessage:{padding:"{list.option.padding}"},colorScheme:{light:{chip:{focusBackground:"{surface.200}",focusColor:"{surface.800}"},dropdown:{background:"{surface.100}",hoverBackground:"{surface.200}",activeBackground:"{surface.300}",color:"{surface.600}",hoverColor:"{surface.700}",activeColor:"{surface.800}"}},dark:{chip:{focusBackground:"{surface.700}",focusColor:"{surface.0}"},dropdown:{background:"{surface.800}",hoverBackground:"{surface.700}",activeBackground:"{surface.600}",color:"{surface.300}",hoverColor:"{surface.200}",activeColor:"{surface.100}"}}}};var hn={root:{width:"2rem",height:"2rem",fontSize:"1rem",background:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}"},icon:{size:"1rem"},group:{borderColor:"{content.background}",offset:"-0.75rem"},lg:{width:"3rem",height:"3rem",fontSize:"1.5rem",icon:{size:"1.5rem"},group:{offset:"-1rem"}},xl:{width:"4rem",height:"4rem",fontSize:"2rem",icon:{size:"2rem"},group:{offset:"-1.5rem"}}};var gn={root:{borderRadius:"{border.radius.md}",padding:"0 0.5rem",fontSize:"0.75rem",fontWeight:"700",minWidth:"1.5rem",height:"1.5rem"},dot:{size:"0.5rem"},sm:{fontSize:"0.625rem",minWidth:"1.25rem",height:"1.25rem"},lg:{fontSize:"0.875rem",minWidth:"1.75rem",height:"1.75rem"},xl:{fontSize:"1rem",minWidth:"2rem",height:"2rem"},colorScheme:{light:{primary:{background:"{primary.color}",color:"{primary.contrast.color}"},secondary:{background:"{surface.100}",color:"{surface.600}"},success:{background:"{green.500}",color:"{surface.0}"},info:{background:"{sky.500}",color:"{surface.0}"},warn:{background:"{orange.500}",color:"{surface.0}"},danger:{background:"{red.500}",color:"{surface.0}"},contrast:{background:"{surface.950}",color:"{surface.0}"}},dark:{primary:{background:"{primary.color}",color:"{primary.contrast.color}"},secondary:{background:"{surface.800}",color:"{surface.300}"},success:{background:"{green.400}",color:"{green.950}"},info:{background:"{sky.400}",color:"{sky.950}"},warn:{background:"{orange.400}",color:"{orange.950}"},danger:{background:"{red.400}",color:"{red.950}"},contrast:{background:"{surface.0}",color:"{surface.950}"}}}};var bn={primitive:{borderRadius:{none:"0",xs:"2px",sm:"4px",md:"6px",lg:"8px",xl:"12px"},emerald:{50:"#ecfdf5",100:"#d1fae5",200:"#a7f3d0",300:"#6ee7b7",400:"#34d399",500:"#10b981",600:"#059669",700:"#047857",800:"#065f46",900:"#064e3b",950:"#022c22"},green:{50:"#f0fdf4",100:"#dcfce7",200:"#bbf7d0",300:"#86efac",400:"#4ade80",500:"#22c55e",600:"#16a34a",700:"#15803d",800:"#166534",900:"#14532d",950:"#052e16"},lime:{50:"#f7fee7",100:"#ecfccb",200:"#d9f99d",300:"#bef264",400:"#a3e635",500:"#84cc16",600:"#65a30d",700:"#4d7c0f",800:"#3f6212",900:"#365314",950:"#1a2e05"},red:{50:"#fef2f2",100:"#fee2e2",200:"#fecaca",300:"#fca5a5",400:"#f87171",500:"#ef4444",600:"#dc2626",700:"#b91c1c",800:"#991b1b",900:"#7f1d1d",950:"#450a0a"},orange:{50:"#fff7ed",100:"#ffedd5",200:"#fed7aa",300:"#fdba74",400:"#fb923c",500:"#f97316",600:"#ea580c",700:"#c2410c",800:"#9a3412",900:"#7c2d12",950:"#431407"},amber:{50:"#fffbeb",100:"#fef3c7",200:"#fde68a",300:"#fcd34d",400:"#fbbf24",500:"#f59e0b",600:"#d97706",700:"#b45309",800:"#92400e",900:"#78350f",950:"#451a03"},yellow:{50:"#fefce8",100:"#fef9c3",200:"#fef08a",300:"#fde047",400:"#facc15",500:"#eab308",600:"#ca8a04",700:"#a16207",800:"#854d0e",900:"#713f12",950:"#422006"},teal:{50:"#f0fdfa",100:"#ccfbf1",200:"#99f6e4",300:"#5eead4",400:"#2dd4bf",500:"#14b8a6",600:"#0d9488",700:"#0f766e",800:"#115e59",900:"#134e4a",950:"#042f2e"},cyan:{50:"#ecfeff",100:"#cffafe",200:"#a5f3fc",300:"#67e8f9",400:"#22d3ee",500:"#06b6d4",600:"#0891b2",700:"#0e7490",800:"#155e75",900:"#164e63",950:"#083344"},sky:{50:"#f0f9ff",100:"#e0f2fe",200:"#bae6fd",300:"#7dd3fc",400:"#38bdf8",500:"#0ea5e9",600:"#0284c7",700:"#0369a1",800:"#075985",900:"#0c4a6e",950:"#082f49"},blue:{50:"#eff6ff",100:"#dbeafe",200:"#bfdbfe",300:"#93c5fd",400:"#60a5fa",500:"#3b82f6",600:"#2563eb",700:"#1d4ed8",800:"#1e40af",900:"#1e3a8a",950:"#172554"},indigo:{50:"#eef2ff",100:"#e0e7ff",200:"#c7d2fe",300:"#a5b4fc",400:"#818cf8",500:"#6366f1",600:"#4f46e5",700:"#4338ca",800:"#3730a3",900:"#312e81",950:"#1e1b4b"},violet:{50:"#f5f3ff",100:"#ede9fe",200:"#ddd6fe",300:"#c4b5fd",400:"#a78bfa",500:"#8b5cf6",600:"#7c3aed",700:"#6d28d9",800:"#5b21b6",900:"#4c1d95",950:"#2e1065"},purple:{50:"#faf5ff",100:"#f3e8ff",200:"#e9d5ff",300:"#d8b4fe",400:"#c084fc",500:"#a855f7",600:"#9333ea",700:"#7e22ce",800:"#6b21a8",900:"#581c87",950:"#3b0764"},fuchsia:{50:"#fdf4ff",100:"#fae8ff",200:"#f5d0fe",300:"#f0abfc",400:"#e879f9",500:"#d946ef",600:"#c026d3",700:"#a21caf",800:"#86198f",900:"#701a75",950:"#4a044e"},pink:{50:"#fdf2f8",100:"#fce7f3",200:"#fbcfe8",300:"#f9a8d4",400:"#f472b6",500:"#ec4899",600:"#db2777",700:"#be185d",800:"#9d174d",900:"#831843",950:"#500724"},rose:{50:"#fff1f2",100:"#ffe4e6",200:"#fecdd3",300:"#fda4af",400:"#fb7185",500:"#f43f5e",600:"#e11d48",700:"#be123c",800:"#9f1239",900:"#881337",950:"#4c0519"},slate:{50:"#f8fafc",100:"#f1f5f9",200:"#e2e8f0",300:"#cbd5e1",400:"#94a3b8",500:"#64748b",600:"#475569",700:"#334155",800:"#1e293b",900:"#0f172a",950:"#020617"},gray:{50:"#f9fafb",100:"#f3f4f6",200:"#e5e7eb",300:"#d1d5db",400:"#9ca3af",500:"#6b7280",600:"#4b5563",700:"#374151",800:"#1f2937",900:"#111827",950:"#030712"},zinc:{50:"#fafafa",100:"#f4f4f5",200:"#e4e4e7",300:"#d4d4d8",400:"#a1a1aa",500:"#71717a",600:"#52525b",700:"#3f3f46",800:"#27272a",900:"#18181b",950:"#09090b"},neutral:{50:"#fafafa",100:"#f5f5f5",200:"#e5e5e5",300:"#d4d4d4",400:"#a3a3a3",500:"#737373",600:"#525252",700:"#404040",800:"#262626",900:"#171717",950:"#0a0a0a"},stone:{50:"#fafaf9",100:"#f5f5f4",200:"#e7e5e4",300:"#d6d3d1",400:"#a8a29e",500:"#78716c",600:"#57534e",700:"#44403c",800:"#292524",900:"#1c1917",950:"#0c0a09"}},semantic:{transitionDuration:"0.2s",focusRing:{width:"1px",style:"solid",color:"{primary.color}",offset:"2px",shadow:"none"},disabledOpacity:"0.6",iconSize:"1rem",anchorGutter:"2px",primary:{50:"{emerald.50}",100:"{emerald.100}",200:"{emerald.200}",300:"{emerald.300}",400:"{emerald.400}",500:"{emerald.500}",600:"{emerald.600}",700:"{emerald.700}",800:"{emerald.800}",900:"{emerald.900}",950:"{emerald.950}"},formField:{paddingX:"0.75rem",paddingY:"0.5rem",sm:{fontSize:"0.875rem",paddingX:"0.625rem",paddingY:"0.375rem"},lg:{fontSize:"1.125rem",paddingX:"0.875rem",paddingY:"0.625rem"},borderRadius:"{border.radius.md}",focusRing:{width:"0",style:"none",color:"transparent",offset:"0",shadow:"none"},transitionDuration:"{transition.duration}"},list:{padding:"0.25rem 0.25rem",gap:"2px",header:{padding:"0.5rem 1rem 0.25rem 1rem"},option:{padding:"0.5rem 0.75rem",borderRadius:"{border.radius.sm}"},optionGroup:{padding:"0.5rem 0.75rem",fontWeight:"600"}},content:{borderRadius:"{border.radius.md}"},mask:{transitionDuration:"0.15s"},navigation:{list:{padding:"0.25rem 0.25rem",gap:"2px"},item:{padding:"0.5rem 0.75rem",borderRadius:"{border.radius.sm}",gap:"0.5rem"},submenuLabel:{padding:"0.5rem 0.75rem",fontWeight:"600"},submenuIcon:{size:"0.875rem"}},overlay:{select:{borderRadius:"{border.radius.md}",shadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)"},popover:{borderRadius:"{border.radius.md}",padding:"0.75rem",shadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)"},modal:{borderRadius:"{border.radius.xl}",padding:"1.25rem",shadow:"0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)"},navigation:{shadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)"}},colorScheme:{light:{surface:{0:"#ffffff",50:"{slate.50}",100:"{slate.100}",200:"{slate.200}",300:"{slate.300}",400:"{slate.400}",500:"{slate.500}",600:"{slate.600}",700:"{slate.700}",800:"{slate.800}",900:"{slate.900}",950:"{slate.950}"},primary:{color:"{primary.500}",contrastColor:"#ffffff",hoverColor:"{primary.600}",activeColor:"{primary.700}"},highlight:{background:"{primary.50}",focusBackground:"{primary.100}",color:"{primary.700}",focusColor:"{primary.800}"},mask:{background:"rgba(0,0,0,0.4)",color:"{surface.200}"},formField:{background:"{surface.0}",disabledBackground:"{surface.200}",filledBackground:"{surface.50}",filledHoverBackground:"{surface.50}",filledFocusBackground:"{surface.50}",borderColor:"{surface.300}",hoverBorderColor:"{surface.400}",focusBorderColor:"{primary.color}",invalidBorderColor:"{red.400}",color:"{surface.700}",disabledColor:"{surface.500}",placeholderColor:"{surface.500}",invalidPlaceholderColor:"{red.600}",floatLabelColor:"{surface.500}",floatLabelFocusColor:"{primary.600}",floatLabelActiveColor:"{surface.500}",floatLabelInvalidColor:"{form.field.invalid.placeholder.color}",iconColor:"{surface.400}",shadow:"0 0 #0000, 0 0 #0000, 0 1px 2px 0 rgba(18, 18, 23, 0.05)"},text:{color:"{surface.700}",hoverColor:"{surface.800}",mutedColor:"{surface.500}",hoverMutedColor:"{surface.600}"},content:{background:"{surface.0}",hoverBackground:"{surface.100}",borderColor:"{surface.200}",color:"{text.color}",hoverColor:"{text.hover.color}"},overlay:{select:{background:"{surface.0}",borderColor:"{surface.200}",color:"{text.color}"},popover:{background:"{surface.0}",borderColor:"{surface.200}",color:"{text.color}"},modal:{background:"{surface.0}",borderColor:"{surface.200}",color:"{text.color}"}},list:{option:{focusBackground:"{surface.100}",selectedBackground:"{highlight.background}",selectedFocusBackground:"{highlight.focus.background}",color:"{text.color}",focusColor:"{text.hover.color}",selectedColor:"{highlight.color}",selectedFocusColor:"{highlight.focus.color}",icon:{color:"{surface.400}",focusColor:"{surface.500}"}},optionGroup:{background:"transparent",color:"{text.muted.color}"}},navigation:{item:{focusBackground:"{surface.100}",activeBackground:"{surface.100}",color:"{text.color}",focusColor:"{text.hover.color}",activeColor:"{text.hover.color}",icon:{color:"{surface.400}",focusColor:"{surface.500}",activeColor:"{surface.500}"}},submenuLabel:{background:"transparent",color:"{text.muted.color}"},submenuIcon:{color:"{surface.400}",focusColor:"{surface.500}",activeColor:"{surface.500}"}}},dark:{surface:{0:"#ffffff",50:"{zinc.50}",100:"{zinc.100}",200:"{zinc.200}",300:"{zinc.300}",400:"{zinc.400}",500:"{zinc.500}",600:"{zinc.600}",700:"{zinc.700}",800:"{zinc.800}",900:"{zinc.900}",950:"{zinc.950}"},primary:{color:"{primary.400}",contrastColor:"{surface.900}",hoverColor:"{primary.300}",activeColor:"{primary.200}"},highlight:{background:"color-mix(in srgb, {primary.400}, transparent 84%)",focusBackground:"color-mix(in srgb, {primary.400}, transparent 76%)",color:"rgba(255,255,255,.87)",focusColor:"rgba(255,255,255,.87)"},mask:{background:"rgba(0,0,0,0.6)",color:"{surface.200}"},formField:{background:"{surface.950}",disabledBackground:"{surface.700}",filledBackground:"{surface.800}",filledHoverBackground:"{surface.800}",filledFocusBackground:"{surface.800}",borderColor:"{surface.600}",hoverBorderColor:"{surface.500}",focusBorderColor:"{primary.color}",invalidBorderColor:"{red.300}",color:"{surface.0}",disabledColor:"{surface.400}",placeholderColor:"{surface.400}",invalidPlaceholderColor:"{red.400}",floatLabelColor:"{surface.400}",floatLabelFocusColor:"{primary.color}",floatLabelActiveColor:"{surface.400}",floatLabelInvalidColor:"{form.field.invalid.placeholder.color}",iconColor:"{surface.400}",shadow:"0 0 #0000, 0 0 #0000, 0 1px 2px 0 rgba(18, 18, 23, 0.05)"},text:{color:"{surface.0}",hoverColor:"{surface.0}",mutedColor:"{surface.400}",hoverMutedColor:"{surface.300}"},content:{background:"{surface.900}",hoverBackground:"{surface.800}",borderColor:"{surface.700}",color:"{text.color}",hoverColor:"{text.hover.color}"},overlay:{select:{background:"{surface.900}",borderColor:"{surface.700}",color:"{text.color}"},popover:{background:"{surface.900}",borderColor:"{surface.700}",color:"{text.color}"},modal:{background:"{surface.900}",borderColor:"{surface.700}",color:"{text.color}"}},list:{option:{focusBackground:"{surface.800}",selectedBackground:"{highlight.background}",selectedFocusBackground:"{highlight.focus.background}",color:"{text.color}",focusColor:"{text.hover.color}",selectedColor:"{highlight.color}",selectedFocusColor:"{highlight.focus.color}",icon:{color:"{surface.500}",focusColor:"{surface.400}"}},optionGroup:{background:"transparent",color:"{text.muted.color}"}},navigation:{item:{focusBackground:"{surface.800}",activeBackground:"{surface.800}",color:"{text.color}",focusColor:"{text.hover.color}",activeColor:"{text.hover.color}",icon:{color:"{surface.500}",focusColor:"{surface.400}",activeColor:"{surface.400}"}},submenuLabel:{background:"transparent",color:"{text.muted.color}"},submenuIcon:{color:"{surface.500}",focusColor:"{surface.400}",activeColor:"{surface.400}"}}}}}};var _n={root:{borderRadius:"{content.border.radius}"}};var vn={root:{padding:"1rem",background:"{content.background}",gap:"0.5rem",transitionDuration:"{transition.duration}"},item:{color:"{text.muted.color}",hoverColor:"{text.color}",borderRadius:"{content.border.radius}",gap:"{navigation.item.gap}",icon:{color:"{navigation.item.icon.color}",hoverColor:"{navigation.item.icon.focus.color}"},focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},separator:{color:"{navigation.item.icon.color}"}};var yn={root:{borderRadius:"{form.field.border.radius}",roundedBorderRadius:"2rem",gap:"0.5rem",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",iconOnlyWidth:"2.5rem",sm:{fontSize:"{form.field.sm.font.size}",paddingX:"{form.field.sm.padding.x}",paddingY:"{form.field.sm.padding.y}",iconOnlyWidth:"2rem"},lg:{fontSize:"{form.field.lg.font.size}",paddingX:"{form.field.lg.padding.x}",paddingY:"{form.field.lg.padding.y}",iconOnlyWidth:"3rem"},label:{fontWeight:"500"},raisedShadow:"0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12)",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",offset:"{focus.ring.offset}"},badgeSize:"1rem",transitionDuration:"{form.field.transition.duration}"},colorScheme:{light:{root:{primary:{background:"{primary.color}",hoverBackground:"{primary.hover.color}",activeBackground:"{primary.active.color}",borderColor:"{primary.color}",hoverBorderColor:"{primary.hover.color}",activeBorderColor:"{primary.active.color}",color:"{primary.contrast.color}",hoverColor:"{primary.contrast.color}",activeColor:"{primary.contrast.color}",focusRing:{color:"{primary.color}",shadow:"none"}},secondary:{background:"{surface.100}",hoverBackground:"{surface.200}",activeBackground:"{surface.300}",borderColor:"{surface.100}",hoverBorderColor:"{surface.200}",activeBorderColor:"{surface.300}",color:"{surface.600}",hoverColor:"{surface.700}",activeColor:"{surface.800}",focusRing:{color:"{surface.600}",shadow:"none"}},info:{background:"{sky.500}",hoverBackground:"{sky.600}",activeBackground:"{sky.700}",borderColor:"{sky.500}",hoverBorderColor:"{sky.600}",activeBorderColor:"{sky.700}",color:"#ffffff",hoverColor:"#ffffff",activeColor:"#ffffff",focusRing:{color:"{sky.500}",shadow:"none"}},success:{background:"{green.500}",hoverBackground:"{green.600}",activeBackground:"{green.700}",borderColor:"{green.500}",hoverBorderColor:"{green.600}",activeBorderColor:"{green.700}",color:"#ffffff",hoverColor:"#ffffff",activeColor:"#ffffff",focusRing:{color:"{green.500}",shadow:"none"}},warn:{background:"{orange.500}",hoverBackground:"{orange.600}",activeBackground:"{orange.700}",borderColor:"{orange.500}",hoverBorderColor:"{orange.600}",activeBorderColor:"{orange.700}",color:"#ffffff",hoverColor:"#ffffff",activeColor:"#ffffff",focusRing:{color:"{orange.500}",shadow:"none"}},help:{background:"{purple.500}",hoverBackground:"{purple.600}",activeBackground:"{purple.700}",borderColor:"{purple.500}",hoverBorderColor:"{purple.600}",activeBorderColor:"{purple.700}",color:"#ffffff",hoverColor:"#ffffff",activeColor:"#ffffff",focusRing:{color:"{purple.500}",shadow:"none"}},danger:{background:"{red.500}",hoverBackground:"{red.600}",activeBackground:"{red.700}",borderColor:"{red.500}",hoverBorderColor:"{red.600}",activeBorderColor:"{red.700}",color:"#ffffff",hoverColor:"#ffffff",activeColor:"#ffffff",focusRing:{color:"{red.500}",shadow:"none"}},contrast:{background:"{surface.950}",hoverBackground:"{surface.900}",activeBackground:"{surface.800}",borderColor:"{surface.950}",hoverBorderColor:"{surface.900}",activeBorderColor:"{surface.800}",color:"{surface.0}",hoverColor:"{surface.0}",activeColor:"{surface.0}",focusRing:{color:"{surface.950}",shadow:"none"}}},outlined:{primary:{hoverBackground:"{primary.50}",activeBackground:"{primary.100}",borderColor:"{primary.200}",color:"{primary.color}"},secondary:{hoverBackground:"{surface.50}",activeBackground:"{surface.100}",borderColor:"{surface.200}",color:"{surface.500}"},success:{hoverBackground:"{green.50}",activeBackground:"{green.100}",borderColor:"{green.200}",color:"{green.500}"},info:{hoverBackground:"{sky.50}",activeBackground:"{sky.100}",borderColor:"{sky.200}",color:"{sky.500}"},warn:{hoverBackground:"{orange.50}",activeBackground:"{orange.100}",borderColor:"{orange.200}",color:"{orange.500}"},help:{hoverBackground:"{purple.50}",activeBackground:"{purple.100}",borderColor:"{purple.200}",color:"{purple.500}"},danger:{hoverBackground:"{red.50}",activeBackground:"{red.100}",borderColor:"{red.200}",color:"{red.500}"},contrast:{hoverBackground:"{surface.50}",activeBackground:"{surface.100}",borderColor:"{surface.700}",color:"{surface.950}"},plain:{hoverBackground:"{surface.50}",activeBackground:"{surface.100}",borderColor:"{surface.200}",color:"{surface.700}"}},text:{primary:{hoverBackground:"{primary.50}",activeBackground:"{primary.100}",color:"{primary.color}"},secondary:{hoverBackground:"{surface.50}",activeBackground:"{surface.100}",color:"{surface.500}"},success:{hoverBackground:"{green.50}",activeBackground:"{green.100}",color:"{green.500}"},info:{hoverBackground:"{sky.50}",activeBackground:"{sky.100}",color:"{sky.500}"},warn:{hoverBackground:"{orange.50}",activeBackground:"{orange.100}",color:"{orange.500}"},help:{hoverBackground:"{purple.50}",activeBackground:"{purple.100}",color:"{purple.500}"},danger:{hoverBackground:"{red.50}",activeBackground:"{red.100}",color:"{red.500}"},contrast:{hoverBackground:"{surface.50}",activeBackground:"{surface.100}",color:"{surface.950}"},plain:{hoverBackground:"{surface.50}",activeBackground:"{surface.100}",color:"{surface.700}"}},link:{color:"{primary.color}",hoverColor:"{primary.color}",activeColor:"{primary.color}"}},dark:{root:{primary:{background:"{primary.color}",hoverBackground:"{primary.hover.color}",activeBackground:"{primary.active.color}",borderColor:"{primary.color}",hoverBorderColor:"{primary.hover.color}",activeBorderColor:"{primary.active.color}",color:"{primary.contrast.color}",hoverColor:"{primary.contrast.color}",activeColor:"{primary.contrast.color}",focusRing:{color:"{primary.color}",shadow:"none"}},secondary:{background:"{surface.800}",hoverBackground:"{surface.700}",activeBackground:"{surface.600}",borderColor:"{surface.800}",hoverBorderColor:"{surface.700}",activeBorderColor:"{surface.600}",color:"{surface.300}",hoverColor:"{surface.200}",activeColor:"{surface.100}",focusRing:{color:"{surface.300}",shadow:"none"}},info:{background:"{sky.400}",hoverBackground:"{sky.300}",activeBackground:"{sky.200}",borderColor:"{sky.400}",hoverBorderColor:"{sky.300}",activeBorderColor:"{sky.200}",color:"{sky.950}",hoverColor:"{sky.950}",activeColor:"{sky.950}",focusRing:{color:"{sky.400}",shadow:"none"}},success:{background:"{green.400}",hoverBackground:"{green.300}",activeBackground:"{green.200}",borderColor:"{green.400}",hoverBorderColor:"{green.300}",activeBorderColor:"{green.200}",color:"{green.950}",hoverColor:"{green.950}",activeColor:"{green.950}",focusRing:{color:"{green.400}",shadow:"none"}},warn:{background:"{orange.400}",hoverBackground:"{orange.300}",activeBackground:"{orange.200}",borderColor:"{orange.400}",hoverBorderColor:"{orange.300}",activeBorderColor:"{orange.200}",color:"{orange.950}",hoverColor:"{orange.950}",activeColor:"{orange.950}",focusRing:{color:"{orange.400}",shadow:"none"}},help:{background:"{purple.400}",hoverBackground:"{purple.300}",activeBackground:"{purple.200}",borderColor:"{purple.400}",hoverBorderColor:"{purple.300}",activeBorderColor:"{purple.200}",color:"{purple.950}",hoverColor:"{purple.950}",activeColor:"{purple.950}",focusRing:{color:"{purple.400}",shadow:"none"}},danger:{background:"{red.400}",hoverBackground:"{red.300}",activeBackground:"{red.200}",borderColor:"{red.400}",hoverBorderColor:"{red.300}",activeBorderColor:"{red.200}",color:"{red.950}",hoverColor:"{red.950}",activeColor:"{red.950}",focusRing:{color:"{red.400}",shadow:"none"}},contrast:{background:"{surface.0}",hoverBackground:"{surface.100}",activeBackground:"{surface.200}",borderColor:"{surface.0}",hoverBorderColor:"{surface.100}",activeBorderColor:"{surface.200}",color:"{surface.950}",hoverColor:"{surface.950}",activeColor:"{surface.950}",focusRing:{color:"{surface.0}",shadow:"none"}}},outlined:{primary:{hoverBackground:"color-mix(in srgb, {primary.color}, transparent 96%)",activeBackground:"color-mix(in srgb, {primary.color}, transparent 84%)",borderColor:"{primary.700}",color:"{primary.color}"},secondary:{hoverBackground:"rgba(255,255,255,0.04)",activeBackground:"rgba(255,255,255,0.16)",borderColor:"{surface.700}",color:"{surface.400}"},success:{hoverBackground:"color-mix(in srgb, {green.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {green.400}, transparent 84%)",borderColor:"{green.700}",color:"{green.400}"},info:{hoverBackground:"color-mix(in srgb, {sky.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {sky.400}, transparent 84%)",borderColor:"{sky.700}",color:"{sky.400}"},warn:{hoverBackground:"color-mix(in srgb, {orange.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {orange.400}, transparent 84%)",borderColor:"{orange.700}",color:"{orange.400}"},help:{hoverBackground:"color-mix(in srgb, {purple.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {purple.400}, transparent 84%)",borderColor:"{purple.700}",color:"{purple.400}"},danger:{hoverBackground:"color-mix(in srgb, {red.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {red.400}, transparent 84%)",borderColor:"{red.700}",color:"{red.400}"},contrast:{hoverBackground:"{surface.800}",activeBackground:"{surface.700}",borderColor:"{surface.500}",color:"{surface.0}"},plain:{hoverBackground:"{surface.800}",activeBackground:"{surface.700}",borderColor:"{surface.600}",color:"{surface.0}"}},text:{primary:{hoverBackground:"color-mix(in srgb, {primary.color}, transparent 96%)",activeBackground:"color-mix(in srgb, {primary.color}, transparent 84%)",color:"{primary.color}"},secondary:{hoverBackground:"{surface.800}",activeBackground:"{surface.700}",color:"{surface.400}"},success:{hoverBackground:"color-mix(in srgb, {green.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {green.400}, transparent 84%)",color:"{green.400}"},info:{hoverBackground:"color-mix(in srgb, {sky.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {sky.400}, transparent 84%)",color:"{sky.400}"},warn:{hoverBackground:"color-mix(in srgb, {orange.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {orange.400}, transparent 84%)",color:"{orange.400}"},help:{hoverBackground:"color-mix(in srgb, {purple.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {purple.400}, transparent 84%)",color:"{purple.400}"},danger:{hoverBackground:"color-mix(in srgb, {red.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {red.400}, transparent 84%)",color:"{red.400}"},contrast:{hoverBackground:"{surface.800}",activeBackground:"{surface.700}",color:"{surface.0}"},plain:{hoverBackground:"{surface.800}",activeBackground:"{surface.700}",color:"{surface.0}"}},link:{color:"{primary.color}",hoverColor:"{primary.color}",activeColor:"{primary.color}"}}}};var kn={root:{background:"{content.background}",borderRadius:"{border.radius.xl}",color:"{content.color}",shadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1)"},body:{padding:"1.25rem",gap:"0.5rem"},caption:{gap:"0.5rem"},title:{fontSize:"1.25rem",fontWeight:"500"},subtitle:{color:"{text.muted.color}"}};var wn={root:{transitionDuration:"{transition.duration}"},content:{gap:"0.25rem"},indicatorList:{padding:"1rem",gap:"0.5rem"},indicator:{width:"2rem",height:"0.5rem",borderRadius:"{content.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},colorScheme:{light:{indicator:{background:"{surface.200}",hoverBackground:"{surface.300}",activeBackground:"{primary.color}"}},dark:{indicator:{background:"{surface.700}",hoverBackground:"{surface.600}",activeBackground:"{primary.color}"}}}};var Cn={root:{background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledHoverBackground:"{form.field.filled.hover.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",invalidPlaceholderColor:"{form.field.invalid.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}",sm:{fontSize:"{form.field.sm.font.size}",paddingX:"{form.field.sm.padding.x}",paddingY:"{form.field.sm.padding.y}"},lg:{fontSize:"{form.field.lg.font.size}",paddingX:"{form.field.lg.padding.x}",paddingY:"{form.field.lg.padding.y}"}},dropdown:{width:"2.5rem",color:"{form.field.icon.color}"},overlay:{background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}"},list:{padding:"{list.padding}",gap:"{list.gap}",mobileIndent:"1rem"},option:{focusBackground:"{list.option.focus.background}",selectedBackground:"{list.option.selected.background}",selectedFocusBackground:"{list.option.selected.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",selectedColor:"{list.option.selected.color}",selectedFocusColor:"{list.option.selected.focus.color}",padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}",icon:{color:"{list.option.icon.color}",focusColor:"{list.option.icon.focus.color}",size:"0.875rem"}},clearIcon:{color:"{form.field.icon.color}"}};var xn={root:{borderRadius:"{border.radius.sm}",width:"1.25rem",height:"1.25rem",background:"{form.field.background}",checkedBackground:"{primary.color}",checkedHoverBackground:"{primary.hover.color}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.border.color}",checkedBorderColor:"{primary.color}",checkedHoverBorderColor:"{primary.hover.color}",checkedFocusBorderColor:"{primary.color}",checkedDisabledBorderColor:"{form.field.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",shadow:"{form.field.shadow}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}",sm:{width:"1rem",height:"1rem"},lg:{width:"1.5rem",height:"1.5rem"}},icon:{size:"0.875rem",color:"{form.field.color}",checkedColor:"{primary.contrast.color}",checkedHoverColor:"{primary.contrast.color}",disabledColor:"{form.field.disabled.color}",sm:{size:"0.75rem"},lg:{size:"1rem"}}};var In={root:{borderRadius:"16px",paddingX:"0.75rem",paddingY:"0.5rem",gap:"0.5rem",transitionDuration:"{transition.duration}"},image:{width:"2rem",height:"2rem"},icon:{size:"1rem"},removeIcon:{size:"1rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"}},colorScheme:{light:{root:{background:"{surface.100}",color:"{surface.800}"},icon:{color:"{surface.800}"},removeIcon:{color:"{surface.800}"}},dark:{root:{background:"{surface.800}",color:"{surface.0}"},icon:{color:"{surface.0}"},removeIcon:{color:"{surface.0}"}}}};var Sn={root:{transitionDuration:"{transition.duration}"},preview:{width:"1.5rem",height:"1.5rem",borderRadius:"{form.field.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},panel:{shadow:"{overlay.popover.shadow}",borderRadius:"{overlay.popover.borderRadius}"},colorScheme:{light:{panel:{background:"{surface.800}",borderColor:"{surface.900}"},handle:{color:"{surface.0}"}},dark:{panel:{background:"{surface.900}",borderColor:"{surface.700}"},handle:{color:"{surface.0}"}}}};var Tn={icon:{size:"2rem",color:"{overlay.modal.color}"},content:{gap:"1rem"}};var En={root:{background:"{overlay.popover.background}",borderColor:"{overlay.popover.border.color}",color:"{overlay.popover.color}",borderRadius:"{overlay.popover.border.radius}",shadow:"{overlay.popover.shadow}",gutter:"10px",arrowOffset:"1.25rem"},content:{padding:"{overlay.popover.padding}",gap:"1rem"},icon:{size:"1.5rem",color:"{overlay.popover.color}"},footer:{gap:"0.5rem",padding:"0 {overlay.popover.padding} {overlay.popover.padding} {overlay.popover.padding}"}};var Mn={root:{background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}",shadow:"{overlay.navigation.shadow}",transitionDuration:"{transition.duration}"},list:{padding:"{navigation.list.padding}",gap:"{navigation.list.gap}"},item:{focusBackground:"{navigation.item.focus.background}",activeBackground:"{navigation.item.active.background}",color:"{navigation.item.color}",focusColor:"{navigation.item.focus.color}",activeColor:"{navigation.item.active.color}",padding:"{navigation.item.padding}",borderRadius:"{navigation.item.border.radius}",gap:"{navigation.item.gap}",icon:{color:"{navigation.item.icon.color}",focusColor:"{navigation.item.icon.focus.color}",activeColor:"{navigation.item.icon.active.color}"}},submenu:{mobileIndent:"1rem"},submenuIcon:{size:"{navigation.submenu.icon.size}",color:"{navigation.submenu.icon.color}",focusColor:"{navigation.submenu.icon.focus.color}",activeColor:"{navigation.submenu.icon.active.color}"},separator:{borderColor:"{content.border.color}"}};var On={root:{transitionDuration:"{transition.duration}"},header:{background:"{content.background}",borderColor:"{datatable.border.color}",color:"{content.color}",borderWidth:"0 0 1px 0",padding:"0.75rem 1rem",sm:{padding:"0.375rem 0.5rem"},lg:{padding:"1rem 1.25rem"}},headerCell:{background:"{content.background}",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",borderColor:"{datatable.border.color}",color:"{content.color}",hoverColor:"{content.hover.color}",selectedColor:"{highlight.color}",gap:"0.5rem",padding:"0.75rem 1rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"},sm:{padding:"0.375rem 0.5rem"},lg:{padding:"1rem 1.25rem"}},columnTitle:{fontWeight:"600"},row:{background:"{content.background}",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",color:"{content.color}",hoverColor:"{content.hover.color}",selectedColor:"{highlight.color}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"}},bodyCell:{borderColor:"{datatable.border.color}",padding:"0.75rem 1rem",sm:{padding:"0.375rem 0.5rem"},lg:{padding:"1rem 1.25rem"}},footerCell:{background:"{content.background}",borderColor:"{datatable.border.color}",color:"{content.color}",padding:"0.75rem 1rem",sm:{padding:"0.375rem 0.5rem"},lg:{padding:"1rem 1.25rem"}},columnFooter:{fontWeight:"600"},footer:{background:"{content.background}",borderColor:"{datatable.border.color}",color:"{content.color}",borderWidth:"0 0 1px 0",padding:"0.75rem 1rem",sm:{padding:"0.375rem 0.5rem"},lg:{padding:"1rem 1.25rem"}},dropPoint:{color:"{primary.color}"},columnResizer:{width:"0.5rem"},resizeIndicator:{width:"1px",color:"{primary.color}"},sortIcon:{color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}",size:"0.875rem"},loadingIcon:{size:"2rem"},rowToggleButton:{hoverBackground:"{content.hover.background}",selectedHoverBackground:"{content.background}",color:"{text.muted.color}",hoverColor:"{text.color}",selectedHoverColor:"{primary.color}",size:"1.75rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},filter:{inlineGap:"0.5rem",overlaySelect:{background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}"},overlayPopover:{background:"{overlay.popover.background}",borderColor:"{overlay.popover.border.color}",borderRadius:"{overlay.popover.border.radius}",color:"{overlay.popover.color}",shadow:"{overlay.popover.shadow}",padding:"{overlay.popover.padding}",gap:"0.5rem"},rule:{borderColor:"{content.border.color}"},constraintList:{padding:"{list.padding}",gap:"{list.gap}"},constraint:{focusBackground:"{list.option.focus.background}",selectedBackground:"{list.option.selected.background}",selectedFocusBackground:"{list.option.selected.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",selectedColor:"{list.option.selected.color}",selectedFocusColor:"{list.option.selected.focus.color}",separator:{borderColor:"{content.border.color}"},padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}"}},paginatorTop:{borderColor:"{datatable.border.color}",borderWidth:"0 0 1px 0"},paginatorBottom:{borderColor:"{datatable.border.color}",borderWidth:"0 0 1px 0"},colorScheme:{light:{root:{borderColor:"{content.border.color}"},row:{stripedBackground:"{surface.50}"},bodyCell:{selectedBorderColor:"{primary.100}"}},dark:{root:{borderColor:"{surface.800}"},row:{stripedBackground:"{surface.950}"},bodyCell:{selectedBorderColor:"{primary.900}"}}}};var Bn={root:{borderColor:"transparent",borderWidth:"0",borderRadius:"0",padding:"0"},header:{background:"{content.background}",color:"{content.color}",borderColor:"{content.border.color}",borderWidth:"0 0 1px 0",padding:"0.75rem 1rem",borderRadius:"0"},content:{background:"{content.background}",color:"{content.color}",borderColor:"transparent",borderWidth:"0",padding:"0",borderRadius:"0"},footer:{background:"{content.background}",color:"{content.color}",borderColor:"{content.border.color}",borderWidth:"1px 0 0 0",padding:"0.75rem 1rem",borderRadius:"0"},paginatorTop:{borderColor:"{content.border.color}",borderWidth:"0 0 1px 0"},paginatorBottom:{borderColor:"{content.border.color}",borderWidth:"1px 0 0 0"}};var Dn={root:{transitionDuration:"{transition.duration}"},panel:{background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}",shadow:"{overlay.popover.shadow}",padding:"{overlay.popover.padding}"},header:{background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",padding:"0 0 0.5rem 0"},title:{gap:"0.5rem",fontWeight:"500"},dropdown:{width:"2.5rem",sm:{width:"2rem"},lg:{width:"3rem"},borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.border.color}",activeBorderColor:"{form.field.border.color}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},inputIcon:{color:"{form.field.icon.color}"},selectMonth:{hoverBackground:"{content.hover.background}",color:"{content.color}",hoverColor:"{content.hover.color}",padding:"0.25rem 0.5rem",borderRadius:"{content.border.radius}"},selectYear:{hoverBackground:"{content.hover.background}",color:"{content.color}",hoverColor:"{content.hover.color}",padding:"0.25rem 0.5rem",borderRadius:"{content.border.radius}"},group:{borderColor:"{content.border.color}",gap:"{overlay.popover.padding}"},dayView:{margin:"0.5rem 0 0 0"},weekDay:{padding:"0.25rem",fontWeight:"500",color:"{content.color}"},date:{hoverBackground:"{content.hover.background}",selectedBackground:"{primary.color}",rangeSelectedBackground:"{highlight.background}",color:"{content.color}",hoverColor:"{content.hover.color}",selectedColor:"{primary.contrast.color}",rangeSelectedColor:"{highlight.color}",width:"2rem",height:"2rem",borderRadius:"50%",padding:"0.25rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},monthView:{margin:"0.5rem 0 0 0"},month:{padding:"0.375rem",borderRadius:"{content.border.radius}"},yearView:{margin:"0.5rem 0 0 0"},year:{padding:"0.375rem",borderRadius:"{content.border.radius}"},buttonbar:{padding:"0.5rem 0 0 0",borderColor:"{content.border.color}"},timePicker:{padding:"0.5rem 0 0 0",borderColor:"{content.border.color}",gap:"0.5rem",buttonGap:"0.25rem"},colorScheme:{light:{dropdown:{background:"{surface.100}",hoverBackground:"{surface.200}",activeBackground:"{surface.300}",color:"{surface.600}",hoverColor:"{surface.700}",activeColor:"{surface.800}"},today:{background:"{surface.200}",color:"{surface.900}"}},dark:{dropdown:{background:"{surface.800}",hoverBackground:"{surface.700}",activeBackground:"{surface.600}",color:"{surface.300}",hoverColor:"{surface.200}",activeColor:"{surface.100}"},today:{background:"{surface.700}",color:"{surface.0}"}}}};var Vn={root:{background:"{overlay.modal.background}",borderColor:"{overlay.modal.border.color}",color:"{overlay.modal.color}",borderRadius:"{overlay.modal.border.radius}",shadow:"{overlay.modal.shadow}"},header:{padding:"{overlay.modal.padding}",gap:"0.5rem"},title:{fontSize:"1.25rem",fontWeight:"600"},content:{padding:"0 {overlay.modal.padding} {overlay.modal.padding} {overlay.modal.padding}"},footer:{padding:"0 {overlay.modal.padding} {overlay.modal.padding} {overlay.modal.padding}",gap:"0.5rem"}};var Fn={root:{borderColor:"{content.border.color}"},content:{background:"{content.background}",color:"{text.color}"},horizontal:{margin:"1rem 0",padding:"0 1rem",content:{padding:"0 0.5rem"}},vertical:{margin:"0 1rem",padding:"0.5rem 0",content:{padding:"0.5rem 0"}}};var Ln={root:{background:"rgba(255, 255, 255, 0.1)",borderColor:"rgba(255, 255, 255, 0.2)",padding:"0.5rem",borderRadius:"{border.radius.xl}"},item:{borderRadius:"{content.border.radius}",padding:"0.5rem",size:"3rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}}};var Pn={root:{background:"{overlay.modal.background}",borderColor:"{overlay.modal.border.color}",color:"{overlay.modal.color}",shadow:"{overlay.modal.shadow}"},header:{padding:"{overlay.modal.padding}"},title:{fontSize:"1.5rem",fontWeight:"600"},content:{padding:"0 {overlay.modal.padding} {overlay.modal.padding} {overlay.modal.padding}"},footer:{padding:"{overlay.modal.padding}"}};var An={toolbar:{background:"{content.background}",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}"},toolbarItem:{color:"{text.muted.color}",hoverColor:"{text.color}",activeColor:"{primary.color}"},overlay:{background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}",padding:"{list.padding}"},overlayOption:{focusBackground:"{list.option.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}"},content:{background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}"}};var Rn={root:{background:"{content.background}",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}",color:"{content.color}",padding:"0 1.125rem 1.125rem 1.125rem",transitionDuration:"{transition.duration}"},legend:{background:"{content.background}",hoverBackground:"{content.hover.background}",color:"{content.color}",hoverColor:"{content.hover.color}",borderRadius:"{content.border.radius}",borderWidth:"1px",borderColor:"transparent",padding:"0.5rem 0.75rem",gap:"0.5rem",fontWeight:"600",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},toggleIcon:{color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}"},content:{padding:"0"}};var Hn={root:{background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}",transitionDuration:"{transition.duration}"},header:{background:"transparent",color:"{text.color}",padding:"1.125rem",borderColor:"unset",borderWidth:"0",borderRadius:"0",gap:"0.5rem"},content:{highlightBorderColor:"{primary.color}",padding:"0 1.125rem 1.125rem 1.125rem",gap:"1rem"},file:{padding:"1rem",gap:"1rem",borderColor:"{content.border.color}",info:{gap:"0.5rem"}},fileList:{gap:"0.5rem"},progressbar:{height:"0.25rem"},basic:{gap:"0.5rem"}};var zn={root:{color:"{form.field.float.label.color}",focusColor:"{form.field.float.label.focus.color}",activeColor:"{form.field.float.label.active.color}",invalidColor:"{form.field.float.label.invalid.color}",transitionDuration:"0.2s",positionX:"{form.field.padding.x}",positionY:"{form.field.padding.y}",fontWeight:"500",active:{fontSize:"0.75rem",fontWeight:"400"}},over:{active:{top:"-1.25rem"}},in:{input:{paddingTop:"1.5rem",paddingBottom:"{form.field.padding.y}"},active:{top:"{form.field.padding.y}"}},on:{borderRadius:"{border.radius.xs}",active:{background:"{form.field.background}",padding:"0 0.125rem"}}};var $n={root:{borderWidth:"1px",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}",transitionDuration:"{transition.duration}"},navButton:{background:"rgba(255, 255, 255, 0.1)",hoverBackground:"rgba(255, 255, 255, 0.2)",color:"{surface.100}",hoverColor:"{surface.0}",size:"3rem",gutter:"0.5rem",prev:{borderRadius:"50%"},next:{borderRadius:"50%"},focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},navIcon:{size:"1.5rem"},thumbnailsContent:{background:"{content.background}",padding:"1rem 0.25rem"},thumbnailNavButton:{size:"2rem",borderRadius:"{content.border.radius}",gutter:"0.5rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},thumbnailNavButtonIcon:{size:"1rem"},caption:{background:"rgba(0, 0, 0, 0.5)",color:"{surface.100}",padding:"1rem"},indicatorList:{gap:"0.5rem",padding:"1rem"},indicatorButton:{width:"1rem",height:"1rem",activeBackground:"{primary.color}",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},insetIndicatorList:{background:"rgba(0, 0, 0, 0.5)"},insetIndicatorButton:{background:"rgba(255, 255, 255, 0.4)",hoverBackground:"rgba(255, 255, 255, 0.6)",activeBackground:"rgba(255, 255, 255, 0.9)"},closeButton:{size:"3rem",gutter:"0.5rem",background:"rgba(255, 255, 255, 0.1)",hoverBackground:"rgba(255, 255, 255, 0.2)",color:"{surface.50}",hoverColor:"{surface.0}",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},closeButtonIcon:{size:"1.5rem"},colorScheme:{light:{thumbnailNavButton:{hoverBackground:"{surface.100}",color:"{surface.600}",hoverColor:"{surface.700}"},indicatorButton:{background:"{surface.200}",hoverBackground:"{surface.300}"}},dark:{thumbnailNavButton:{hoverBackground:"{surface.700}",color:"{surface.400}",hoverColor:"{surface.0}"},indicatorButton:{background:"{surface.700}",hoverBackground:"{surface.600}"}}}};var Nn={icon:{color:"{form.field.icon.color}"}};var Un={root:{color:"{form.field.float.label.color}",focusColor:"{form.field.float.label.focus.color}",invalidColor:"{form.field.float.label.invalid.color}",transitionDuration:"0.2s",positionX:"{form.field.padding.x}",top:"{form.field.padding.y}",fontSize:"0.75rem",fontWeight:"400"},input:{paddingTop:"1.5rem",paddingBottom:"{form.field.padding.y}"}};var jn={root:{transitionDuration:"{transition.duration}"},preview:{icon:{size:"1.5rem"},mask:{background:"{mask.background}",color:"{mask.color}"}},toolbar:{position:{left:"auto",right:"1rem",top:"1rem",bottom:"auto"},blur:"8px",background:"rgba(255,255,255,0.1)",borderColor:"rgba(255,255,255,0.2)",borderWidth:"1px",borderRadius:"30px",padding:".5rem",gap:"0.5rem"},action:{hoverBackground:"rgba(255,255,255,0.1)",color:"{surface.50}",hoverColor:"{surface.0}",size:"3rem",iconSize:"1.5rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}}};var Kn={handle:{size:"15px",hoverSize:"30px",background:"rgba(255,255,255,0.3)",hoverBackground:"rgba(255,255,255,0.3)",borderColor:"unset",hoverBorderColor:"unset",borderWidth:"0",borderRadius:"50%",transitionDuration:"{transition.duration}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"rgba(255,255,255,0.3)",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}}};var Wn={root:{padding:"{form.field.padding.y} {form.field.padding.x}",borderRadius:"{content.border.radius}",gap:"0.5rem"},text:{fontWeight:"500"},icon:{size:"1rem"},colorScheme:{light:{info:{background:"color-mix(in srgb, {blue.50}, transparent 5%)",borderColor:"{blue.200}",color:"{blue.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {blue.500}, transparent 96%)"},success:{background:"color-mix(in srgb, {green.50}, transparent 5%)",borderColor:"{green.200}",color:"{green.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {green.500}, transparent 96%)"},warn:{background:"color-mix(in srgb,{yellow.50}, transparent 5%)",borderColor:"{yellow.200}",color:"{yellow.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {yellow.500}, transparent 96%)"},error:{background:"color-mix(in srgb, {red.50}, transparent 5%)",borderColor:"{red.200}",color:"{red.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {red.500}, transparent 96%)"},secondary:{background:"{surface.100}",borderColor:"{surface.200}",color:"{surface.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.500}, transparent 96%)"},contrast:{background:"{surface.900}",borderColor:"{surface.950}",color:"{surface.50}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.950}, transparent 96%)"}},dark:{info:{background:"color-mix(in srgb, {blue.500}, transparent 84%)",borderColor:"color-mix(in srgb, {blue.700}, transparent 64%)",color:"{blue.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {blue.500}, transparent 96%)"},success:{background:"color-mix(in srgb, {green.500}, transparent 84%)",borderColor:"color-mix(in srgb, {green.700}, transparent 64%)",color:"{green.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {green.500}, transparent 96%)"},warn:{background:"color-mix(in srgb, {yellow.500}, transparent 84%)",borderColor:"color-mix(in srgb, {yellow.700}, transparent 64%)",color:"{yellow.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {yellow.500}, transparent 96%)"},error:{background:"color-mix(in srgb, {red.500}, transparent 84%)",borderColor:"color-mix(in srgb, {red.700}, transparent 64%)",color:"{red.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {red.500}, transparent 96%)"},secondary:{background:"{surface.800}",borderColor:"{surface.700}",color:"{surface.300}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.500}, transparent 96%)"},contrast:{background:"{surface.0}",borderColor:"{surface.100}",color:"{surface.950}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.950}, transparent 96%)"}}}};var Qn={root:{padding:"{form.field.padding.y} {form.field.padding.x}",borderRadius:"{content.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"},transitionDuration:"{transition.duration}"},display:{hoverBackground:"{content.hover.background}",hoverColor:"{content.hover.color}"}};var qn={root:{background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}"},chip:{borderRadius:"{border.radius.sm}"},colorScheme:{light:{chip:{focusBackground:"{surface.200}",color:"{surface.800}"}},dark:{chip:{focusBackground:"{surface.700}",color:"{surface.0}"}}}};var Zn={addon:{background:"{form.field.background}",borderColor:"{form.field.border.color}",color:"{form.field.icon.color}",borderRadius:"{form.field.border.radius}",padding:"0.5rem",minWidth:"2.5rem"}};var Gn={root:{transitionDuration:"{transition.duration}"},button:{width:"2.5rem",borderRadius:"{form.field.border.radius}",verticalPadding:"{form.field.padding.y}"},colorScheme:{light:{button:{background:"transparent",hoverBackground:"{surface.100}",activeBackground:"{surface.200}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.border.color}",activeBorderColor:"{form.field.border.color}",color:"{surface.400}",hoverColor:"{surface.500}",activeColor:"{surface.600}"}},dark:{button:{background:"transparent",hoverBackground:"{surface.800}",activeBackground:"{surface.700}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.border.color}",activeBorderColor:"{form.field.border.color}",color:"{surface.400}",hoverColor:"{surface.300}",activeColor:"{surface.200}"}}}};var Yn={root:{gap:"0.5rem"},input:{width:"2.5rem",sm:{width:"2rem"},lg:{width:"3rem"}}};var Xn={root:{background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledHoverBackground:"{form.field.filled.hover.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",invalidPlaceholderColor:"{form.field.invalid.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}",sm:{fontSize:"{form.field.sm.font.size}",paddingX:"{form.field.sm.padding.x}",paddingY:"{form.field.sm.padding.y}"},lg:{fontSize:"{form.field.lg.font.size}",paddingX:"{form.field.lg.padding.x}",paddingY:"{form.field.lg.padding.y}"}}};var Jn={root:{transitionDuration:"{transition.duration}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},value:{background:"{primary.color}"},range:{background:"{content.border.color}"},text:{color:"{text.muted.color}"}};var ea={root:{background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",borderColor:"{form.field.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",shadow:"{form.field.shadow}",borderRadius:"{form.field.border.radius}",transitionDuration:"{form.field.transition.duration}"},list:{padding:"{list.padding}",gap:"{list.gap}",header:{padding:"{list.header.padding}"}},option:{focusBackground:"{list.option.focus.background}",selectedBackground:"{list.option.selected.background}",selectedFocusBackground:"{list.option.selected.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",selectedColor:"{list.option.selected.color}",selectedFocusColor:"{list.option.selected.focus.color}",padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}"},optionGroup:{background:"{list.option.group.background}",color:"{list.option.group.color}",fontWeight:"{list.option.group.font.weight}",padding:"{list.option.group.padding}"},checkmark:{color:"{list.option.color}",gutterStart:"-0.375rem",gutterEnd:"0.375rem"},emptyMessage:{padding:"{list.option.padding}"},colorScheme:{light:{option:{stripedBackground:"{surface.50}"}},dark:{option:{stripedBackground:"{surface.900}"}}}};var ta={root:{background:"{content.background}",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}",color:"{content.color}",gap:"0.5rem",verticalOrientation:{padding:"{navigation.list.padding}",gap:"{navigation.list.gap}"},horizontalOrientation:{padding:"0.5rem 0.75rem",gap:"0.5rem"},transitionDuration:"{transition.duration}"},baseItem:{borderRadius:"{content.border.radius}",padding:"{navigation.item.padding}"},item:{focusBackground:"{navigation.item.focus.background}",activeBackground:"{navigation.item.active.background}",color:"{navigation.item.color}",focusColor:"{navigation.item.focus.color}",activeColor:"{navigation.item.active.color}",padding:"{navigation.item.padding}",borderRadius:"{navigation.item.border.radius}",gap:"{navigation.item.gap}",icon:{color:"{navigation.item.icon.color}",focusColor:"{navigation.item.icon.focus.color}",activeColor:"{navigation.item.icon.active.color}"}},overlay:{padding:"0",background:"{content.background}",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}",color:"{content.color}",shadow:"{overlay.navigation.shadow}",gap:"0.5rem"},submenu:{padding:"{navigation.list.padding}",gap:"{navigation.list.gap}"},submenuLabel:{padding:"{navigation.submenu.label.padding}",fontWeight:"{navigation.submenu.label.font.weight}",background:"{navigation.submenu.label.background.}",color:"{navigation.submenu.label.color}"},submenuIcon:{size:"{navigation.submenu.icon.size}",color:"{navigation.submenu.icon.color}",focusColor:"{navigation.submenu.icon.focus.color}",activeColor:"{navigation.submenu.icon.active.color}"},separator:{borderColor:"{content.border.color}"},mobileButton:{borderRadius:"50%",size:"1.75rem",color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}",hoverBackground:"{content.hover.background}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}}};var oa={root:{background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}",shadow:"{overlay.navigation.shadow}",transitionDuration:"{transition.duration}"},list:{padding:"{navigation.list.padding}",gap:"{navigation.list.gap}"},item:{focusBackground:"{navigation.item.focus.background}",color:"{navigation.item.color}",focusColor:"{navigation.item.focus.color}",padding:"{navigation.item.padding}",borderRadius:"{navigation.item.border.radius}",gap:"{navigation.item.gap}",icon:{color:"{navigation.item.icon.color}",focusColor:"{navigation.item.icon.focus.color}"}},submenuLabel:{padding:"{navigation.submenu.label.padding}",fontWeight:"{navigation.submenu.label.font.weight}",background:"{navigation.submenu.label.background}",color:"{navigation.submenu.label.color}"},separator:{borderColor:"{content.border.color}"}};var ra={root:{background:"{content.background}",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}",color:"{content.color}",gap:"0.5rem",padding:"0.5rem 0.75rem",transitionDuration:"{transition.duration}"},baseItem:{borderRadius:"{content.border.radius}",padding:"{navigation.item.padding}"},item:{focusBackground:"{navigation.item.focus.background}",activeBackground:"{navigation.item.active.background}",color:"{navigation.item.color}",focusColor:"{navigation.item.focus.color}",activeColor:"{navigation.item.active.color}",padding:"{navigation.item.padding}",borderRadius:"{navigation.item.border.radius}",gap:"{navigation.item.gap}",icon:{color:"{navigation.item.icon.color}",focusColor:"{navigation.item.icon.focus.color}",activeColor:"{navigation.item.icon.active.color}"}},submenu:{padding:"{navigation.list.padding}",gap:"{navigation.list.gap}",background:"{content.background}",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}",shadow:"{overlay.navigation.shadow}",mobileIndent:"1rem",icon:{size:"{navigation.submenu.icon.size}",color:"{navigation.submenu.icon.color}",focusColor:"{navigation.submenu.icon.focus.color}",activeColor:"{navigation.submenu.icon.active.color}"}},separator:{borderColor:"{content.border.color}"},mobileButton:{borderRadius:"50%",size:"1.75rem",color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}",hoverBackground:"{content.hover.background}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}}};var ia={root:{borderRadius:"{content.border.radius}",borderWidth:"1px",transitionDuration:"{transition.duration}"},content:{padding:"0.5rem 0.75rem",gap:"0.5rem",sm:{padding:"0.375rem 0.625rem"},lg:{padding:"0.625rem 0.875rem"}},text:{fontSize:"1rem",fontWeight:"500",sm:{fontSize:"0.875rem"},lg:{fontSize:"1.125rem"}},icon:{size:"1.125rem",sm:{size:"1rem"},lg:{size:"1.25rem"}},closeButton:{width:"1.75rem",height:"1.75rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",offset:"{focus.ring.offset}"}},closeIcon:{size:"1rem",sm:{size:"0.875rem"},lg:{size:"1.125rem"}},outlined:{root:{borderWidth:"1px"}},simple:{content:{padding:"0"}},colorScheme:{light:{info:{background:"color-mix(in srgb, {blue.50}, transparent 5%)",borderColor:"{blue.200}",color:"{blue.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {blue.500}, transparent 96%)",closeButton:{hoverBackground:"{blue.100}",focusRing:{color:"{blue.600}",shadow:"none"}},outlined:{color:"{blue.600}",borderColor:"{blue.600}"},simple:{color:"{blue.600}"}},success:{background:"color-mix(in srgb, {green.50}, transparent 5%)",borderColor:"{green.200}",color:"{green.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {green.500}, transparent 96%)",closeButton:{hoverBackground:"{green.100}",focusRing:{color:"{green.600}",shadow:"none"}},outlined:{color:"{green.600}",borderColor:"{green.600}"},simple:{color:"{green.600}"}},warn:{background:"color-mix(in srgb,{yellow.50}, transparent 5%)",borderColor:"{yellow.200}",color:"{yellow.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {yellow.500}, transparent 96%)",closeButton:{hoverBackground:"{yellow.100}",focusRing:{color:"{yellow.600}",shadow:"none"}},outlined:{color:"{yellow.600}",borderColor:"{yellow.600}"},simple:{color:"{yellow.600}"}},error:{background:"color-mix(in srgb, {red.50}, transparent 5%)",borderColor:"{red.200}",color:"{red.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {red.500}, transparent 96%)",closeButton:{hoverBackground:"{red.100}",focusRing:{color:"{red.600}",shadow:"none"}},outlined:{color:"{red.600}",borderColor:"{red.600}"},simple:{color:"{red.600}"}},secondary:{background:"{surface.100}",borderColor:"{surface.200}",color:"{surface.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.500}, transparent 96%)",closeButton:{hoverBackground:"{surface.200}",focusRing:{color:"{surface.600}",shadow:"none"}},outlined:{color:"{surface.500}",borderColor:"{surface.500}"},simple:{color:"{surface.500}"}},contrast:{background:"{surface.900}",borderColor:"{surface.950}",color:"{surface.50}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.950}, transparent 96%)",closeButton:{hoverBackground:"{surface.800}",focusRing:{color:"{surface.50}",shadow:"none"}},outlined:{color:"{surface.950}",borderColor:"{surface.950}"},simple:{color:"{surface.950}"}}},dark:{info:{background:"color-mix(in srgb, {blue.500}, transparent 84%)",borderColor:"color-mix(in srgb, {blue.700}, transparent 64%)",color:"{blue.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {blue.500}, transparent 96%)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{blue.500}",shadow:"none"}},outlined:{color:"{blue.500}",borderColor:"{blue.500}"},simple:{color:"{blue.500}"}},success:{background:"color-mix(in srgb, {green.500}, transparent 84%)",borderColor:"color-mix(in srgb, {green.700}, transparent 64%)",color:"{green.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {green.500}, transparent 96%)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{green.500}",shadow:"none"}},outlined:{color:"{green.500}",borderColor:"{green.500}"},simple:{color:"{green.500}"}},warn:{background:"color-mix(in srgb, {yellow.500}, transparent 84%)",borderColor:"color-mix(in srgb, {yellow.700}, transparent 64%)",color:"{yellow.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {yellow.500}, transparent 96%)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{yellow.500}",shadow:"none"}},outlined:{color:"{yellow.500}",borderColor:"{yellow.500}"},simple:{color:"{yellow.500}"}},error:{background:"color-mix(in srgb, {red.500}, transparent 84%)",borderColor:"color-mix(in srgb, {red.700}, transparent 64%)",color:"{red.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {red.500}, transparent 96%)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{red.500}",shadow:"none"}},outlined:{color:"{red.500}",borderColor:"{red.500}"},simple:{color:"{red.500}"}},secondary:{background:"{surface.800}",borderColor:"{surface.700}",color:"{surface.300}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.500}, transparent 96%)",closeButton:{hoverBackground:"{surface.700}",focusRing:{color:"{surface.300}",shadow:"none"}},outlined:{color:"{surface.400}",borderColor:"{surface.400}"},simple:{color:"{surface.400}"}},contrast:{background:"{surface.0}",borderColor:"{surface.100}",color:"{surface.950}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.950}, transparent 96%)",closeButton:{hoverBackground:"{surface.100}",focusRing:{color:"{surface.950}",shadow:"none"}},outlined:{color:"{surface.0}",borderColor:"{surface.0}"},simple:{color:"{surface.0}"}}}}};var na={root:{borderRadius:"{content.border.radius}",gap:"1rem"},meters:{background:"{content.border.color}",size:"0.5rem"},label:{gap:"0.5rem"},labelMarker:{size:"0.5rem"},labelIcon:{size:"1rem"},labelList:{verticalGap:"0.5rem",horizontalGap:"1rem"}};var aa={root:{background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledHoverBackground:"{form.field.filled.hover.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",invalidPlaceholderColor:"{form.field.invalid.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}",sm:{fontSize:"{form.field.sm.font.size}",paddingX:"{form.field.sm.padding.x}",paddingY:"{form.field.sm.padding.y}"},lg:{fontSize:"{form.field.lg.font.size}",paddingX:"{form.field.lg.padding.x}",paddingY:"{form.field.lg.padding.y}"}},dropdown:{width:"2.5rem",color:"{form.field.icon.color}"},overlay:{background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}"},list:{padding:"{list.padding}",gap:"{list.gap}",header:{padding:"{list.header.padding}"}},option:{focusBackground:"{list.option.focus.background}",selectedBackground:"{list.option.selected.background}",selectedFocusBackground:"{list.option.selected.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",selectedColor:"{list.option.selected.color}",selectedFocusColor:"{list.option.selected.focus.color}",padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}",gap:"0.5rem"},optionGroup:{background:"{list.option.group.background}",color:"{list.option.group.color}",fontWeight:"{list.option.group.font.weight}",padding:"{list.option.group.padding}"},clearIcon:{color:"{form.field.icon.color}"},chip:{borderRadius:"{border.radius.sm}"},emptyMessage:{padding:"{list.option.padding}"}};var sa={root:{gap:"1.125rem"},controls:{gap:"0.5rem"}};var la={root:{gutter:"0.75rem",transitionDuration:"{transition.duration}"},node:{background:"{content.background}",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",borderColor:"{content.border.color}",color:"{content.color}",selectedColor:"{highlight.color}",hoverColor:"{content.hover.color}",padding:"0.75rem 1rem",toggleablePadding:"0.75rem 1rem 1.25rem 1rem",borderRadius:"{content.border.radius}"},nodeToggleButton:{background:"{content.background}",hoverBackground:"{content.hover.background}",borderColor:"{content.border.color}",color:"{text.muted.color}",hoverColor:"{text.color}",size:"1.5rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},connector:{color:"{content.border.color}",borderRadius:"{content.border.radius}",height:"24px"}};var ca={root:{outline:{width:"2px",color:"{content.background}"}}};var da={root:{padding:"0.5rem 1rem",gap:"0.25rem",borderRadius:"{content.border.radius}",background:"{content.background}",color:"{content.color}",transitionDuration:"{transition.duration}"},navButton:{background:"transparent",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}",selectedColor:"{highlight.color}",width:"2.5rem",height:"2.5rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},currentPageReport:{color:"{text.muted.color}"},jumpToPageInput:{maxWidth:"2.5rem"}};var ua={root:{background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}"},header:{background:"transparent",color:"{text.color}",padding:"1.125rem",borderColor:"{content.border.color}",borderWidth:"0",borderRadius:"0"},toggleableHeader:{padding:"0.375rem 1.125rem"},title:{fontWeight:"600"},content:{padding:"0 1.125rem 1.125rem 1.125rem"},footer:{padding:"0 1.125rem 1.125rem 1.125rem"}};var pa={root:{gap:"0.5rem",transitionDuration:"{transition.duration}"},panel:{background:"{content.background}",borderColor:"{content.border.color}",borderWidth:"1px",color:"{content.color}",padding:"0.25rem 0.25rem",borderRadius:"{content.border.radius}",first:{borderWidth:"1px",topBorderRadius:"{content.border.radius}"},last:{borderWidth:"1px",bottomBorderRadius:"{content.border.radius}"}},item:{focusBackground:"{navigation.item.focus.background}",color:"{navigation.item.color}",focusColor:"{navigation.item.focus.color}",gap:"0.5rem",padding:"{navigation.item.padding}",borderRadius:"{content.border.radius}",icon:{color:"{navigation.item.icon.color}",focusColor:"{navigation.item.icon.focus.color}"}},submenu:{indent:"1rem"},submenuIcon:{color:"{navigation.submenu.icon.color}",focusColor:"{navigation.submenu.icon.focus.color}"}};var ma={meter:{background:"{content.border.color}",borderRadius:"{content.border.radius}",height:".75rem"},icon:{color:"{form.field.icon.color}"},overlay:{background:"{overlay.popover.background}",borderColor:"{overlay.popover.border.color}",borderRadius:"{overlay.popover.border.radius}",color:"{overlay.popover.color}",padding:"{overlay.popover.padding}",shadow:"{overlay.popover.shadow}"},content:{gap:"0.5rem"},colorScheme:{light:{strength:{weakBackground:"{red.500}",mediumBackground:"{amber.500}",strongBackground:"{green.500}"}},dark:{strength:{weakBackground:"{red.400}",mediumBackground:"{amber.400}",strongBackground:"{green.400}"}}}};var fa={root:{gap:"1.125rem"},controls:{gap:"0.5rem"}};var ha={root:{background:"{overlay.popover.background}",borderColor:"{overlay.popover.border.color}",color:"{overlay.popover.color}",borderRadius:"{overlay.popover.border.radius}",shadow:"{overlay.popover.shadow}",gutter:"10px",arrowOffset:"1.25rem"},content:{padding:"{overlay.popover.padding}"}};var ga={root:{background:"{content.border.color}",borderRadius:"{content.border.radius}",height:"1.25rem"},value:{background:"{primary.color}"},label:{color:"{primary.contrast.color}",fontSize:"0.75rem",fontWeight:"600"}};var ba={colorScheme:{light:{root:{colorOne:"{red.500}",colorTwo:"{blue.500}",colorThree:"{green.500}",colorFour:"{yellow.500}"}},dark:{root:{colorOne:"{red.400}",colorTwo:"{blue.400}",colorThree:"{green.400}",colorFour:"{yellow.400}"}}}};var _a={root:{width:"1.25rem",height:"1.25rem",background:"{form.field.background}",checkedBackground:"{primary.color}",checkedHoverBackground:"{primary.hover.color}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.border.color}",checkedBorderColor:"{primary.color}",checkedHoverBorderColor:"{primary.hover.color}",checkedFocusBorderColor:"{primary.color}",checkedDisabledBorderColor:"{form.field.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",shadow:"{form.field.shadow}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}",sm:{width:"1rem",height:"1rem"},lg:{width:"1.5rem",height:"1.5rem"}},icon:{size:"0.75rem",checkedColor:"{primary.contrast.color}",checkedHoverColor:"{primary.contrast.color}",disabledColor:"{form.field.disabled.color}",sm:{size:"0.5rem"},lg:{size:"1rem"}}};var va={root:{gap:"0.25rem",transitionDuration:"{transition.duration}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},icon:{size:"1rem",color:"{text.muted.color}",hoverColor:"{primary.color}",activeColor:"{primary.color}"}};var ya={colorScheme:{light:{root:{background:"rgba(0,0,0,0.1)"}},dark:{root:{background:"rgba(255,255,255,0.3)"}}}};var ka={root:{transitionDuration:"{transition.duration}"},bar:{size:"9px",borderRadius:"{border.radius.sm}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},colorScheme:{light:{bar:{background:"{surface.100}"}},dark:{bar:{background:"{surface.800}"}}}};var wa={root:{background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledHoverBackground:"{form.field.filled.hover.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",invalidPlaceholderColor:"{form.field.invalid.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}",sm:{fontSize:"{form.field.sm.font.size}",paddingX:"{form.field.sm.padding.x}",paddingY:"{form.field.sm.padding.y}"},lg:{fontSize:"{form.field.lg.font.size}",paddingX:"{form.field.lg.padding.x}",paddingY:"{form.field.lg.padding.y}"}},dropdown:{width:"2.5rem",color:"{form.field.icon.color}"},overlay:{background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}"},list:{padding:"{list.padding}",gap:"{list.gap}",header:{padding:"{list.header.padding}"}},option:{focusBackground:"{list.option.focus.background}",selectedBackground:"{list.option.selected.background}",selectedFocusBackground:"{list.option.selected.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",selectedColor:"{list.option.selected.color}",selectedFocusColor:"{list.option.selected.focus.color}",padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}"},optionGroup:{background:"{list.option.group.background}",color:"{list.option.group.color}",fontWeight:"{list.option.group.font.weight}",padding:"{list.option.group.padding}"},clearIcon:{color:"{form.field.icon.color}"},checkmark:{color:"{list.option.color}",gutterStart:"-0.375rem",gutterEnd:"0.375rem"},emptyMessage:{padding:"{list.option.padding}"}};var Ca={root:{borderRadius:"{form.field.border.radius}"},colorScheme:{light:{root:{invalidBorderColor:"{form.field.invalid.border.color}"}},dark:{root:{invalidBorderColor:"{form.field.invalid.border.color}"}}}};var xa={root:{borderRadius:"{content.border.radius}"},colorScheme:{light:{root:{background:"{surface.200}",animationBackground:"rgba(255,255,255,0.4)"}},dark:{root:{background:"rgba(255, 255, 255, 0.06)",animationBackground:"rgba(255, 255, 255, 0.04)"}}}};var Ia={root:{transitionDuration:"{transition.duration}"},track:{background:"{content.border.color}",borderRadius:"{content.border.radius}",size:"3px"},range:{background:"{primary.color}"},handle:{width:"20px",height:"20px",borderRadius:"50%",background:"{content.border.color}",hoverBackground:"{content.border.color}",content:{borderRadius:"50%",hoverBackground:"{content.background}",width:"16px",height:"16px",shadow:"0px 0.5px 0px 0px rgba(0, 0, 0, 0.08), 0px 1px 1px 0px rgba(0, 0, 0, 0.14)"},focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},colorScheme:{light:{handle:{content:{background:"{surface.0}"}}},dark:{handle:{content:{background:"{surface.950}"}}}}};var Sa={root:{gap:"0.5rem",transitionDuration:"{transition.duration}"}};var Ta={root:{borderRadius:"{form.field.border.radius}",roundedBorderRadius:"2rem",raisedShadow:"0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12)"}};var Ea={root:{background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",transitionDuration:"{transition.duration}"},gutter:{background:"{content.border.color}"},handle:{size:"24px",background:"transparent",borderRadius:"{content.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}}};var Ma={root:{transitionDuration:"{transition.duration}"},separator:{background:"{content.border.color}",activeBackground:"{primary.color}",margin:"0 0 0 1.625rem",size:"2px"},step:{padding:"0.5rem",gap:"1rem"},stepHeader:{padding:"0",borderRadius:"{content.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"},gap:"0.5rem"},stepTitle:{color:"{text.muted.color}",activeColor:"{primary.color}",fontWeight:"500"},stepNumber:{background:"{content.background}",activeBackground:"{content.background}",borderColor:"{content.border.color}",activeBorderColor:"{content.border.color}",color:"{text.muted.color}",activeColor:"{primary.color}",size:"2rem",fontSize:"1.143rem",fontWeight:"500",borderRadius:"50%",shadow:"0px 0.5px 0px 0px rgba(0, 0, 0, 0.06), 0px 1px 1px 0px rgba(0, 0, 0, 0.12)"},steppanels:{padding:"0.875rem 0.5rem 1.125rem 0.5rem"},steppanel:{background:"{content.background}",color:"{content.color}",padding:"0",indent:"1rem"}};var Oa={root:{transitionDuration:"{transition.duration}"},separator:{background:"{content.border.color}"},itemLink:{borderRadius:"{content.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"},gap:"0.5rem"},itemLabel:{color:"{text.muted.color}",activeColor:"{primary.color}",fontWeight:"500"},itemNumber:{background:"{content.background}",activeBackground:"{content.background}",borderColor:"{content.border.color}",activeBorderColor:"{content.border.color}",color:"{text.muted.color}",activeColor:"{primary.color}",size:"2rem",fontSize:"1.143rem",fontWeight:"500",borderRadius:"50%",shadow:"0px 0.5px 0px 0px rgba(0, 0, 0, 0.06), 0px 1px 1px 0px rgba(0, 0, 0, 0.12)"}};var Ba={root:{transitionDuration:"{transition.duration}"},tablist:{borderWidth:"0 0 1px 0",background:"{content.background}",borderColor:"{content.border.color}"},item:{background:"transparent",hoverBackground:"transparent",activeBackground:"transparent",borderWidth:"0 0 1px 0",borderColor:"{content.border.color}",hoverBorderColor:"{content.border.color}",activeBorderColor:"{primary.color}",color:"{text.muted.color}",hoverColor:"{text.color}",activeColor:"{primary.color}",padding:"1rem 1.125rem",fontWeight:"600",margin:"0 0 -1px 0",gap:"0.5rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},itemIcon:{color:"{text.muted.color}",hoverColor:"{text.color}",activeColor:"{primary.color}"},activeBar:{height:"1px",bottom:"-1px",background:"{primary.color}"}};var Da={root:{transitionDuration:"{transition.duration}"},tablist:{borderWidth:"0 0 1px 0",background:"{content.background}",borderColor:"{content.border.color}"},tab:{background:"transparent",hoverBackground:"transparent",activeBackground:"transparent",borderWidth:"0 0 1px 0",borderColor:"{content.border.color}",hoverBorderColor:"{content.border.color}",activeBorderColor:"{primary.color}",color:"{text.muted.color}",hoverColor:"{text.color}",activeColor:"{primary.color}",padding:"1rem 1.125rem",fontWeight:"600",margin:"0 0 -1px 0",gap:"0.5rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"}},tabpanel:{background:"{content.background}",color:"{content.color}",padding:"0.875rem 1.125rem 1.125rem 1.125rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"inset {focus.ring.shadow}"}},navButton:{background:"{content.background}",color:"{text.muted.color}",hoverColor:"{text.color}",width:"2.5rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"}},activeBar:{height:"1px",bottom:"-1px",background:"{primary.color}"},colorScheme:{light:{navButton:{shadow:"0px 0px 10px 50px rgba(255, 255, 255, 0.6)"}},dark:{navButton:{shadow:"0px 0px 10px 50px color-mix(in srgb, {content.background}, transparent 50%)"}}}};var Va={root:{transitionDuration:"{transition.duration}"},tabList:{background:"{content.background}",borderColor:"{content.border.color}"},tab:{borderColor:"{content.border.color}",activeBorderColor:"{primary.color}",color:"{text.muted.color}",hoverColor:"{text.color}",activeColor:"{primary.color}"},tabPanel:{background:"{content.background}",color:"{content.color}"},navButton:{background:"{content.background}",color:"{text.muted.color}",hoverColor:"{text.color}"},colorScheme:{light:{navButton:{shadow:"0px 0px 10px 50px rgba(255, 255, 255, 0.6)"}},dark:{navButton:{shadow:"0px 0px 10px 50px color-mix(in srgb, {content.background}, transparent 50%)"}}}};var Fa={root:{fontSize:"0.875rem",fontWeight:"700",padding:"0.25rem 0.5rem",gap:"0.25rem",borderRadius:"{content.border.radius}",roundedBorderRadius:"{border.radius.xl}"},icon:{size:"0.75rem"},colorScheme:{light:{primary:{background:"{primary.100}",color:"{primary.700}"},secondary:{background:"{surface.100}",color:"{surface.600}"},success:{background:"{green.100}",color:"{green.700}"},info:{background:"{sky.100}",color:"{sky.700}"},warn:{background:"{orange.100}",color:"{orange.700}"},danger:{background:"{red.100}",color:"{red.700}"},contrast:{background:"{surface.950}",color:"{surface.0}"}},dark:{primary:{background:"color-mix(in srgb, {primary.500}, transparent 84%)",color:"{primary.300}"},secondary:{background:"{surface.800}",color:"{surface.300}"},success:{background:"color-mix(in srgb, {green.500}, transparent 84%)",color:"{green.300}"},info:{background:"color-mix(in srgb, {sky.500}, transparent 84%)",color:"{sky.300}"},warn:{background:"color-mix(in srgb, {orange.500}, transparent 84%)",color:"{orange.300}"},danger:{background:"color-mix(in srgb, {red.500}, transparent 84%)",color:"{red.300}"},contrast:{background:"{surface.0}",color:"{surface.950}"}}}};var La={root:{background:"{form.field.background}",borderColor:"{form.field.border.color}",color:"{form.field.color}",height:"18rem",padding:"{form.field.padding.y} {form.field.padding.x}",borderRadius:"{form.field.border.radius}"},prompt:{gap:"0.25rem"},commandResponse:{margin:"2px 0"}};var Pa={root:{background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledHoverBackground:"{form.field.filled.hover.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",invalidPlaceholderColor:"{form.field.invalid.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}",sm:{fontSize:"{form.field.sm.font.size}",paddingX:"{form.field.sm.padding.x}",paddingY:"{form.field.sm.padding.y}"},lg:{fontSize:"{form.field.lg.font.size}",paddingX:"{form.field.lg.padding.x}",paddingY:"{form.field.lg.padding.y}"}}};var Aa={root:{background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}",shadow:"{overlay.navigation.shadow}",transitionDuration:"{transition.duration}"},list:{padding:"{navigation.list.padding}",gap:"{navigation.list.gap}"},item:{focusBackground:"{navigation.item.focus.background}",activeBackground:"{navigation.item.active.background}",color:"{navigation.item.color}",focusColor:"{navigation.item.focus.color}",activeColor:"{navigation.item.active.color}",padding:"{navigation.item.padding}",borderRadius:"{navigation.item.border.radius}",gap:"{navigation.item.gap}",icon:{color:"{navigation.item.icon.color}",focusColor:"{navigation.item.icon.focus.color}",activeColor:"{navigation.item.icon.active.color}"}},submenu:{mobileIndent:"1rem"},submenuIcon:{size:"{navigation.submenu.icon.size}",color:"{navigation.submenu.icon.color}",focusColor:"{navigation.submenu.icon.focus.color}",activeColor:"{navigation.submenu.icon.active.color}"},separator:{borderColor:"{content.border.color}"}};var Ra={event:{minHeight:"5rem"},horizontal:{eventContent:{padding:"1rem 0"}},vertical:{eventContent:{padding:"0 1rem"}},eventMarker:{size:"1.125rem",borderRadius:"50%",borderWidth:"2px",background:"{content.background}",borderColor:"{content.border.color}",content:{borderRadius:"50%",size:"0.375rem",background:"{primary.color}",insetShadow:"0px 0.5px 0px 0px rgba(0, 0, 0, 0.06), 0px 1px 1px 0px rgba(0, 0, 0, 0.12)"}},eventConnector:{color:"{content.border.color}",size:"2px"}};var Ha={root:{width:"25rem",borderRadius:"{content.border.radius}",borderWidth:"1px",transitionDuration:"{transition.duration}"},icon:{size:"1.125rem"},content:{padding:"{overlay.popover.padding}",gap:"0.5rem"},text:{gap:"0.5rem"},summary:{fontWeight:"500",fontSize:"1rem"},detail:{fontWeight:"500",fontSize:"0.875rem"},closeButton:{width:"1.75rem",height:"1.75rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",offset:"{focus.ring.offset}"}},closeIcon:{size:"1rem"},colorScheme:{light:{root:{blur:"1.5px"},info:{background:"color-mix(in srgb, {blue.50}, transparent 5%)",borderColor:"{blue.200}",color:"{blue.600}",detailColor:"{surface.700}",shadow:"0px 4px 8px 0px color-mix(in srgb, {blue.500}, transparent 96%)",closeButton:{hoverBackground:"{blue.100}",focusRing:{color:"{blue.600}",shadow:"none"}}},success:{background:"color-mix(in srgb, {green.50}, transparent 5%)",borderColor:"{green.200}",color:"{green.600}",detailColor:"{surface.700}",shadow:"0px 4px 8px 0px color-mix(in srgb, {green.500}, transparent 96%)",closeButton:{hoverBackground:"{green.100}",focusRing:{color:"{green.600}",shadow:"none"}}},warn:{background:"color-mix(in srgb,{yellow.50}, transparent 5%)",borderColor:"{yellow.200}",color:"{yellow.600}",detailColor:"{surface.700}",shadow:"0px 4px 8px 0px color-mix(in srgb, {yellow.500}, transparent 96%)",closeButton:{hoverBackground:"{yellow.100}",focusRing:{color:"{yellow.600}",shadow:"none"}}},error:{background:"color-mix(in srgb, {red.50}, transparent 5%)",borderColor:"{red.200}",color:"{red.600}",detailColor:"{surface.700}",shadow:"0px 4px 8px 0px color-mix(in srgb, {red.500}, transparent 96%)",closeButton:{hoverBackground:"{red.100}",focusRing:{color:"{red.600}",shadow:"none"}}},secondary:{background:"{surface.100}",borderColor:"{surface.200}",color:"{surface.600}",detailColor:"{surface.700}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.500}, transparent 96%)",closeButton:{hoverBackground:"{surface.200}",focusRing:{color:"{surface.600}",shadow:"none"}}},contrast:{background:"{surface.900}",borderColor:"{surface.950}",color:"{surface.50}",detailColor:"{surface.0}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.950}, transparent 96%)",closeButton:{hoverBackground:"{surface.800}",focusRing:{color:"{surface.50}",shadow:"none"}}}},dark:{root:{blur:"10px"},info:{background:"color-mix(in srgb, {blue.500}, transparent 84%)",borderColor:"color-mix(in srgb, {blue.700}, transparent 64%)",color:"{blue.500}",detailColor:"{surface.0}",shadow:"0px 4px 8px 0px color-mix(in srgb, {blue.500}, transparent 96%)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{blue.500}",shadow:"none"}}},success:{background:"color-mix(in srgb, {green.500}, transparent 84%)",borderColor:"color-mix(in srgb, {green.700}, transparent 64%)",color:"{green.500}",detailColor:"{surface.0}",shadow:"0px 4px 8px 0px color-mix(in srgb, {green.500}, transparent 96%)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{green.500}",shadow:"none"}}},warn:{background:"color-mix(in srgb, {yellow.500}, transparent 84%)",borderColor:"color-mix(in srgb, {yellow.700}, transparent 64%)",color:"{yellow.500}",detailColor:"{surface.0}",shadow:"0px 4px 8px 0px color-mix(in srgb, {yellow.500}, transparent 96%)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{yellow.500}",shadow:"none"}}},error:{background:"color-mix(in srgb, {red.500}, transparent 84%)",borderColor:"color-mix(in srgb, {red.700}, transparent 64%)",color:"{red.500}",detailColor:"{surface.0}",shadow:"0px 4px 8px 0px color-mix(in srgb, {red.500}, transparent 96%)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{red.500}",shadow:"none"}}},secondary:{background:"{surface.800}",borderColor:"{surface.700}",color:"{surface.300}",detailColor:"{surface.0}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.500}, transparent 96%)",closeButton:{hoverBackground:"{surface.700}",focusRing:{color:"{surface.300}",shadow:"none"}}},contrast:{background:"{surface.0}",borderColor:"{surface.100}",color:"{surface.950}",detailColor:"{surface.950}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.950}, transparent 96%)",closeButton:{hoverBackground:"{surface.100}",focusRing:{color:"{surface.950}",shadow:"none"}}}}}};var za={root:{padding:"0.25rem",borderRadius:"{content.border.radius}",gap:"0.5rem",fontWeight:"500",disabledBackground:"{form.field.disabled.background}",disabledBorderColor:"{form.field.disabled.background}",disabledColor:"{form.field.disabled.color}",invalidBorderColor:"{form.field.invalid.border.color}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}",sm:{fontSize:"{form.field.sm.font.size}",padding:"0.25rem"},lg:{fontSize:"{form.field.lg.font.size}",padding:"0.25rem"}},icon:{disabledColor:"{form.field.disabled.color}"},content:{padding:"0.25rem 0.75rem",borderRadius:"{content.border.radius}",checkedShadow:"0px 1px 2px 0px rgba(0, 0, 0, 0.02), 0px 1px 2px 0px rgba(0, 0, 0, 0.04)",sm:{padding:"0.25rem 0.75rem"},lg:{padding:"0.25rem 0.75rem"}},colorScheme:{light:{root:{background:"{surface.100}",checkedBackground:"{surface.100}",hoverBackground:"{surface.100}",borderColor:"{surface.100}",color:"{surface.500}",hoverColor:"{surface.700}",checkedColor:"{surface.900}",checkedBorderColor:"{surface.100}"},content:{checkedBackground:"{surface.0}"},icon:{color:"{surface.500}",hoverColor:"{surface.700}",checkedColor:"{surface.900}"}},dark:{root:{background:"{surface.950}",checkedBackground:"{surface.950}",hoverBackground:"{surface.950}",borderColor:"{surface.950}",color:"{surface.400}",hoverColor:"{surface.300}",checkedColor:"{surface.0}",checkedBorderColor:"{surface.950}"},content:{checkedBackground:"{surface.800}"},icon:{color:"{surface.400}",hoverColor:"{surface.300}",checkedColor:"{surface.0}"}}}};var $a={root:{width:"2.5rem",height:"1.5rem",borderRadius:"30px",gap:"0.25rem",shadow:"{form.field.shadow}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"},borderWidth:"1px",borderColor:"transparent",hoverBorderColor:"transparent",checkedBorderColor:"transparent",checkedHoverBorderColor:"transparent",invalidBorderColor:"{form.field.invalid.border.color}",transitionDuration:"{form.field.transition.duration}",slideDuration:"0.2s"},handle:{borderRadius:"50%",size:"1rem"},colorScheme:{light:{root:{background:"{surface.300}",disabledBackground:"{form.field.disabled.background}",hoverBackground:"{surface.400}",checkedBackground:"{primary.color}",checkedHoverBackground:"{primary.hover.color}"},handle:{background:"{surface.0}",disabledBackground:"{form.field.disabled.color}",hoverBackground:"{surface.0}",checkedBackground:"{surface.0}",checkedHoverBackground:"{surface.0}",color:"{text.muted.color}",hoverColor:"{text.color}",checkedColor:"{primary.color}",checkedHoverColor:"{primary.hover.color}"}},dark:{root:{background:"{surface.700}",disabledBackground:"{surface.600}",hoverBackground:"{surface.600}",checkedBackground:"{primary.color}",checkedHoverBackground:"{primary.hover.color}"},handle:{background:"{surface.400}",disabledBackground:"{surface.900}",hoverBackground:"{surface.300}",checkedBackground:"{surface.900}",checkedHoverBackground:"{surface.900}",color:"{surface.900}",hoverColor:"{surface.800}",checkedColor:"{primary.color}",checkedHoverColor:"{primary.hover.color}"}}}};var Na={root:{background:"{content.background}",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}",color:"{content.color}",gap:"0.5rem",padding:"0.75rem"}};var Ua={root:{maxWidth:"12.5rem",gutter:"0.25rem",shadow:"{overlay.popover.shadow}",padding:"0.5rem 0.75rem",borderRadius:"{overlay.popover.border.radius}"},colorScheme:{light:{root:{background:"{surface.700}",color:"{surface.0}"}},dark:{root:{background:"{surface.700}",color:"{surface.0}"}}}};var ja={root:{background:"{content.background}",color:"{content.color}",padding:"1rem",gap:"2px",indent:"1rem",transitionDuration:"{transition.duration}"},node:{padding:"0.25rem 0.5rem",borderRadius:"{content.border.radius}",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",color:"{text.color}",hoverColor:"{text.hover.color}",selectedColor:"{highlight.color}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"},gap:"0.25rem"},nodeIcon:{color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}",selectedColor:"{highlight.color}"},nodeToggleButton:{borderRadius:"50%",size:"1.75rem",hoverBackground:"{content.hover.background}",selectedHoverBackground:"{content.background}",color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}",selectedHoverColor:"{primary.color}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},loadingIcon:{size:"2rem"},filter:{margin:"0 0 0.5rem 0"}};var Ka={root:{background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledHoverBackground:"{form.field.filled.hover.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",invalidPlaceholderColor:"{form.field.invalid.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}",sm:{fontSize:"{form.field.sm.font.size}",paddingX:"{form.field.sm.padding.x}",paddingY:"{form.field.sm.padding.y}"},lg:{fontSize:"{form.field.lg.font.size}",paddingX:"{form.field.lg.padding.x}",paddingY:"{form.field.lg.padding.y}"}},dropdown:{width:"2.5rem",color:"{form.field.icon.color}"},overlay:{background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}"},tree:{padding:"{list.padding}"},clearIcon:{color:"{form.field.icon.color}"},emptyMessage:{padding:"{list.option.padding}"},chip:{borderRadius:"{border.radius.sm}"}};var Wa={root:{transitionDuration:"{transition.duration}"},header:{background:"{content.background}",borderColor:"{treetable.border.color}",color:"{content.color}",borderWidth:"0 0 1px 0",padding:"0.75rem 1rem"},headerCell:{background:"{content.background}",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",borderColor:"{treetable.border.color}",color:"{content.color}",hoverColor:"{content.hover.color}",selectedColor:"{highlight.color}",gap:"0.5rem",padding:"0.75rem 1rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"}},columnTitle:{fontWeight:"600"},row:{background:"{content.background}",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",color:"{content.color}",hoverColor:"{content.hover.color}",selectedColor:"{highlight.color}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"}},bodyCell:{borderColor:"{treetable.border.color}",padding:"0.75rem 1rem",gap:"0.5rem"},footerCell:{background:"{content.background}",borderColor:"{treetable.border.color}",color:"{content.color}",padding:"0.75rem 1rem"},columnFooter:{fontWeight:"600"},footer:{background:"{content.background}",borderColor:"{treetable.border.color}",color:"{content.color}",borderWidth:"0 0 1px 0",padding:"0.75rem 1rem"},columnResizer:{width:"0.5rem"},resizeIndicator:{width:"1px",color:"{primary.color}"},sortIcon:{color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}",size:"0.875rem"},loadingIcon:{size:"2rem"},nodeToggleButton:{hoverBackground:"{content.hover.background}",selectedHoverBackground:"{content.background}",color:"{text.muted.color}",hoverColor:"{text.color}",selectedHoverColor:"{primary.color}",size:"1.75rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},paginatorTop:{borderColor:"{content.border.color}",borderWidth:"0 0 1px 0"},paginatorBottom:{borderColor:"{content.border.color}",borderWidth:"0 0 1px 0"},colorScheme:{light:{root:{borderColor:"{content.border.color}"},bodyCell:{selectedBorderColor:"{primary.100}"}},dark:{root:{borderColor:"{surface.800}"},bodyCell:{selectedBorderColor:"{primary.900}"}}}};var Qa={loader:{mask:{background:"{content.background}",color:"{text.muted.color}"},icon:{size:"2rem"}}};var ar=ze(se({},bn),{components:{accordion:mn,autocomplete:fn,avatar:hn,badge:gn,blockui:_n,breadcrumb:vn,button:yn,datepicker:Dn,card:kn,carousel:wn,cascadeselect:Cn,checkbox:xn,chip:In,colorpicker:Sn,confirmdialog:Tn,confirmpopup:En,contextmenu:Mn,dataview:Bn,datatable:On,dialog:Vn,divider:Fn,dock:Ln,drawer:Pn,editor:An,fieldset:Rn,fileupload:Hn,iftalabel:Un,floatlabel:zn,galleria:$n,iconfield:Nn,image:jn,imagecompare:Kn,inlinemessage:Wn,inplace:Qn,inputchips:qn,inputgroup:Zn,inputnumber:Gn,inputotp:Yn,inputtext:Xn,knob:Jn,listbox:ea,megamenu:ta,menu:oa,menubar:ra,message:ia,metergroup:na,multiselect:aa,orderlist:sa,organizationchart:la,overlaybadge:ca,popover:ha,paginator:da,password:ma,panel:ua,panelmenu:pa,picklist:fa,progressbar:ga,progressspinner:ba,radiobutton:_a,rating:va,scrollpanel:ka,select:wa,selectbutton:Ca,skeleton:xa,slider:Ia,speeddial:Sa,splitter:Ea,splitbutton:Ta,stepper:Ma,steps:Oa,tabmenu:Ba,tabs:Da,tabview:Va,textarea:Pa,tieredmenu:Aa,tag:Fa,terminal:La,timeline:Ra,togglebutton:za,toggleswitch:$a,tree:ja,treeselect:Ka,treetable:Wa,toast:Ha,toolbar:Na,virtualscroller:Qa,tooltip:Ua,ripple:ya}});var Kt=Ko(ar,{semantic:{primary:{50:"#ffe4c7",100:"{orange.100}",200:"#d1d5db",300:"{orange.300}",400:"#6b7280",500:"#6b7280",600:"{orange.600}",700:"{orange.700}",800:"{orange.800}",900:"{orange.900}",950:"{orange.950}"},colorScheme:{light:{surface:{0:"#FFFFFF",50:"#ffe4c7",100:"#ffe4c7",200:"#d1d5db",300:"#d1d5db",400:"#6b7280",500:"#6b7280",600:"{orange.600}",700:"#6b7280",800:"{orange.800}",900:"{orange.900}",950:"{orange.950}"}}}}}),qa=Ko(ar,{semantic:{primary:{50:"{green.50}",100:"{green.100}",200:"#d1d5db",300:"{green.300}",400:"#6b7280",500:"#6b7280",600:"{green.600}",700:"{green.700}",800:"{green.800}",900:"{green.900}",950:"{green.950}"},colorScheme:{light:{surface:{0:"#DCE9DD",50:"{green.50}",100:"#EDFFE8",200:"#d1d5db",300:"#d1d5db",400:"#6b7280",500:"#6b7280",600:"{green.600}",700:"#6b7280",800:"{green.800}",900:"{green.900}",950:"{green.950}"}}}}});var wo=class{validateSignature(i){return Promise.resolve(null)}validateAtHash(i){return Promise.resolve(!0)}},Co=class{};var Wt=class{},Us=(()=>{class t extends Wt{now(){return Date.now()}new(){return new Date}static{this.\u0275fac=(()=>{let e;return function(r){return(e||(e=j(t)))(r||t)}})()}static{this.\u0275prov=B({token:t,factory:t.\u0275fac})}}return t})();var xo=class{},Pt=class{},js=(()=>{class t{constructor(){this.data=new Map}getItem(e){return this.data.get(e)}removeItem(e){this.data.delete(e)}setItem(e,o){this.data.set(e,o)}static{this.\u0275fac=function(o){return new(o||t)}}static{this.\u0275prov=B({token:t,factory:t.\u0275fac})}}return t})();var Qt=class{constructor(i){this.type=i}},ge=class extends Qt{constructor(i,e=null){super(i),this.info=e}},Oe=class extends Qt{constructor(i,e=null){super(i),this.info=e}},K=class extends Qt{constructor(i,e,o=null){super(i),this.reason=e,this.params=o}};function Za(t){let i=t.replace(/-/g,"+").replace(/_/g,"/");return decodeURIComponent(atob(i).split("").map(function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)}).join(""))}function Ga(t){return btoa(t).replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}var Lt=class{constructor(i){this.clientId="",this.redirectUri="",this.postLogoutRedirectUri="",this.redirectUriAsPostLogoutRedirectUriFallback=!0,this.loginUrl="",this.scope="openid profile",this.resource="",this.rngUrl="",this.oidc=!0,this.requestAccessToken=!0,this.options=null,this.issuer="",this.logoutUrl="",this.clearHashAfterLogin=!0,this.tokenEndpoint=null,this.revocationEndpoint=null,this.customTokenParameters=[],this.userinfoEndpoint=null,this.responseType="",this.showDebugInformation=!1,this.silentRefreshRedirectUri="",this.silentRefreshMessagePrefix="",this.silentRefreshShowIFrame=!1,this.siletRefreshTimeout=1e3*20,this.silentRefreshTimeout=1e3*20,this.dummyClientSecret="",this.requireHttps="remoteOnly",this.strictDiscoveryDocumentValidation=!0,this.jwks=null,this.customQueryParams=null,this.silentRefreshIFrameName="angular-oauth-oidc-silent-refresh-iframe",this.timeoutFactor=.75,this.sessionChecksEnabled=!1,this.sessionCheckIntervall=3*1e3,this.sessionCheckIFrameUrl=null,this.sessionCheckIFrameName="angular-oauth-oidc-check-session-iframe",this.disableAtHashCheck=!1,this.skipSubjectCheck=!1,this.useIdTokenHintForSilentRefresh=!1,this.skipIssuerCheck=!1,this.nonceStateSeparator=";",this.useHttpBasicAuth=!1,this.decreaseExpirationBySec=0,this.waitForTokenInMsec=0,this.disablePKCE=!1,this.preserveRequestedRoute=!1,this.disableIdTokenTimer=!1,this.checkOrigin=!1,this.openUri=e=>{location.href=e},i&&Object.assign(this,i)}},pt=class{encodeKey(i){return encodeURIComponent(i)}encodeValue(i){return encodeURIComponent(i)}decodeKey(i){return decodeURIComponent(i)}decodeValue(i){return decodeURIComponent(i)}},Io=class{};var Ya=(()=>{class t{getHashFragmentParams(e){let o=e||window.location.hash;if(o=decodeURIComponent(o),o.indexOf("#")!==0)return{};let r=o.indexOf("?");return r>-1?o=o.substr(r+1):o=o.substr(1),this.parseQueryString(o)}parseQueryString(e){let o={},r,n,a,c,m,_;if(e===null)return o;let x=e.split("&");for(let b=0;b<x.length;b++)r=x[b],n=r.indexOf("="),n===-1?(a=r,c=null):(a=r.substr(0,n),c=r.substr(n+1)),m=decodeURIComponent(a),_=decodeURIComponent(c),m.substr(0,1)==="/"&&(m=m.substr(1)),o[m]=_;return o}static{this.\u0275fac=function(o){return new(o||t)}}static{this.\u0275prov=B({token:t,factory:t.\u0275fac})}}return t})(),Xa=32,Ks=64,Ws=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]);function sr(t,i,e,o,r){let n,a,c,m,_,x,b,M,C,L,ce,Je,Ye;for(;r>=64;){for(n=i[0],a=i[1],c=i[2],m=i[3],_=i[4],x=i[5],b=i[6],M=i[7],L=0;L<16;L++)ce=o+L*4,t[L]=(e[ce]&255)<<24|(e[ce+1]&255)<<16|(e[ce+2]&255)<<8|e[ce+3]&255;for(L=16;L<64;L++)C=t[L-2],Je=(C>>>17|C<<15)^(C>>>19|C<<13)^C>>>10,C=t[L-15],Ye=(C>>>7|C<<25)^(C>>>18|C<<14)^C>>>3,t[L]=(Je+t[L-7]|0)+(Ye+t[L-16]|0);for(L=0;L<64;L++)Je=(((_>>>6|_<<26)^(_>>>11|_<<21)^(_>>>25|_<<7))+(_&x^~_&b)|0)+(M+(Ws[L]+t[L]|0)|0)|0,Ye=((n>>>2|n<<30)^(n>>>13|n<<19)^(n>>>22|n<<10))+(n&a^n&c^a&c)|0,M=b,b=x,x=_,_=m+Je|0,m=c,c=a,a=n,n=Je+Ye|0;i[0]+=n,i[1]+=a,i[2]+=c,i[3]+=m,i[4]+=_,i[5]+=x,i[6]+=b,i[7]+=M,o+=64,r-=64}return o}var lr=class{constructor(){this.digestLength=Xa,this.blockSize=Ks,this.state=new Int32Array(8),this.temp=new Int32Array(64),this.buffer=new Uint8Array(128),this.bufferLength=0,this.bytesHashed=0,this.finished=!1,this.reset()}reset(){return this.state[0]=1779033703,this.state[1]=3144134277,this.state[2]=1013904242,this.state[3]=2773480762,this.state[4]=1359893119,this.state[5]=2600822924,this.state[6]=528734635,this.state[7]=1541459225,this.bufferLength=0,this.bytesHashed=0,this.finished=!1,this}clean(){for(let i=0;i<this.buffer.length;i++)this.buffer[i]=0;for(let i=0;i<this.temp.length;i++)this.temp[i]=0;this.reset()}update(i,e=i.length){if(this.finished)throw new Error("SHA256: can't update because hash was finished.");let o=0;if(this.bytesHashed+=e,this.bufferLength>0){for(;this.bufferLength<64&&e>0;)this.buffer[this.bufferLength++]=i[o++],e--;this.bufferLength===64&&(sr(this.temp,this.state,this.buffer,0,64),this.bufferLength=0)}for(e>=64&&(o=sr(this.temp,this.state,i,o,e),e%=64);e>0;)this.buffer[this.bufferLength++]=i[o++],e--;return this}finish(i){if(!this.finished){let e=this.bytesHashed,o=this.bufferLength,r=e/536870912|0,n=e<<3,a=e%64<56?64:128;this.buffer[o]=128;for(let c=o+1;c<a-8;c++)this.buffer[c]=0;this.buffer[a-8]=r>>>24&255,this.buffer[a-7]=r>>>16&255,this.buffer[a-6]=r>>>8&255,this.buffer[a-5]=r>>>0&255,this.buffer[a-4]=n>>>24&255,this.buffer[a-3]=n>>>16&255,this.buffer[a-2]=n>>>8&255,this.buffer[a-1]=n>>>0&255,sr(this.temp,this.state,this.buffer,0,a),this.finished=!0}for(let e=0;e<8;e++)i[e*4+0]=this.state[e]>>>24&255,i[e*4+1]=this.state[e]>>>16&255,i[e*4+2]=this.state[e]>>>8&255,i[e*4+3]=this.state[e]>>>0&255;return this}digest(){let i=new Uint8Array(this.digestLength);return this.finish(i),i}_saveState(i){for(let e=0;e<this.state.length;e++)i[e]=this.state[e]}_restoreState(i,e){for(let o=0;o<this.state.length;o++)this.state[o]=i[o];this.bytesHashed=e,this.finished=!1,this.bufferLength=0}};function Qs(t){let i=new lr().update(t),e=i.digest();return i.clean(),e}var jh=new Uint8Array(Xa);var So=class{};function qs(t){if(typeof t!="string")throw new TypeError("expected string");let i=t,e=new Uint8Array(i.length);for(let o=0;o<i.length;o++)e[o]=i.charCodeAt(o);return e}function Zs(t){let i=[];for(let e=0;e<t.length;e++)i.push(String.fromCharCode(t[e]));return i.join("")}var Gs=(()=>{class t{calcHash(e,o){return xe(this,null,function*(){return Zs(Qs(qs(e)))})}toHashString2(e){let o="";for(let r of e)o+=String.fromCharCode(r);return o}toHashString(e){let o=new Uint8Array(e),r="";for(let n of o)r+=String.fromCharCode(n);return r}static{this.\u0275fac=function(o){return new(o||t)}}static{this.\u0275prov=B({token:t,factory:t.\u0275fac})}}return t})(),Re=(()=>{class t extends Lt{constructor(e,o,r,n,a,c,m,_,x,b){super(),this.ngZone=e,this.http=o,this.config=a,this.urlHelper=c,this.logger=m,this.crypto=_,this.dateTimeService=b,this.discoveryDocumentLoaded=!1,this.state="",this.eventsSubject=new Xe,this.discoveryDocumentLoadedSubject=new Xe,this.grantTypesSupported=[],this.inImplicitFlow=!1,this.saveNoncesInLocalStorage=!1,this.debug("angular-oauth2-oidc v10"),this.document=x,a||(a={}),this.discoveryDocumentLoaded$=this.discoveryDocumentLoadedSubject.asObservable(),this.events=this.eventsSubject.asObservable(),n&&(this.tokenValidationHandler=n),a&&this.configure(a);try{r?this.setStorage(r):typeof sessionStorage<"u"&&this.setStorage(sessionStorage)}catch(M){console.error("No OAuthStorage provided and cannot access default (sessionStorage).Consider providing a custom OAuthStorage implementation in your module.",M)}if(this.checkLocalStorageAccessable()){let M=window?.navigator?.userAgent;(M?.includes("MSIE ")||M?.includes("Trident"))&&(this.saveNoncesInLocalStorage=!0)}this.setupRefreshTimer()}checkLocalStorageAccessable(){if(typeof window>"u")return!1;let e="test";try{return typeof window.localStorage>"u"?!1:(localStorage.setItem(e,e),localStorage.removeItem(e),!0)}catch{return!1}}configure(e){Object.assign(this,new Lt,e),this.config=Object.assign({},new Lt,e),this.sessionChecksEnabled&&this.setupSessionCheck(),this.configChanged()}configChanged(){this.setupRefreshTimer()}restartSessionChecksIfStillLoggedIn(){this.hasValidIdToken()&&this.initSessionCheck()}restartRefreshTimerIfStillLoggedIn(){this.setupExpirationTimers()}setupSessionCheck(){this.events.pipe($(e=>e.type==="token_received")).subscribe(()=>{this.initSessionCheck()})}setupAutomaticSilentRefresh(e={},o,r=!0){let n=!0;this.clearAutomaticRefreshTimer(),this.automaticRefreshSubscription=this.events.pipe(Fe(a=>{a.type==="token_received"?n=!0:a.type==="logout"&&(n=!1)}),$(a=>a.type==="token_expires"&&(o==null||o==="any"||a.info===o)),Lr(1e3)).subscribe(()=>{n&&this.refreshInternal(e,r).catch(()=>{this.debug("Automatic silent refresh did not work")})}),this.restartRefreshTimerIfStillLoggedIn()}refreshInternal(e,o){return!this.useSilentRefresh&&this.responseType==="code"?this.refreshToken():this.silentRefresh(e,o)}loadDiscoveryDocumentAndTryLogin(e=null){return this.loadDiscoveryDocument().then(()=>this.tryLogin(e))}loadDiscoveryDocumentAndLogin(e=null){return e=e||{},this.loadDiscoveryDocumentAndTryLogin(e).then(()=>{if(!this.hasValidIdToken()||!this.hasValidAccessToken()){let o=typeof e.state=="string"?e.state:"";return this.initLoginFlow(o),!1}else return!0})}debug(...e){this.showDebugInformation&&this.logger.debug(...e)}validateUrlFromDiscoveryDocument(e){let o=[],r=this.validateUrlForHttps(e),n=this.validateUrlAgainstIssuer(e);return r||o.push("https for all urls required. Also for urls received by discovery."),n||o.push("Every url in discovery document has to start with the issuer url.Also see property strictDiscoveryDocumentValidation."),o}validateUrlForHttps(e){if(!e)return!0;let o=e.toLowerCase();return this.requireHttps===!1||(o.match(/^http:\/\/localhost($|[:/])/)||o.match(/^http:\/\/localhost($|[:/])/))&&this.requireHttps==="remoteOnly"?!0:o.startsWith("https://")}assertUrlNotNullAndCorrectProtocol(e,o){if(!e)throw new Error(`'${o}' should not be null`);if(!this.validateUrlForHttps(e))throw new Error(`'${o}' must use HTTPS (with TLS), or config value for property 'requireHttps' must be set to 'false' and allow HTTP (without TLS).`)}validateUrlAgainstIssuer(e){return!this.strictDiscoveryDocumentValidation||!e?!0:e.toLowerCase().startsWith(this.issuer.toLowerCase())}setupRefreshTimer(){if(typeof window>"u"){this.debug("timer not supported on this plattform");return}(this.hasValidIdToken()||this.hasValidAccessToken())&&(this.clearAccessTokenTimer(),this.clearIdTokenTimer(),this.setupExpirationTimers()),this.tokenReceivedSubscription&&this.tokenReceivedSubscription.unsubscribe(),this.tokenReceivedSubscription=this.events.pipe($(e=>e.type==="token_received")).subscribe(()=>{this.clearAccessTokenTimer(),this.clearIdTokenTimer(),this.setupExpirationTimers()})}setupExpirationTimers(){this.hasValidAccessToken()&&this.setupAccessTokenTimer(),!this.disableIdTokenTimer&&this.hasValidIdToken()&&this.setupIdTokenTimer()}setupAccessTokenTimer(){let e=this.getAccessTokenExpiration(),o=this.getAccessTokenStoredAt(),r=this.calcTimeout(o,e);this.ngZone.runOutsideAngular(()=>{this.accessTokenTimeoutSubscription=be(new Oe("token_expires","access_token")).pipe(Jt(r)).subscribe(n=>{this.ngZone.run(()=>{this.eventsSubject.next(n)})})})}setupIdTokenTimer(){let e=this.getIdTokenExpiration(),o=this.getIdTokenStoredAt(),r=this.calcTimeout(o,e);this.ngZone.runOutsideAngular(()=>{this.idTokenTimeoutSubscription=be(new Oe("token_expires","id_token")).pipe(Jt(r)).subscribe(n=>{this.ngZone.run(()=>{this.eventsSubject.next(n)})})})}stopAutomaticRefresh(){this.clearAccessTokenTimer(),this.clearIdTokenTimer(),this.clearAutomaticRefreshTimer()}clearAccessTokenTimer(){this.accessTokenTimeoutSubscription&&this.accessTokenTimeoutSubscription.unsubscribe()}clearIdTokenTimer(){this.idTokenTimeoutSubscription&&this.idTokenTimeoutSubscription.unsubscribe()}clearAutomaticRefreshTimer(){this.automaticRefreshSubscription&&this.automaticRefreshSubscription.unsubscribe()}calcTimeout(e,o){let r=this.dateTimeService.now(),n=(o-e)*this.timeoutFactor-(r-e),a=Math.max(0,n),c=2147483647;return a>c?c:a}setStorage(e){this._storage=e,this.configChanged()}loadDiscoveryDocument(e=null){return new Promise((o,r)=>{if(e||(e=this.issuer||"",e.endsWith("/")||(e+="/"),e+=".well-known/openid-configuration"),!this.validateUrlForHttps(e)){r("issuer  must use HTTPS (with TLS), or config value for property 'requireHttps' must be set to 'false' and allow HTTP (without TLS).");return}this.http.get(e).subscribe(n=>{if(!this.validateDiscoveryDocument(n)){this.eventsSubject.next(new K("discovery_document_validation_error",null)),r("discovery_document_validation_error");return}this.loginUrl=n.authorization_endpoint,this.logoutUrl=n.end_session_endpoint||this.logoutUrl,this.grantTypesSupported=n.grant_types_supported,this.issuer=n.issuer,this.tokenEndpoint=n.token_endpoint,this.userinfoEndpoint=n.userinfo_endpoint||this.userinfoEndpoint,this.jwksUri=n.jwks_uri,this.sessionCheckIFrameUrl=n.check_session_iframe||this.sessionCheckIFrameUrl,this.discoveryDocumentLoaded=!0,this.discoveryDocumentLoadedSubject.next(n),this.revocationEndpoint=n.revocation_endpoint||this.revocationEndpoint,this.sessionChecksEnabled&&this.restartSessionChecksIfStillLoggedIn(),this.loadJwks().then(a=>{let c={discoveryDocument:n,jwks:a},m=new ge("discovery_document_loaded",c);this.eventsSubject.next(m),o(m)}).catch(a=>{this.eventsSubject.next(new K("discovery_document_load_error",a)),r(a)})},n=>{this.logger.error("error loading discovery document",n),this.eventsSubject.next(new K("discovery_document_load_error",n)),r(n)})})}loadJwks(){return new Promise((e,o)=>{this.jwksUri?this.http.get(this.jwksUri).subscribe(r=>{this.jwks=r,e(r)},r=>{this.logger.error("error loading jwks",r),this.eventsSubject.next(new K("jwks_load_error",r)),o(r)}):e(null)})}validateDiscoveryDocument(e){let o;return!this.skipIssuerCheck&&e.issuer!==this.issuer?(this.logger.error("invalid issuer in discovery document","expected: "+this.issuer,"current: "+e.issuer),!1):(o=this.validateUrlFromDiscoveryDocument(e.authorization_endpoint),o.length>0?(this.logger.error("error validating authorization_endpoint in discovery document",o),!1):(o=this.validateUrlFromDiscoveryDocument(e.end_session_endpoint),o.length>0?(this.logger.error("error validating end_session_endpoint in discovery document",o),!1):(o=this.validateUrlFromDiscoveryDocument(e.token_endpoint),o.length>0&&this.logger.error("error validating token_endpoint in discovery document",o),o=this.validateUrlFromDiscoveryDocument(e.revocation_endpoint),o.length>0&&this.logger.error("error validating revocation_endpoint in discovery document",o),o=this.validateUrlFromDiscoveryDocument(e.userinfo_endpoint),o.length>0?(this.logger.error("error validating userinfo_endpoint in discovery document",o),!1):(o=this.validateUrlFromDiscoveryDocument(e.jwks_uri),o.length>0?(this.logger.error("error validating jwks_uri in discovery document",o),!1):(this.sessionChecksEnabled&&!e.check_session_iframe&&this.logger.warn("sessionChecksEnabled is activated but discovery document does not contain a check_session_iframe field"),!0)))))}fetchTokenUsingPasswordFlowAndLoadUserProfile(e,o,r=new Qe){return this.fetchTokenUsingPasswordFlow(e,o,r).then(()=>this.loadUserProfile())}loadUserProfile(){if(!this.hasValidAccessToken())throw new Error("Can not load User Profile without access_token");if(!this.validateUrlForHttps(this.userinfoEndpoint))throw new Error("userinfoEndpoint must use HTTPS (with TLS), or config value for property 'requireHttps' must be set to 'false' and allow HTTP (without TLS).");return new Promise((e,o)=>{let r=new Qe().set("Authorization","Bearer "+this.getAccessToken());this.http.get(this.userinfoEndpoint,{headers:r,observe:"response",responseType:"text"}).subscribe(n=>{if(this.debug("userinfo received",JSON.stringify(n)),n.headers.get("content-type").startsWith("application/json")){let a=JSON.parse(n.body),c=this.getIdentityClaims()||{};if(!this.skipSubjectCheck&&this.oidc&&(!c.sub||a.sub!==c.sub)){o(`if property oidc is true, the received user-id (sub) has to be the user-id of the user that has logged in with oidc.
if you are not using oidc but just oauth2 password flow set oidc to false`);return}a=Object.assign({},c,a),this._storage.setItem("id_token_claims_obj",JSON.stringify(a)),this.eventsSubject.next(new ge("user_profile_loaded")),e({info:a})}else this.debug("userinfo is not JSON, treating it as JWE/JWS"),this.eventsSubject.next(new ge("user_profile_loaded")),e(JSON.parse(n.body))},n=>{this.logger.error("error loading user info",n),this.eventsSubject.next(new K("user_profile_load_error",n)),o(n)})})}fetchTokenUsingPasswordFlow(e,o,r=new Qe){let n={username:e,password:o};return this.fetchTokenUsingGrant("password",n,r)}fetchTokenUsingGrant(e,o,r=new Qe){this.assertUrlNotNullAndCorrectProtocol(this.tokenEndpoint,"tokenEndpoint");let n=new It({encoder:new pt}).set("grant_type",e).set("scope",this.scope);if(this.useHttpBasicAuth){let a=btoa(`${this.clientId}:${this.dummyClientSecret}`);r=r.set("Authorization","Basic "+a)}if(this.useHttpBasicAuth||(n=n.set("client_id",this.clientId)),!this.useHttpBasicAuth&&this.dummyClientSecret&&(n=n.set("client_secret",this.dummyClientSecret)),this.customQueryParams)for(let a of Object.getOwnPropertyNames(this.customQueryParams))n=n.set(a,this.customQueryParams[a]);for(let a of Object.keys(o))n=n.set(a,o[a]);return r=r.set("Content-Type","application/x-www-form-urlencoded"),new Promise((a,c)=>{this.http.post(this.tokenEndpoint,n,{headers:r}).subscribe(m=>{this.debug("tokenResponse",m),this.storeAccessTokenResponse(m.access_token,m.refresh_token,m.expires_in||this.fallbackAccessTokenExpirationTimeInSec,m.scope,this.extractRecognizedCustomParameters(m)),this.oidc&&m.id_token&&this.processIdToken(m.id_token,m.access_token).then(_=>{this.storeIdToken(_),a(m)}),this.eventsSubject.next(new ge("token_received")),a(m)},m=>{this.logger.error("Error performing ${grantType} flow",m),this.eventsSubject.next(new K("token_error",m)),c(m)})})}refreshToken(){return this.assertUrlNotNullAndCorrectProtocol(this.tokenEndpoint,"tokenEndpoint"),new Promise((e,o)=>{let r=new It({encoder:new pt}).set("grant_type","refresh_token").set("scope",this.scope).set("refresh_token",this._storage.getItem("refresh_token")),n=new Qe().set("Content-Type","application/x-www-form-urlencoded");if(this.useHttpBasicAuth){let a=btoa(`${this.clientId}:${this.dummyClientSecret}`);n=n.set("Authorization","Basic "+a)}if(this.useHttpBasicAuth||(r=r.set("client_id",this.clientId)),!this.useHttpBasicAuth&&this.dummyClientSecret&&(r=r.set("client_secret",this.dummyClientSecret)),this.customQueryParams)for(let a of Object.getOwnPropertyNames(this.customQueryParams))r=r.set(a,this.customQueryParams[a]);this.http.post(this.tokenEndpoint,r,{headers:n}).pipe(Te(a=>this.oidc&&a.id_token?et(this.processIdToken(a.id_token,a.access_token,!0)).pipe(Fe(c=>this.storeIdToken(c)),Ie(()=>a)):be(a))).subscribe(a=>{this.debug("refresh tokenResponse",a),this.storeAccessTokenResponse(a.access_token,a.refresh_token,a.expires_in||this.fallbackAccessTokenExpirationTimeInSec,a.scope,this.extractRecognizedCustomParameters(a)),this.eventsSubject.next(new ge("token_received")),this.eventsSubject.next(new ge("token_refreshed")),e(a)},a=>{this.logger.error("Error refreshing token",a),this.eventsSubject.next(new K("token_refresh_error",a)),o(a)})})}removeSilentRefreshEventListener(){this.silentRefreshPostMessageEventListener&&(window.removeEventListener("message",this.silentRefreshPostMessageEventListener),this.silentRefreshPostMessageEventListener=null)}setupSilentRefreshEventListener(){this.removeSilentRefreshEventListener(),this.silentRefreshPostMessageEventListener=e=>{let o=this.processMessageEventMessage(e);this.checkOrigin&&e.origin!==location.origin&&console.error("wrong origin requested silent refresh!"),this.tryLogin({customHashFragment:o,preventClearHashAfterLogin:!0,customRedirectUri:this.silentRefreshRedirectUri||this.redirectUri}).catch(r=>this.debug("tryLogin during silent refresh failed",r))},window.addEventListener("message",this.silentRefreshPostMessageEventListener)}silentRefresh(e={},o=!0){let r=this.getIdentityClaims()||{};if(this.useIdTokenHintForSilentRefresh&&this.hasValidIdToken()&&(e.id_token_hint=this.getIdToken()),!this.validateUrlForHttps(this.loginUrl))throw new Error("loginUrl  must use HTTPS (with TLS), or config value for property 'requireHttps' must be set to 'false' and allow HTTP (without TLS).");if(typeof this.document>"u")throw new Error("silent refresh is not supported on this platform");let n=this.document.getElementById(this.silentRefreshIFrameName);n&&this.document.body.removeChild(n),this.silentRefreshSubject=r.sub;let a=this.document.createElement("iframe");a.id=this.silentRefreshIFrameName,this.setupSilentRefreshEventListener();let c=this.silentRefreshRedirectUri||this.redirectUri;this.createLoginUrl(null,null,c,o,e).then(b=>{a.setAttribute("src",b),this.silentRefreshShowIFrame||(a.style.display="none"),this.document.body.appendChild(a)});let m=this.events.pipe($(b=>b instanceof K),eo()),_=this.events.pipe($(b=>b.type==="token_received"),eo()),x=be(new K("silent_refresh_timeout",null)).pipe(Jt(this.silentRefreshTimeout));return Vr([m,_,x]).pipe(Ie(b=>{if(b instanceof K)throw b.type==="silent_refresh_timeout"?this.eventsSubject.next(b):(b=new K("silent_refresh_error",b),this.eventsSubject.next(b)),b;return b.type==="token_received"&&(b=new ge("silently_refreshed"),this.eventsSubject.next(b)),b})).toPromise()}initImplicitFlowInPopup(e){return this.initLoginFlowInPopup(e)}initLoginFlowInPopup(e){return e=e||{},this.createLoginUrl(null,null,this.silentRefreshRedirectUri,!1,{display:"popup"}).then(o=>new Promise((r,n)=>{let c=null;e.windowRef?e.windowRef&&!e.windowRef.closed&&(c=e.windowRef,c.location.href=o):c=window.open(o,"ngx-oauth2-oidc-login",this.calculatePopupFeatures(e));let m,_=L=>{this.tryLogin({customHashFragment:L,preventClearHashAfterLogin:!0,customRedirectUri:this.silentRefreshRedirectUri}).then(()=>{b(),r(!0)},ce=>{b(),n(ce)})},x=()=>{(!c||c.closed)&&(b(),n(new K("popup_closed",{})))};c?m=window.setInterval(x,500):n(new K("popup_blocked",{}));let b=()=>{window.clearInterval(m),window.removeEventListener("storage",C),window.removeEventListener("message",M),c!==null&&c.close(),c=null},M=L=>{let ce=this.processMessageEventMessage(L);ce&&ce!==null?(window.removeEventListener("storage",C),_(ce)):console.log("false event firing")},C=L=>{L.key==="auth_hash"&&(window.removeEventListener("message",M),_(L.newValue))};window.addEventListener("message",M),window.addEventListener("storage",C)}))}calculatePopupFeatures(e){let o=e.height||470,r=e.width||500,n=window.screenLeft+(window.outerWidth-r)/2,a=window.screenTop+(window.outerHeight-o)/2;return`location=no,toolbar=no,width=${r},height=${o},top=${a},left=${n}`}processMessageEventMessage(e){let o="#";if(this.silentRefreshMessagePrefix&&(o+=this.silentRefreshMessagePrefix),!e||!e.data||typeof e.data!="string")return;let r=e.data;if(r.startsWith(o))return"#"+r.substr(o.length)}canPerformSessionCheck(){return this.sessionChecksEnabled?this.sessionCheckIFrameUrl?this.getSessionState()?!(typeof this.document>"u"):(console.warn("sessionChecksEnabled is activated but there is no session_state"),!1):(console.warn("sessionChecksEnabled is activated but there is no sessionCheckIFrameUrl"),!1):!1}setupSessionCheckEventListener(){this.removeSessionCheckEventListener(),this.sessionCheckEventListener=e=>{let o=e.origin.toLowerCase(),r=this.issuer.toLowerCase();if(this.debug("sessionCheckEventListener"),!r.startsWith(o)){this.debug("sessionCheckEventListener","wrong origin",o,"expected",r,"event",e);return}switch(e.data){case"unchanged":this.ngZone.run(()=>{this.handleSessionUnchanged()});break;case"changed":this.ngZone.run(()=>{this.handleSessionChange()});break;case"error":this.ngZone.run(()=>{this.handleSessionError()});break}this.debug("got info from session check inframe",e)},this.ngZone.runOutsideAngular(()=>{window.addEventListener("message",this.sessionCheckEventListener)})}handleSessionUnchanged(){this.debug("session check","session unchanged"),this.eventsSubject.next(new Oe("session_unchanged"))}handleSessionChange(){this.eventsSubject.next(new Oe("session_changed")),this.stopSessionCheckTimer(),!this.useSilentRefresh&&this.responseType==="code"?this.refreshToken().then(()=>{this.debug("token refresh after session change worked")}).catch(()=>{this.debug("token refresh did not work after session changed"),this.eventsSubject.next(new Oe("session_terminated")),this.logOut(!0)}):this.silentRefreshRedirectUri?(this.silentRefresh().catch(()=>this.debug("silent refresh failed after session changed")),this.waitForSilentRefreshAfterSessionChange()):(this.eventsSubject.next(new Oe("session_terminated")),this.logOut(!0))}waitForSilentRefreshAfterSessionChange(){this.events.pipe($(e=>e.type==="silently_refreshed"||e.type==="silent_refresh_timeout"||e.type==="silent_refresh_error"),eo()).subscribe(e=>{e.type!=="silently_refreshed"&&(this.debug("silent refresh did not work after session changed"),this.eventsSubject.next(new Oe("session_terminated")),this.logOut(!0))})}handleSessionError(){this.stopSessionCheckTimer(),this.eventsSubject.next(new Oe("session_error"))}removeSessionCheckEventListener(){this.sessionCheckEventListener&&(window.removeEventListener("message",this.sessionCheckEventListener),this.sessionCheckEventListener=null)}initSessionCheck(){if(!this.canPerformSessionCheck())return;let e=this.document.getElementById(this.sessionCheckIFrameName);e&&this.document.body.removeChild(e);let o=this.document.createElement("iframe");o.id=this.sessionCheckIFrameName,this.setupSessionCheckEventListener();let r=this.sessionCheckIFrameUrl;o.setAttribute("src",r),o.style.display="none",this.document.body.appendChild(o),this.startSessionCheckTimer()}startSessionCheckTimer(){this.stopSessionCheckTimer(),this.ngZone.runOutsideAngular(()=>{this.sessionCheckTimer=setInterval(this.checkSession.bind(this),this.sessionCheckIntervall)})}stopSessionCheckTimer(){this.sessionCheckTimer&&(clearInterval(this.sessionCheckTimer),this.sessionCheckTimer=null)}checkSession(){let e=this.document.getElementById(this.sessionCheckIFrameName);e||this.logger.warn("checkSession did not find iframe",this.sessionCheckIFrameName);let o=this.getSessionState();o||this.stopSessionCheckTimer();let r=this.clientId+" "+o;e.contentWindow.postMessage(r,this.issuer)}createLoginUrl(){return xe(this,arguments,function*(e="",o="",r="",n=!1,a={}){let c=this,m;r?m=r:m=this.redirectUri;let _=yield this.createAndSaveNonce();if(e?e=_+this.config.nonceStateSeparator+encodeURIComponent(e):e=_,!this.requestAccessToken&&!this.oidc)throw new Error("Either requestAccessToken or oidc or both must be true");this.config.responseType?this.responseType=this.config.responseType:this.oidc&&this.requestAccessToken?this.responseType="id_token token":this.oidc&&!this.requestAccessToken?this.responseType="id_token":this.responseType="token";let x=c.loginUrl.indexOf("?")>-1?"&":"?",b=c.scope;this.oidc&&!b.match(/(^|\s)openid($|\s)/)&&(b="openid "+b);let M=c.loginUrl+x+"response_type="+encodeURIComponent(c.responseType)+"&client_id="+encodeURIComponent(c.clientId)+"&state="+encodeURIComponent(e)+"&redirect_uri="+encodeURIComponent(m)+"&scope="+encodeURIComponent(b);if(this.responseType.includes("code")&&!this.disablePKCE){let[C,L]=yield this.createChallangeVerifierPairForPKCE();this.saveNoncesInLocalStorage&&typeof window.localStorage<"u"?localStorage.setItem("PKCE_verifier",L):this._storage.setItem("PKCE_verifier",L),M+="&code_challenge="+C,M+="&code_challenge_method=S256"}o&&(M+="&login_hint="+encodeURIComponent(o)),c.resource&&(M+="&resource="+encodeURIComponent(c.resource)),c.oidc&&(M+="&nonce="+encodeURIComponent(_)),n&&(M+="&prompt=none");for(let C of Object.keys(a))M+="&"+encodeURIComponent(C)+"="+encodeURIComponent(a[C]);if(this.customQueryParams)for(let C of Object.getOwnPropertyNames(this.customQueryParams))M+="&"+C+"="+encodeURIComponent(this.customQueryParams[C]);return M})}initImplicitFlowInternal(e="",o=""){if(this.inImplicitFlow)return;if(this.inImplicitFlow=!0,!this.validateUrlForHttps(this.loginUrl))throw new Error("loginUrl  must use HTTPS (with TLS), or config value for property 'requireHttps' must be set to 'false' and allow HTTP (without TLS).");let r={},n=null;typeof o=="string"?n=o:typeof o=="object"&&(r=o),this.createLoginUrl(e,n,null,!1,r).then(this.config.openUri).catch(a=>{console.error("Error in initImplicitFlow",a),this.inImplicitFlow=!1})}initImplicitFlow(e="",o=""){this.loginUrl!==""?this.initImplicitFlowInternal(e,o):this.events.pipe($(r=>r.type==="discovery_document_loaded")).subscribe(()=>this.initImplicitFlowInternal(e,o))}resetImplicitFlow(){this.inImplicitFlow=!1}callOnTokenReceivedIfExists(e){let o=this;if(e.onTokenReceived){let r={idClaims:o.getIdentityClaims(),idToken:o.getIdToken(),accessToken:o.getAccessToken(),state:o.state};e.onTokenReceived(r)}}storeAccessTokenResponse(e,o,r,n,a){if(this._storage.setItem("access_token",e),n&&!Array.isArray(n)?this._storage.setItem("granted_scopes",JSON.stringify(n.split(" "))):n&&Array.isArray(n)&&this._storage.setItem("granted_scopes",JSON.stringify(n)),this._storage.setItem("access_token_stored_at",""+this.dateTimeService.now()),r){let c=r*1e3,_=this.dateTimeService.new().getTime()+c;this._storage.setItem("expires_at",""+_)}o&&this._storage.setItem("refresh_token",o),a&&a.forEach((c,m)=>{this._storage.setItem(m,c)})}tryLogin(e=null){return this.config.responseType==="code"?this.tryLoginCodeFlow(e).then(()=>!0):this.tryLoginImplicitFlow(e)}parseQueryString(e){return!e||e.length===0?{}:(e.charAt(0)==="?"&&(e=e.substr(1)),this.urlHelper.parseQueryString(e))}tryLoginCodeFlow(e=null){return xe(this,null,function*(){e=e||{};let o=e.customHashFragment?e.customHashFragment.substring(1):window.location.search,r=this.getCodePartsFromUrl(o),n=r.code,a=r.state,c=r.session_state;if(!e.preventClearHashAfterLogin){let x=location.origin+location.pathname+location.search.replace(/code=[^&$]*/,"").replace(/scope=[^&$]*/,"").replace(/state=[^&$]*/,"").replace(/session_state=[^&$]*/,"").replace(/^\?&/,"?").replace(/&$/,"").replace(/^\?$/,"").replace(/&+/g,"&").replace(/\?&/,"?").replace(/\?$/,"")+location.hash;history.replaceState(null,window.name,x)}let[m,_]=this.parseState(a);if(this.state=_,r.error){this.debug("error trying to login"),this.handleLoginError(e,r);let x=new K("code_error",{},r);return this.eventsSubject.next(x),Promise.reject(x)}if(!e.disableNonceCheck){if(!m)return this.saveRequestedRoute(),Promise.resolve();if(!e.disableOAuth2StateCheck&&!this.validateNonce(m)){let b=new K("invalid_nonce_in_state",null);return this.eventsSubject.next(b),Promise.reject(b)}}return this.storeSessionState(c),n&&(yield this.getTokenFromCode(n,e),this.restoreRequestedRoute()),Promise.resolve()})}saveRequestedRoute(){this.config.preserveRequestedRoute&&this._storage.setItem("requested_route",window.location.pathname+window.location.search)}restoreRequestedRoute(){let e=this._storage.getItem("requested_route");e&&history.replaceState(null,"",window.location.origin+e)}getCodePartsFromUrl(e){return!e||e.length===0?this.urlHelper.getHashFragmentParams():(e.charAt(0)==="?"&&(e=e.substr(1)),this.urlHelper.parseQueryString(e))}getTokenFromCode(e,o){let r=new It({encoder:new pt}).set("grant_type","authorization_code").set("code",e).set("redirect_uri",o.customRedirectUri||this.redirectUri);if(!this.disablePKCE){let n;this.saveNoncesInLocalStorage&&typeof window.localStorage<"u"?n=localStorage.getItem("PKCE_verifier"):n=this._storage.getItem("PKCE_verifier"),n?r=r.set("code_verifier",n):console.warn("No PKCE verifier found in oauth storage!")}return this.fetchAndProcessToken(r,o)}fetchAndProcessToken(e,o){o=o||{},this.assertUrlNotNullAndCorrectProtocol(this.tokenEndpoint,"tokenEndpoint");let r=new Qe().set("Content-Type","application/x-www-form-urlencoded");if(this.useHttpBasicAuth){let n=btoa(`${this.clientId}:${this.dummyClientSecret}`);r=r.set("Authorization","Basic "+n)}return this.useHttpBasicAuth||(e=e.set("client_id",this.clientId)),!this.useHttpBasicAuth&&this.dummyClientSecret&&(e=e.set("client_secret",this.dummyClientSecret)),new Promise((n,a)=>{if(this.customQueryParams)for(let c of Object.getOwnPropertyNames(this.customQueryParams))e=e.set(c,this.customQueryParams[c]);this.http.post(this.tokenEndpoint,e,{headers:r}).subscribe(c=>{this.debug("refresh tokenResponse",c),this.storeAccessTokenResponse(c.access_token,c.refresh_token,c.expires_in||this.fallbackAccessTokenExpirationTimeInSec,c.scope,this.extractRecognizedCustomParameters(c)),this.oidc&&c.id_token?this.processIdToken(c.id_token,c.access_token,o.disableNonceCheck).then(m=>{this.storeIdToken(m),this.eventsSubject.next(new ge("token_received")),this.eventsSubject.next(new ge("token_refreshed")),n(c)}).catch(m=>{this.eventsSubject.next(new K("token_validation_error",m)),console.error("Error validating tokens"),console.error(m),a(m)}):(this.eventsSubject.next(new ge("token_received")),this.eventsSubject.next(new ge("token_refreshed")),n(c))},c=>{console.error("Error getting token",c),this.eventsSubject.next(new K("token_refresh_error",c)),a(c)})})}tryLoginImplicitFlow(e=null){e=e||{};let o;e.customHashFragment?o=this.urlHelper.getHashFragmentParams(e.customHashFragment):o=this.urlHelper.getHashFragmentParams(),this.debug("parsed url",o);let r=o.state,[n,a]=this.parseState(r);if(this.state=a,o.error){this.debug("error trying to login"),this.handleLoginError(e,o);let b=new K("token_error",{},o);return this.eventsSubject.next(b),Promise.reject(b)}let c=o.access_token,m=o.id_token,_=o.session_state,x=o.scope;if(!this.requestAccessToken&&!this.oidc)return Promise.reject("Either requestAccessToken or oidc (or both) must be true.");if(this.requestAccessToken&&!c||this.requestAccessToken&&!e.disableOAuth2StateCheck&&!r||this.oidc&&!m)return Promise.resolve(!1);if(this.sessionChecksEnabled&&!_&&this.logger.warn("session checks (Session Status Change Notification) were activated in the configuration but the id_token does not contain a session_state claim"),this.requestAccessToken&&!e.disableNonceCheck&&!this.validateNonce(n)){let M=new K("invalid_nonce_in_state",null);return this.eventsSubject.next(M),Promise.reject(M)}return this.requestAccessToken&&this.storeAccessTokenResponse(c,null,o.expires_in||this.fallbackAccessTokenExpirationTimeInSec,x),this.oidc?this.processIdToken(m,c,e.disableNonceCheck).then(b=>e.validationHandler?e.validationHandler({accessToken:c,idClaims:b.idTokenClaims,idToken:b.idToken,state:r}).then(()=>b):b).then(b=>(this.storeIdToken(b),this.storeSessionState(_),this.clearHashAfterLogin&&!e.preventClearHashAfterLogin&&this.clearLocationHash(),this.eventsSubject.next(new ge("token_received")),this.callOnTokenReceivedIfExists(e),this.inImplicitFlow=!1,!0)).catch(b=>(this.eventsSubject.next(new K("token_validation_error",b)),this.logger.error("Error validating tokens"),this.logger.error(b),Promise.reject(b))):(this.eventsSubject.next(new ge("token_received")),this.clearHashAfterLogin&&!e.preventClearHashAfterLogin&&this.clearLocationHash(),this.callOnTokenReceivedIfExists(e),Promise.resolve(!0))}parseState(e){let o=e,r="";if(e){let n=e.indexOf(this.config.nonceStateSeparator);n>-1&&(o=e.substr(0,n),r=e.substr(n+this.config.nonceStateSeparator.length))}return[o,r]}validateNonce(e){let o;return this.saveNoncesInLocalStorage&&typeof window.localStorage<"u"?o=localStorage.getItem("nonce"):o=this._storage.getItem("nonce"),o!==e?(console.error("Validating access_token failed, wrong state/nonce.",o,e),!1):!0}storeIdToken(e){this._storage.setItem("id_token",e.idToken),this._storage.setItem("id_token_claims_obj",e.idTokenClaimsJson),this._storage.setItem("id_token_expires_at",""+e.idTokenExpiresAt),this._storage.setItem("id_token_stored_at",""+this.dateTimeService.now())}storeSessionState(e){this._storage.setItem("session_state",e)}getSessionState(){return this._storage.getItem("session_state")}handleLoginError(e,o){e.onLoginError&&e.onLoginError(o),this.clearHashAfterLogin&&!e.preventClearHashAfterLogin&&this.clearLocationHash()}getClockSkewInMsec(e=6e5){return!this.clockSkewInSec&&this.clockSkewInSec!==0?e:this.clockSkewInSec*1e3}processIdToken(e,o,r=!1){let n=e.split("."),a=this.padBase64(n[0]),c=Za(a),m=JSON.parse(c),_=this.padBase64(n[1]),x=Za(_),b=JSON.parse(x),M;if(this.saveNoncesInLocalStorage&&typeof window.localStorage<"u"?M=localStorage.getItem("nonce"):M=this._storage.getItem("nonce"),Array.isArray(b.aud)){if(b.aud.every(P=>P!==this.clientId)){let P="Wrong audience: "+b.aud.join(",");return this.logger.warn(P),Promise.reject(P)}}else if(b.aud!==this.clientId){let P="Wrong audience: "+b.aud;return this.logger.warn(P),Promise.reject(P)}if(!b.sub){let P="No sub claim in id_token";return this.logger.warn(P),Promise.reject(P)}if(this.sessionChecksEnabled&&this.silentRefreshSubject&&this.silentRefreshSubject!==b.sub){let P=`After refreshing, we got an id_token for another user (sub). Expected sub: ${this.silentRefreshSubject}, received sub: ${b.sub}`;return this.logger.warn(P),Promise.reject(P)}if(!b.iat){let P="No iat claim in id_token";return this.logger.warn(P),Promise.reject(P)}if(!this.skipIssuerCheck&&b.iss!==this.issuer){let P="Wrong issuer: "+b.iss;return this.logger.warn(P),Promise.reject(P)}if(!r&&b.nonce!==M){let P="Wrong nonce: "+b.nonce;return this.logger.warn(P),Promise.reject(P)}if(Object.prototype.hasOwnProperty.call(this,"responseType")&&(this.responseType==="code"||this.responseType==="id_token")&&(this.disableAtHashCheck=!0),!this.disableAtHashCheck&&this.requestAccessToken&&!b.at_hash){let P="An at_hash is needed!";return this.logger.warn(P),Promise.reject(P)}let C=this.dateTimeService.now(),L=b.iat*1e3,ce=b.exp*1e3,Je=this.getClockSkewInMsec();if(L-Je>=C||ce+Je-this.decreaseExpirationBySec<=C){let P="Token has expired";return console.error(P),console.error({now:C,issuedAtMSec:L,expiresAtMSec:ce}),Promise.reject(P)}let Ye={accessToken:o,idToken:e,jwks:this.jwks,idTokenClaims:b,idTokenHeader:m,loadKeys:()=>this.loadJwks()};return this.disableAtHashCheck?this.checkSignature(Ye).then(()=>({idToken:e,idTokenClaims:b,idTokenClaimsJson:x,idTokenHeader:m,idTokenHeaderJson:c,idTokenExpiresAt:ce})):this.checkAtHash(Ye).then(P=>{if(!this.disableAtHashCheck&&this.requestAccessToken&&!P){let Gt="Wrong at_hash";return this.logger.warn(Gt),Promise.reject(Gt)}return this.checkSignature(Ye).then(()=>{let Gt=!this.disableAtHashCheck,wr={idToken:e,idTokenClaims:b,idTokenClaimsJson:x,idTokenHeader:m,idTokenHeaderJson:c,idTokenExpiresAt:ce};return Gt?this.checkAtHash(Ye).then(Ls=>{if(this.requestAccessToken&&!Ls){let Cr="Wrong at_hash";return this.logger.warn(Cr),Promise.reject(Cr)}else return wr}):wr})})}getIdentityClaims(){let e=this._storage.getItem("id_token_claims_obj");return e?JSON.parse(e):null}getGrantedScopes(){let e=this._storage.getItem("granted_scopes");return e?JSON.parse(e):null}getIdToken(){return this._storage?this._storage.getItem("id_token"):null}padBase64(e){for(;e.length%4!==0;)e+="=";return e}getAccessToken(){return this._storage?this._storage.getItem("access_token"):null}getRefreshToken(){return this._storage?this._storage.getItem("refresh_token"):null}getAccessTokenExpiration(){return this._storage.getItem("expires_at")?parseInt(this._storage.getItem("expires_at"),10):null}getAccessTokenStoredAt(){return parseInt(this._storage.getItem("access_token_stored_at"),10)}getIdTokenStoredAt(){return parseInt(this._storage.getItem("id_token_stored_at"),10)}getIdTokenExpiration(){return this._storage.getItem("id_token_expires_at")?parseInt(this._storage.getItem("id_token_expires_at"),10):null}hasValidAccessToken(){if(this.getAccessToken()){let e=this._storage.getItem("expires_at"),o=this.dateTimeService.new();return!(e&&parseInt(e,10)-this.decreaseExpirationBySec<o.getTime()-this.getClockSkewInMsec())}return!1}hasValidIdToken(){if(this.getIdToken()){let e=this._storage.getItem("id_token_expires_at"),o=this.dateTimeService.new();return!(e&&parseInt(e,10)-this.decreaseExpirationBySec<o.getTime()-this.getClockSkewInMsec())}return!1}getCustomTokenResponseProperty(e){return this._storage&&this.config.customTokenParameters&&this.config.customTokenParameters.indexOf(e)>=0&&this._storage.getItem(e)!==null?JSON.parse(this._storage.getItem(e)):null}authorizationHeader(){return"Bearer "+this.getAccessToken()}logOut(e={},o=""){let r=!1;typeof e=="boolean"&&(r=e,e={});let n=this.getIdToken();if(this._storage.removeItem("access_token"),this._storage.removeItem("id_token"),this._storage.removeItem("refresh_token"),this.saveNoncesInLocalStorage?(localStorage.removeItem("nonce"),localStorage.removeItem("PKCE_verifier")):(this._storage.removeItem("nonce"),this._storage.removeItem("PKCE_verifier")),this._storage.removeItem("expires_at"),this._storage.removeItem("id_token_claims_obj"),this._storage.removeItem("id_token_expires_at"),this._storage.removeItem("id_token_stored_at"),this._storage.removeItem("access_token_stored_at"),this._storage.removeItem("granted_scopes"),this._storage.removeItem("session_state"),this.config.customTokenParameters&&this.config.customTokenParameters.forEach(c=>this._storage.removeItem(c)),this.silentRefreshSubject=null,this.eventsSubject.next(new Oe("logout")),!this.logoutUrl||r)return;let a;if(!this.validateUrlForHttps(this.logoutUrl))throw new Error("logoutUrl  must use HTTPS (with TLS), or config value for property 'requireHttps' must be set to 'false' and allow HTTP (without TLS).");if(this.logoutUrl.indexOf("{{")>-1)a=this.logoutUrl.replace(/\{\{id_token\}\}/,encodeURIComponent(n)).replace(/\{\{client_id\}\}/,encodeURIComponent(this.clientId));else{let c=new It({encoder:new pt});n&&(c=c.set("id_token_hint",n));let m=this.postLogoutRedirectUri||this.redirectUriAsPostLogoutRedirectUriFallback&&this.redirectUri||"";m&&(c=c.set("post_logout_redirect_uri",m),o&&(c=c.set("state",o)));for(let _ in e)c=c.set(_,e[_]);a=this.logoutUrl+(this.logoutUrl.indexOf("?")>-1?"&":"?")+c.toString()}this.config.openUri(a)}createAndSaveNonce(){let e=this;return this.createNonce().then(function(o){return e.saveNoncesInLocalStorage&&typeof window.localStorage<"u"?localStorage.setItem("nonce",o):e._storage.setItem("nonce",o),o})}ngOnDestroy(){this.clearAccessTokenTimer(),this.clearIdTokenTimer(),this.removeSilentRefreshEventListener();let e=this.document.getElementById(this.silentRefreshIFrameName);e&&e.remove(),this.stopSessionCheckTimer(),this.removeSessionCheckEventListener();let o=this.document.getElementById(this.sessionCheckIFrameName);o&&o.remove()}createNonce(){return new Promise(e=>{if(this.rngUrl)throw new Error("createNonce with rng-web-api has not been implemented so far");let o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",r=45,n="",a=typeof self>"u"?null:self.crypto||self.msCrypto;if(a){let c=new Uint8Array(r);a.getRandomValues(c),c.map||(c.map=Array.prototype.map),c=c.map(m=>o.charCodeAt(m%o.length)),n=String.fromCharCode.apply(null,c)}else for(;0<r--;)n+=o[Math.random()*o.length|0];e(Ga(n))})}checkAtHash(e){return xe(this,null,function*(){return this.tokenValidationHandler?this.tokenValidationHandler.validateAtHash(e):(this.logger.warn("No tokenValidationHandler configured. Cannot check at_hash."),!0)})}checkSignature(e){return this.tokenValidationHandler?this.tokenValidationHandler.validateSignature(e):(this.logger.warn("No tokenValidationHandler configured. Cannot check signature."),Promise.resolve(null))}initLoginFlow(e="",o={}){return this.responseType==="code"?this.initCodeFlow(e,o):this.initImplicitFlow(e,o)}initCodeFlow(e="",o={}){this.loginUrl!==""?this.initCodeFlowInternal(e,o):this.events.pipe($(r=>r.type==="discovery_document_loaded")).subscribe(()=>this.initCodeFlowInternal(e,o))}initCodeFlowInternal(e="",o={}){if(!this.validateUrlForHttps(this.loginUrl))throw new Error("loginUrl  must use HTTPS (with TLS), or config value for property 'requireHttps' must be set to 'false' and allow HTTP (without TLS).");let r={},n=null;typeof o=="string"?n=o:typeof o=="object"&&(r=o),this.createLoginUrl(e,n,null,!1,r).then(this.config.openUri).catch(a=>{console.error("Error in initAuthorizationCodeFlow"),console.error(a)})}createChallangeVerifierPairForPKCE(){return xe(this,null,function*(){if(!this.crypto)throw new Error("PKCE support for code flow needs a CryptoHander. Did you import the OAuthModule using forRoot() ?");let e=yield this.createNonce(),o=yield this.crypto.calcHash(e,"sha-256");return[Ga(o),e]})}extractRecognizedCustomParameters(e){let o=new Map;return this.config.customTokenParameters&&this.config.customTokenParameters.forEach(r=>{e[r]&&o.set(r,JSON.stringify(e[r]))}),o}revokeTokenAndLogout(e={},o=!1){let r=this.revocationEndpoint,n=this.getAccessToken(),a=this.getRefreshToken();if(!n)return Promise.resolve();let c=new It({encoder:new pt}),m=new Qe().set("Content-Type","application/x-www-form-urlencoded");if(this.useHttpBasicAuth){let _=btoa(`${this.clientId}:${this.dummyClientSecret}`);m=m.set("Authorization","Basic "+_)}if(this.useHttpBasicAuth||(c=c.set("client_id",this.clientId)),!this.useHttpBasicAuth&&this.dummyClientSecret&&(c=c.set("client_secret",this.dummyClientSecret)),this.customQueryParams)for(let _ of Object.getOwnPropertyNames(this.customQueryParams))c=c.set(_,this.customQueryParams[_]);return new Promise((_,x)=>{let b,M;if(n){let C=c.set("token",n).set("token_type_hint","access_token");b=this.http.post(r,C,{headers:m})}else b=be(null);if(a){let C=c.set("token",a).set("token_type_hint","refresh_token");M=this.http.post(r,C,{headers:m})}else M=be(null);o&&(b=b.pipe(ft(C=>C.status===0?be(null):Xt(C))),M=M.pipe(ft(C=>C.status===0?be(null):Xt(C)))),Mr([b,M]).subscribe(C=>{this.logOut(e),_(C),this.logger.info("Token successfully revoked")},C=>{this.logger.error("Error revoking token",C),this.eventsSubject.next(new K("token_revoke_error",C)),x(C)})})}clearLocationHash(){location.hash!=""&&(location.hash="")}static{this.\u0275fac=function(o){return new(o||t)(N($e),N(oi),N(Pt,8),N(Io,8),N(Lt,8),N(Ya),N(xo),N(So,8),N(wt),N(Wt))}}static{this.\u0275prov=B({token:t,factory:t.\u0275fac})}}return t})(),To=class{},cr=class{handleError(i){return Xt(i)}},Ys=(()=>{class t{constructor(e,o,r){this.oAuthService=e,this.errorHandler=o,this.moduleConfig=r}checkUrl(e){return this.moduleConfig.resourceServer.customUrlValidation?this.moduleConfig.resourceServer.customUrlValidation(e):this.moduleConfig.resourceServer.allowedUrls?!!this.moduleConfig.resourceServer.allowedUrls.find(o=>e.toLowerCase().startsWith(o.toLowerCase())):!0}intercept(e,o){let r=e.url.toLowerCase();return!this.moduleConfig||!this.moduleConfig.resourceServer||!this.checkUrl(r)?o.handle(e):this.moduleConfig.resourceServer.sendAccessToken?Dr(be(this.oAuthService.getAccessToken()).pipe($(a=>!!a)),this.oAuthService.events.pipe($(a=>a.type==="token_received"),Er(this.oAuthService.waitForTokenInMsec||0),ft(()=>be(null)),Ie(()=>this.oAuthService.getAccessToken()))).pipe(Se(1),Or(a=>{if(a){let c="Bearer "+a,m=e.headers.set("Authorization",c);e=e.clone({headers:m})}return o.handle(e).pipe(ft(c=>this.errorHandler.handleError(c)))})):o.handle(e).pipe(ft(a=>this.errorHandler.handleError(a)))}static{this.\u0275fac=function(o){return new(o||t)(N(Re),N(To),N(Co,8))}}static{this.\u0275prov=B({token:t,factory:t.\u0275fac})}}return t})();function Xs(){return console}function Js(){return typeof sessionStorage<"u"?sessionStorage:new js}function el(t=null,i=wo){return ot([Re,Ya,{provide:xo,useFactory:Xs},{provide:Pt,useFactory:Js},{provide:Io,useClass:i},{provide:So,useClass:Gs},{provide:To,useClass:cr},{provide:Co,useValue:t},{provide:no,useClass:Ys,multi:!0},{provide:Wt,useClass:Us}])}var Ja=(()=>{class t{static forRoot(e=null,o=wo){return{ngModule:t,providers:[el(e,o)]}}static{this.\u0275fac=function(o){return new(o||t)}}static{this.\u0275mod=ne({type:t})}static{this.\u0275inj=ie({imports:[R]})}}return t})();var Kh=new gt("AUTH_CONFIG");var ol={provide:qi,useFactory:()=>{let t=y(Vt);return()=>{let i=t.getEnvironment();if(!i.oAuthConfig){console.warn("The oAuthConfig env is missing on environment.ts");return}let{issuer:e}=i.oAuthConfig,o=e.endsWith("/")?e:`${e}/`;window.open(`${o}Account/Manage?returnUrl=${window.location.href}`,"_self")}}},rl=(()=>{class t{constructor(){this.oAuthService=y(Re),this.authService=y(Ut)}canActivate(e,o){if(this.oAuthService.hasValidAccessToken())return!0;let n={returnUrl:o.url};return this.authService.navigateToLogin(n),!1}static{this.\u0275fac=function(o){return new(o||t)}}static{this.\u0275prov=B({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),il=(t,i)=>{let e=y(Re),o=y(Ut);if(e.hasValidAccessToken())return!0;let n={returnUrl:i.url};return o.navigateToLogin(n),!1},nl=(()=>{class t{constructor(e,o,r){this.oAuthService=e,this.environmentService=o,this.options=r,this.listenToSetEnvironment()}listenToSetEnvironment(){this.environmentService.createOnUpdateStream(e=>e).pipe(Ie(e=>e.oAuthConfig),$(e=>!Ai(e,this.options.environment.oAuthConfig))).subscribe(e=>{this.oAuthService.configure(e)})}static{this.\u0275fac=function(o){return new(o||t)(N(Re),N(Vt),N(Ni))}}static{this.\u0275prov=B({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),al=(()=>{class t{constructor(e,o,r,n){this.oAuthService=e,this.sessionState=o,this.httpWaitService=r,this.tenantKey=n}intercept(e,o){this.httpWaitService.addRequest(e);let n=e.context?.get(Ki)?e:e.clone({setHeaders:this.getAdditionalHeaders(e.headers)});return o.handle(n).pipe(Pr(()=>this.httpWaitService.deleteRequest(e)))}getAdditionalHeaders(e){let o={},r=this.oAuthService.getAccessToken();!e?.has("Authorization")&&r&&(o.Authorization=`Bearer ${r}`);let n=this.sessionState.getLanguage();!e?.has("Accept-Language")&&n&&(o["Accept-Language"]=n);let a=this.sessionState.getTenant();return!e?.has(this.tenantKey)&&a?.id&&(o[this.tenantKey]=a.id),o["X-Requested-With"]="XMLHttpRequest",o}static{this.\u0275fac=function(o){return new(o||t)(N(Re),N(_o),N(Qi),N(tr))}}static{this.\u0275prov=B({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),dr=localStorage;function qt(t=dr){["access_token","id_token","refresh_token","nonce","PKCE_verifier","expires_at","id_token_claims_obj","id_token_expires_at","id_token_stored_at","access_token_stored_at","granted_scopes","session_state"].forEach(e=>t.removeItem(e))}var ts=(()=>{class t{constructor(){this.#e="remember_me",this.localStorageService=y(bo)}#e;set(e){this.localStorageService.setItem(this.#e,JSON.stringify(e))}remove(){this.localStorageService.removeItem(this.#e)}get(){return!!JSON.parse(this.localStorageService.getItem(this.#e)||"false")}getFromToken(e){let o=e.split(".")[1].replace(/-/g,"+").replace(/_/g,"/");try{return!!JSON.parse(atob(o))[this.#e]}catch{return!1}}static{this.\u0275fac=function(o){return new(o||t)}}static{this.\u0275prov=B({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),os=function(t,i){let e=i.get(vo),o=i.get(dt),r=i.get(ts),n=i.get(Ut);return Ir(Te(()=>e.refreshAppState()),Fe(()=>{r.set(t.rememberMe||r.get()||r.getFromToken(n.getAccessToken())),t.redirectUrl&&o.navigate([t.redirectUrl])}))};function mr(t){let i=new Date().getTime();return t<i}var sl=function(t){let i=t.get(vo);t.get(Re).hasValidAccessToken()&&!i.getDeep("currentUser.id")&&qt()},Eo=class{constructor(i){this.injector=i,this.catchError=e=>(this.httpErrorReporter.reportError(e),be(null)),this.httpErrorReporter=i.get(ji),this.environment=i.get(Vt),this.configState=i.get(vo),this.oAuthService=i.get(Re),this.sessionState=i.get(_o),this.localStorageService=i.get(bo),this.oAuthConfig=this.environment.getEnvironment().oAuthConfig||{},this.tenantKey=i.get(tr),this.router=i.get(dt),this.oAuthErrorFilterService=i.get(rs),this.rememberMeService=i.get(ts),this.windowService=i.get(Gi),this.listenToOauthErrors()}init(){return xe(this,null,function*(){return this.oAuthConfig.clientId&&ll(this.oAuthConfig.clientId,dr)&&qt(dr),this.oAuthService.configure(this.oAuthConfig),this.oAuthService.events.pipe($(i=>i.type==="token_refresh_error")).subscribe(()=>this.navigateToLogin()),this.navigateToPreviousUrl(),this.oAuthService.loadDiscoveryDocument().then(()=>mr(this.oAuthService.getAccessTokenExpiration())&&this.oAuthService.getRefreshToken()?this.refreshToken():Promise.resolve()).catch(this.catchError)})}navigateToPreviousUrl(){let{responseType:i}=this.oAuthConfig;i==="code"&&this.oAuthService.events.pipe($(e=>e.type==="token_received"&&!!this.oAuthService.state),Se(1),Ie(()=>{let e=decodeURIComponent(this.oAuthService.state);return e&&e!=="/"?e:"/"}),Te(e=>this.configState.getOne$("currentUser").pipe($(o=>!!o?.isAuthenticated),Fe(()=>this.router.navigateByUrl(e))))).subscribe()}refreshToken(){return this.oAuthService.refreshToken().catch(()=>qt())}listenToOauthErrors(){this.oAuthService.events.pipe($(i=>i instanceof K),Fe(i=>{this.oAuthErrorFilterService.run(i)||qt()}),Te(()=>this.configState.refreshAppState())).subscribe()}};function ll(t,i){let e="abpOAuthClientId";if(!i.getItem(e))return i.setItem(e,t),!1;let o=i.getItem(e)!==t;return o&&i.setItem(e,t),o}var ur=class t extends Eo{constructor(){super(...arguments),this.isInternalAuth=!1}init(){return xe(this,null,function*(){return this.checkRememberMeOption(),this.listenToTokenReceived(),Ho(t.prototype,this,"init").call(this).then(()=>this.oAuthService.tryLogin().catch(Ui)).then(()=>this.oAuthService.setupAutomaticSilentRefresh())})}checkRememberMeOption(){let i=this.oAuthService.getAccessToken(),e=mr(this.oAuthService.getAccessTokenExpiration()),o=this.rememberMeService.get();if(i&&!o){let r=this.rememberMeService.getFromToken(i);this.rememberMeService.set(!!r)}o=this.rememberMeService.get(),i&&e&&!o&&(this.rememberMeService.remove(),this.oAuthService.logOut())}getCultureParams(i){let e=this.sessionState.getLanguage();return se(se({},e&&{culture:e,"ui-culture":e}),i)}setUICulture(){let i=new URLSearchParams(window.location.search);this.configState.uiCultureFromAuthCodeFlow=i.get("ui-culture")}replaceURLParams(){let i=this.windowService.window.location,e=this.windowService.window.history,o=i.search.replace(/([?&])iss=[^&]*&?/,"$1").replace(/([?&])culture=[^&]*&?/,"$1").replace(/([?&])ui-culture=[^&]*&?/,"$1").replace(/[?&]+$/,""),r=i.origin+i.pathname+o+i.hash;e.replaceState(null,"",r)}listenToTokenReceived(){this.oAuthService.events.pipe($(i=>i.type==="token_received"),Fe(()=>{this.setUICulture(),this.replaceURLParams()}),Se(1)).subscribe()}navigateToLogin(i){let e="";i?.returnUrl&&(e=i.returnUrl);let o=this.getCultureParams(i);this.oAuthService.initCodeFlow(e,o)}checkIfInternalAuth(i){return this.oAuthService.initCodeFlow("",this.getCultureParams(i)),!1}logout(i){return this.rememberMeService.remove(),i?.noRedirectToLogoutUrl?(this.router.navigate(["/"]),et(this.oAuthService.revokeTokenAndLogout(!0))):et(this.oAuthService.revokeTokenAndLogout(this.getCultureParams(i)))}login(i){return this.oAuthService.initCodeFlow("",this.getCultureParams(i)),be(null)}},pr=class t extends Eo{constructor(){super(...arguments),this.isInternalAuth=!0}listenToTokenExpiration(){this.oAuthService.events.pipe($(i=>i instanceof Oe&&i.type==="token_expires"&&i.info==="access_token")).subscribe(()=>{this.oAuthService.getRefreshToken()?this.refreshToken():(this.oAuthService.logOut(),this.rememberMeService.remove(),this.configState.refreshAppState().subscribe())})}init(){return xe(this,null,function*(){return this.checkRememberMeOption(),Ho(t.prototype,this,"init").call(this).then(()=>this.listenToTokenExpiration())})}checkRememberMeOption(){let i=this.oAuthService.getAccessToken(),e=mr(this.oAuthService.getAccessTokenExpiration()),o=this.rememberMeService.get();i&&e&&!o&&(this.rememberMeService.remove(),this.oAuthService.logOut())}navigateToLogin(i){return this.injector.get(dt).navigate(["/account/login"],{queryParams:i})}checkIfInternalAuth(){return!0}login(i){let e=this.sessionState.getTenant();return et(this.oAuthService.fetchTokenUsingPasswordFlow(i.username,i.password,new Qe(se({},e&&e.id&&{[this.tenantKey]:e.id})))).pipe(os(i,this.injector))}logout(){let i=this.injector.get(dt);return et(this.oAuthService.revokeTokenAndLogout(!0)).pipe(Te(()=>this.configState.refreshAppState()),Fe(()=>{this.rememberMeService.remove(),i.navigateByUrl("/")}))}refreshToken(){return this.oAuthService.refreshToken().catch(()=>{qt(),this.rememberMeService.remove()})}},es={Code(t){return new ur(t)},Password(t){return new pr(t)}},cl=(()=>{class t{get oidc(){return this.oAuthService.oidc}set oidc(e){this.oAuthService.oidc=e}get isInternalAuth(){return this.strategy.isInternalAuth}constructor(e){this.injector=e,this.oAuthService=this.injector.get(Re)}init(){return xe(this,null,function*(){let o=this.injector.get(Vt).getEnvironment$().pipe(Ie(r=>r?.oAuthConfig),$(Boolean),Fe(r=>{this.strategy=r.responseType==="code"?es.Code(this.injector):es.Password(this.injector)}),Te(()=>et(this.strategy.init())),Se(1));return yield Tr(o)})}logout(e){return this.strategy?this.strategy.logout(e):Sr}navigateToLogin(e){this.strategy.navigateToLogin(e)}login(e){return this.strategy.login(e)}get isAuthenticated(){return this.oAuthService.hasValidAccessToken()}loginUsingGrant(e,o,r){let{clientId:n,dummyClientSecret:a}=this.oAuthService,c=this.oAuthService.getAccessToken(),m=se({access_token:c,grant_type:e,client_id:n},o);return a&&(m.client_secret=a),this.oAuthService.fetchTokenUsingGrant(e,m,r)}getRefreshToken(){return this.oAuthService.getRefreshToken()}getAccessToken(){return this.oAuthService.getAccessToken()}refreshToken(){return this.oAuthService.refreshToken()}getAccessTokenExpiration(){return this.oAuthService.getAccessTokenExpiration()}static{this.\u0275fac=function(o){return new(o||t)(N(bt))}}static{this.\u0275prov=B({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),rs=(()=>{class t extends zi{constructor(){super(...arguments),this._filters=D([]),this.filters=this._filters.asReadonly()}get(e){return this._filters().find(({id:o})=>o===e)}add(e){this._filters.update(o=>[...o,e])}patch(e){let o=this.filters().find(({id:r})=>r===e.id);o&&Object.assign(o,e)}remove(e){this.filters().find(({id:r})=>r===e)&&this._filters.update(r=>r.filter(({id:n})=>n!==e))}run(e){return this.filters().filter(({executable:o})=>!!o).map(({execute:o})=>o(e)).some(o=>o)}static{this.\u0275fac=(()=>{let e;return function(r){return(e||(e=j(t)))(r||t)}})()}static{this.\u0275prov=B({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})();function is(){let t=[{provide:Ut,useClass:cl},{provide:Ri,useClass:rl},{provide:Hi,useValue:il},{provide:or,useClass:al},{provide:Zi,useValue:os},{provide:Wi,useValue:sl},{provide:no,useExisting:or,multi:!0},ol,yt(()=>{y(nl)}),Ja.forRoot().providers,{provide:Pt,useClass:bo},{provide:$i,useExisting:rs}];return ot(t)}function dl(t){let i=t;return 5}var Mo=["zh",[["\u4E0A\u5348","\u4E0B\u5348"],void 0,void 0],void 0,[["\u65E5","\u4E00","\u4E8C","\u4E09","\u56DB","\u4E94","\u516D"],["\u5468\u65E5","\u5468\u4E00","\u5468\u4E8C","\u5468\u4E09","\u5468\u56DB","\u5468\u4E94","\u5468\u516D"],["\u661F\u671F\u65E5","\u661F\u671F\u4E00","\u661F\u671F\u4E8C","\u661F\u671F\u4E09","\u661F\u671F\u56DB","\u661F\u671F\u4E94","\u661F\u671F\u516D"],["\u5468\u65E5","\u5468\u4E00","\u5468\u4E8C","\u5468\u4E09","\u5468\u56DB","\u5468\u4E94","\u5468\u516D"]],void 0,[["1","2","3","4","5","6","7","8","9","10","11","12"],["1\u6708","2\u6708","3\u6708","4\u6708","5\u6708","6\u6708","7\u6708","8\u6708","9\u6708","10\u6708","11\u6708","12\u6708"],["\u4E00\u6708","\u4E8C\u6708","\u4E09\u6708","\u56DB\u6708","\u4E94\u6708","\u516D\u6708","\u4E03\u6708","\u516B\u6708","\u4E5D\u6708","\u5341\u6708","\u5341\u4E00\u6708","\u5341\u4E8C\u6708"]],void 0,[["\u516C\u5143\u524D","\u516C\u5143"],void 0,void 0],0,[6,0],["y/M/d","y\u5E74M\u6708d\u65E5",void 0,"y\u5E74M\u6708d\u65E5EEEE"],["HH:mm","HH:mm:ss","z HH:mm:ss","zzzz HH:mm:ss"],["{1} {0}",void 0,void 0,void 0],[".",",",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","\xA4#,##0.00","#E0"],"CNY","\xA5","\u4EBA\u6C11\u5E01",{AUD:["AU$","$"],BYN:[void 0,"\u0440."],CNY:["\xA5"],ILR:["ILS"],JPY:["JP\xA5","\xA5"],KRW:["\uFFE6","\u20A9"],PHP:[void 0,"\u20B1"],RUR:[void 0,"\u0440."],TWD:["NT$"],USD:["US$","$"],XXX:[]},"ltr",dl];function ul(t){let i=t,e=Math.floor(Math.abs(t)),o=t.toString().replace(/^[^.]*\.?/,"").length;return e===1&&o===0?1:5}var Oo=["en",[["a","p"],["AM","PM"],void 0],[["AM","PM"],void 0,void 0],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],void 0,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],void 0,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",void 0,"{1} 'at' {0}",void 0],[".",",",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","\xA4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",ul];var ns=(t,i)=>{let e=y(Ge),o=t.clone({setHeaders:{"Accept-Language":e.language(),"X-User-Language-Audio":e.audioDevice(),"X-User-Language-UUID":e.userUUID(),"X-User-Language-Country":e.countryCode()}});return i(o)};var ss="https://holybless-public.pages.dev/",as={issuer:"https://dev.holyblesspan.com/",redirectUri:ss,clientId:"holybless_App",responseType:"code",scope:"offline_access holybless",requireHttps:!0},ls={production:!0,application:{baseUrl:ss,name:"holybless"},oAuthConfig:as,apis:{default:{url:"https://dev.holyblesspan.com",rootNamespace:"Holybless"},AbpAccountPublic:{url:as.issuer,rootNamespace:"AbpAccountPublic"}}};var fr={providers:[Yr({eventCoalescing:!0}),li(pn,ci({anchorScrolling:"enabled",scrollPositionRestoration:"enabled"}),di()),un(),ri(ni(),ii([ns])),Ti({theme:{preset:Kt,options:{darkModeSelector:".app-dark"}}}),ir("ngsw-worker.js",{enabled:!$o(),registrationStrategy:"registerWhenStable:30000"}),ir("ngsw-worker.js",{enabled:!$o(),registrationStrategy:"registerWhenStable:30000"}),yt(()=>{}),Xi(Yi({environment:ls,registerLocaleFn:()=>new Promise(t=>{ct(Mo,"zh-Hans"),ct(Oo,"en"),t(null)})})),is()]};var cs=(()=>{class t extends Ei{static \u0275fac=(()=>{let e;return function(r){return(e||(e=j(t)))(r||t)}})();static \u0275cmp=F({type:t,selectors:[["BarsIcon"]],features:[ae],decls:2,vars:5,consts:[["width","14","height","14","viewBox","0 0 14 14","fill","none","xmlns","http://www.w3.org/2000/svg"],["fill-rule","evenodd","clip-rule","evenodd","d","M13.3226 3.6129H0.677419C0.497757 3.6129 0.325452 3.54152 0.198411 3.41448C0.0713707 3.28744 0 3.11514 0 2.93548C0 2.75581 0.0713707 2.58351 0.198411 2.45647C0.325452 2.32943 0.497757 2.25806 0.677419 2.25806H13.3226C13.5022 2.25806 13.6745 2.32943 13.8016 2.45647C13.9286 2.58351 14 2.75581 14 2.93548C14 3.11514 13.9286 3.28744 13.8016 3.41448C13.6745 3.54152 13.5022 3.6129 13.3226 3.6129ZM13.3226 7.67741H0.677419C0.497757 7.67741 0.325452 7.60604 0.198411 7.479C0.0713707 7.35196 0 7.17965 0 6.99999C0 6.82033 0.0713707 6.64802 0.198411 6.52098C0.325452 6.39394 0.497757 6.32257 0.677419 6.32257H13.3226C13.5022 6.32257 13.6745 6.39394 13.8016 6.52098C13.9286 6.64802 14 6.82033 14 6.99999C14 7.17965 13.9286 7.35196 13.8016 7.479C13.6745 7.60604 13.5022 7.67741 13.3226 7.67741ZM0.677419 11.7419H13.3226C13.5022 11.7419 13.6745 11.6706 13.8016 11.5435C13.9286 11.4165 14 11.2442 14 11.0645C14 10.8848 13.9286 10.7125 13.8016 10.5855C13.6745 10.4585 13.5022 10.3871 13.3226 10.3871H0.677419C0.497757 10.3871 0.325452 10.4585 0.198411 10.5855C0.0713707 10.7125 0 10.8848 0 11.0645C0 11.2442 0.0713707 11.4165 0.198411 11.5435C0.325452 11.6706 0.497757 11.7419 0.677419 11.7419Z","fill","currentColor"]],template:function(o,r){o&1&&(to(),u(0,"svg",0),w(1,"path",1),p()),o&2&&(W(r.getClassNames()),k("aria-label",r.ariaLabel)("aria-hidden",r.ariaHidden)("role",r.role))},encapsulation:2})}return t})();var fl=["menubar"],hl=(t,i)=>({"p-menubar-submenu":t,"p-menubar-root-list":i}),us=t=>({"p-menubar-item-link":!0,"p-disabled":t}),gl=()=>({exact:!1}),bl=(t,i)=>({$implicit:t,root:i}),_l=t=>({display:t});function vl(t,i){if(t&1&&w(0,"li",8),t&2){let e=l().$implicit,o=l();Ne(o.getItemProp(e,"style")),s("ngClass",o.getSeparatorItemClass(e)),k("id",o.getItemId(e))("data-pc-section","separator")}}function yl(t,i){if(t&1&&w(0,"span",19),t&2){let e=l(4).$implicit,o=l();s("ngClass",o.getItemProp(e,"icon"))("ngStyle",o.getItemProp(e,"iconStyle")),k("data-pc-section","icon")("tabindex",-1)}}function kl(t,i){if(t&1&&(u(0,"span",20),A(1),p()),t&2){let e=l(4).$implicit,o=l();s("id",o.getItemLabelId(e)),k("data-pc-section","label"),d(),me(" ",o.getItemLabel(e)," ")}}function wl(t,i){if(t&1&&w(0,"span",21),t&2){let e=l(4).$implicit,o=l();s("innerHTML",o.getItemLabel(e),vt)("id",o.getItemLabelId(e)),k("data-pc-section","label")}}function Cl(t,i){if(t&1&&w(0,"p-badge",22),t&2){let e=l(4).$implicit,o=l();s("styleClass",o.getItemProp(e,"badgeStyleClass"))("value",o.getItemProp(e,"badge"))}}function xl(t,i){t&1&&w(0,"AngleDownIcon",25),t&2&&k("data-pc-section","submenuicon")}function Il(t,i){t&1&&w(0,"AngleRightIcon",25),t&2&&k("data-pc-section","submenuicon")}function Sl(t,i){if(t&1&&(Q(0),f(1,xl,1,1,"AngleDownIcon",24)(2,Il,1,1,"AngleRightIcon",24),q()),t&2){let e=l(6);d(),s("ngIf",e.root),d(),s("ngIf",!e.root)}}function Tl(t,i){}function El(t,i){t&1&&f(0,Tl,0,0,"ng-template",26),t&2&&s("data-pc-section","submenuicon")}function Ml(t,i){if(t&1&&(Q(0),f(1,Sl,3,2,"ng-container",11)(2,El,1,1,null,23),q()),t&2){let e=l(5);d(),s("ngIf",!e.submenuiconTemplate),d(),s("ngTemplateOutlet",e.submenuiconTemplate)}}function Ol(t,i){if(t&1&&(u(0,"a",15),f(1,yl,1,4,"span",16)(2,kl,2,3,"span",17)(3,wl,1,3,"ng-template",null,2,de)(5,Cl,1,2,"p-badge",18)(6,Ml,3,2,"ng-container",11),p()),t&2){let e=X(4),o=l(3).$implicit,r=l();s("target",r.getItemProp(o,"target"))("ngClass",O(11,us,r.getItemProp(o,"disabled"))),k("href",r.getItemProp(o,"url"),st)("data-automationid",r.getItemProp(o,"automationId"))("data-pc-section","action")("tabindex",-1),d(),s("ngIf",r.getItemProp(o,"icon")),d(),s("ngIf",r.getItemProp(o,"escape"))("ngIfElse",e),d(3),s("ngIf",r.getItemProp(o,"badge")),d(),s("ngIf",r.isItemGroup(o))}}function Bl(t,i){if(t&1&&w(0,"span",19),t&2){let e=l(4).$implicit,o=l();s("ngClass",o.getItemProp(e,"icon"))("ngStyle",o.getItemProp(e,"iconStyle")),k("data-pc-section","icon")("tabindex",-1)}}function Dl(t,i){if(t&1&&(u(0,"span",29),A(1),p()),t&2){let e=l(4).$implicit,o=l();d(),_e(o.getItemLabel(e))}}function Vl(t,i){if(t&1&&w(0,"span",30),t&2){let e=l(4).$implicit,o=l();s("innerHTML",o.getItemLabel(e),vt),k("data-pc-section","label")}}function Fl(t,i){if(t&1&&w(0,"p-badge",22),t&2){let e=l(4).$implicit,o=l();s("styleClass",o.getItemProp(e,"badgeStyleClass"))("value",o.getItemProp(e,"badge"))}}function Ll(t,i){t&1&&w(0,"AngleDownIcon",25),t&2&&k("data-pc-section","submenuicon")}function Pl(t,i){t&1&&w(0,"AngleRightIcon",25),t&2&&k("data-pc-section","submenuicon")}function Al(t,i){if(t&1&&(Q(0),f(1,Ll,1,1,"AngleDownIcon",24)(2,Pl,1,1,"AngleRightIcon",24),q()),t&2){let e=l(6);d(),s("ngIf",e.root),d(),s("ngIf",!e.root)}}function Rl(t,i){}function Hl(t,i){t&1&&f(0,Rl,0,0,"ng-template",26),t&2&&s("data-pc-section","submenuicon")}function zl(t,i){if(t&1&&(Q(0),f(1,Al,3,2,"ng-container",11)(2,Hl,1,1,null,23),q()),t&2){let e=l(5);d(),s("ngIf",!e.submenuiconTemplate),d(),s("ngTemplateOutlet",e.submenuiconTemplate)}}function $l(t,i){if(t&1&&(u(0,"a",27),f(1,Bl,1,4,"span",16)(2,Dl,2,1,"span",28)(3,Vl,1,2,"ng-template",null,3,de)(5,Fl,1,2,"p-badge",18)(6,zl,3,2,"ng-container",11),p()),t&2){let e=X(4),o=l(3).$implicit,r=l();s("routerLink",r.getItemProp(o,"routerLink"))("queryParams",r.getItemProp(o,"queryParams"))("routerLinkActive","p-menubar-item-link-active")("routerLinkActiveOptions",r.getItemProp(o,"routerLinkActiveOptions")||We(20,gl))("target",r.getItemProp(o,"target"))("ngClass",O(21,us,r.getItemProp(o,"disabled")))("fragment",r.getItemProp(o,"fragment"))("queryParamsHandling",r.getItemProp(o,"queryParamsHandling"))("preserveFragment",r.getItemProp(o,"preserveFragment"))("skipLocationChange",r.getItemProp(o,"skipLocationChange"))("replaceUrl",r.getItemProp(o,"replaceUrl"))("state",r.getItemProp(o,"state")),k("data-automationid",r.getItemProp(o,"automationId"))("tabindex",-1)("data-pc-section","action"),d(),s("ngIf",r.getItemProp(o,"icon")),d(),s("ngIf",r.getItemProp(o,"escape"))("ngIfElse",e),d(3),s("ngIf",r.getItemProp(o,"badge")),d(),s("ngIf",r.isItemGroup(o))}}function Nl(t,i){if(t&1&&(Q(0),f(1,Ol,7,13,"a",13)(2,$l,7,23,"a",14),q()),t&2){let e=l(2).$implicit,o=l();d(),s("ngIf",!o.getItemProp(e,"routerLink")),d(),s("ngIf",o.getItemProp(e,"routerLink"))}}function Ul(t,i){}function jl(t,i){t&1&&f(0,Ul,0,0,"ng-template")}function Kl(t,i){if(t&1&&(Q(0),f(1,jl,1,0,null,31),q()),t&2){let e=l(2).$implicit,o=l();d(),s("ngTemplateOutlet",o.itemTemplate)("ngTemplateOutletContext",J(2,bl,e.item,o.root))}}function Wl(t,i){if(t&1){let e=E();u(0,"p-menubarSub",32),v("itemClick",function(r){h(e);let n=l(3);return g(n.itemClick.emit(r))})("itemMouseEnter",function(r){h(e);let n=l(3);return g(n.onItemMouseEnter(r))}),p()}if(t&2){let e=l(2).$implicit,o=l();s("itemTemplate",o.itemTemplate)("items",e.items)("mobileActive",o.mobileActive)("autoDisplay",o.autoDisplay)("menuId",o.menuId)("activeItemPath",o.activeItemPath)("focusedItemId",o.focusedItemId)("level",o.level+1)("ariaLabelledBy",o.getItemLabelId(e))("inlineStyles",O(10,_l,o.isItemActive(e)?"flex":"none"))}}function Ql(t,i){if(t&1){let e=E();u(0,"li",9,1)(2,"div",10),v("click",function(r){h(e);let n=l().$implicit,a=l();return g(a.onItemClick(r,n))})("mouseenter",function(r){h(e);let n=l().$implicit,a=l();return g(a.onItemMouseEnter({$event:r,processedItem:n}))}),f(3,Nl,3,2,"ng-container",11)(4,Kl,2,5,"ng-container",11),p(),f(5,Wl,1,12,"p-menubarSub",12),p()}if(t&2){let e=l(),o=e.$implicit,r=e.index,n=l();W(n.getItemProp(o,"styleClass")),s("ngStyle",n.getItemProp(o,"style"))("ngClass",n.getItemClass(o))("tooltipOptions",n.getItemProp(o,"tooltipOptions")),k("id",n.getItemId(o))("data-pc-section","menuitem")("data-p-highlight",n.isItemActive(o))("data-p-focused",n.isItemFocused(o))("data-p-disabled",n.isItemDisabled(o))("aria-label",n.getItemLabel(o))("aria-disabled",n.isItemDisabled(o)||void 0)("aria-haspopup",n.isItemGroup(o)&&!n.getItemProp(o,"to")?"menu":void 0)("aria-expanded",n.isItemGroup(o)?n.isItemActive(o):void 0)("aria-setsize",n.getAriaSetSize())("aria-posinset",n.getAriaPosInset(r)),d(2),k("data-pc-section","content"),d(),s("ngIf",!n.itemTemplate),d(),s("ngIf",n.itemTemplate),d(),s("ngIf",n.isItemVisible(o)&&n.isItemGroup(o))}}function ql(t,i){if(t&1&&f(0,vl,1,5,"li",6)(1,Ql,6,20,"li",7),t&2){let e=i.$implicit,o=l();s("ngIf",o.isItemVisible(e)&&o.getItemProp(e,"separator")),d(),s("ngIf",o.isItemVisible(e)&&!o.getItemProp(e,"separator"))}}var Zl=["start"],Gl=["end"],Yl=["item"],Xl=["menuicon"],Jl=["submenuicon"],ec=["menubutton"],tc=["rootmenu"],oc=["*"],rc=(t,i)=>({"p-menubar p-component":!0,"p-menubar-mobile":t,"p-menubar-mobile-active":i});function ic(t,i){t&1&&U(0)}function nc(t,i){if(t&1&&(u(0,"div",8),f(1,ic,1,0,"ng-container",9),p()),t&2){let e=l();d(),s("ngTemplateOutlet",e.startTemplate||e._startTemplate)}}function ac(t,i){t&1&&w(0,"BarsIcon")}function sc(t,i){}function lc(t,i){t&1&&f(0,sc,0,0,"ng-template")}function cc(t,i){if(t&1){let e=E();u(0,"a",10,2),v("click",function(r){h(e);let n=l();return g(n.menuButtonClick(r))})("keydown",function(r){h(e);let n=l();return g(n.menuButtonKeydown(r))}),f(2,ac,1,0,"BarsIcon",11)(3,lc,1,0,null,9),p()}if(t&2){let e=l();k("aria-haspopup",!!(e.model.length&&e.model.length>0))("aria-expanded",e.mobileActive)("aria-controls",e.id)("aria-label",e.config.translation.aria.navigation)("data-pc-section","button"),d(2),s("ngIf",!e.menuIconTemplate&&!e._menuIconTemplate),d(),s("ngTemplateOutlet",e.menuIconTemplate||e._menuIconTemplate)}}function dc(t,i){t&1&&U(0)}function uc(t,i){if(t&1&&(u(0,"div",12),f(1,dc,1,0,"ng-container",9),p()),t&2){let e=l();d(),s("ngTemplateOutlet",e.endTemplate||e._endTemplate)}}function pc(t,i){t&1&&(u(0,"div",12),lt(1),p())}var mc=({dt:t})=>`
.p-menubar {
    display: flex;
    align-items: center;
    background: ${t("menubar.background")};
    border: 1px solid ${t("menubar.border.color")};
    border-radius: ${t("menubar.border.radius")};
    color: ${t("menubar.color")};
    padding: ${t("menubar.padding")};
    gap: ${t("menubar.gap")};
}

.p-menubar-start,
.p-megamenu-end {
    display: flex;
    align-items: center;
}

.p-menubar-root-list,
.p-menubar-submenu {
    display: flex;
    margin: 0;
    padding: 0;
    list-style: none;
    outline: 0 none;
}

.p-menubar-root-list {
    align-items: center;
    flex-wrap: wrap;
    gap: ${t("menubar.gap")};
}

.p-menubar-root-list > .p-menubar-item > .p-menubar-item-content {
    border-radius: ${t("menubar.base.item.border.radius")};
}

.p-menubar-root-list > .p-menubar-item > .p-menubar-item-content > .p-menubar-item-link {
    padding: ${t("menubar.base.item.padding")};
}

.p-menubar-item-content {
    transition: background ${t("menubar.transition.duration")}, color ${t("menubar.transition.duration")};
    border-radius: ${t("menubar.item.border.radius")};
    color: ${t("menubar.item.color")};
}

.p-menubar-item-link {
    cursor: pointer;
    display: flex;
    align-items: center;
    text-decoration: none;
    overflow: hidden;
    position: relative;
    color: inherit;
    padding: ${t("menubar.item.padding")};
    gap: ${t("menubar.item.gap")};
    user-select: none;
    outline: 0 none;
}

.p-menubar-item-label {
    line-height: 1;
}

.p-menubar-item-icon {
    color: ${t("menubar.item.icon.color")};
}

.p-menubar-submenu-icon {
    color: ${t("menubar.submenu.icon.color")};
    margin-left: auto;
    font-size: ${t("menubar.submenu.icon.size")};
    width: ${t("menubar.submenu.icon.size")};
    height: ${t("menubar.submenu.icon.size")};
}

.p-menubar-submenu .p-menubar-submenu-icon:dir(rtl) {
    margin-left: 0;
    margin-right: auto;
}

.p-menubar-item.p-focus > .p-menubar-item-content {
    color: ${t("menubar.item.focus.color")};
    background: ${t("menubar.item.focus.background")};
}

.p-menubar-item.p-focus > .p-menubar-item-content .p-menubar-item-icon {
    color: ${t("menubar.item.icon.focus.color")};
}

.p-menubar-item.p-focus > .p-menubar-item-content .p-menubar-submenu-icon {
    color: ${t("menubar.submenu.icon.focus.color")};
}

.p-menubar-item:not(.p-disabled) > .p-menubar-item-content:hover {
    color: ${t("menubar.item.focus.color")};
    background: ${t("menubar.item.focus.background")};
}

.p-menubar-item:not(.p-disabled) > .p-menubar-item-content:hover .p-menubar-item-icon {
    color: ${t("menubar.item.icon.focus.color")};
}

.p-menubar-item:not(.p-disabled) > .p-menubar-item-content:hover .p-menubar-submenu-icon {
    color: ${t("menubar.submenu.icon.focus.color")};
}

.p-menubar-item-active > .p-menubar-item-content {
    color: ${t("menubar.item.active.color")};
    background: ${t("menubar.item.active.background")};
}

.p-menubar-item-active > .p-menubar-item-content .p-menubar-item-icon {
    color: ${t("menubar.item.icon.active.color")};
}

.p-menubar-item-active > .p-menubar-item-content .p-menubar-submenu-icon {
    color: ${t("menubar.submenu.icon.active.color")};
}

.p-menubar-submenu {
    display: none;
    position: absolute;
    min-width: 12.5rem;
    z-index: 1;
    background: ${t("menubar.submenu.background")};
    border: 1px solid ${t("menubar.submenu.border.color")};
    border-radius: ${t("menubar.submenu.border.radius")};
    box-shadow: ${t("menubar.submenu.shadow")};
    color: ${t("menubar.submenu.color")};
    flex-direction: column;
    padding: ${t("menubar.submenu.padding")};
    gap: ${t("menubar.submenu.gap")};
}

.p-menubar-submenu .p-menubar-separator {
    border-top: 1px solid ${t("menubar.separator.border.color")};
}

.p-menubar-submenu .p-menubar-item {
    position: relative;
}

.p-menubar-submenu > .p-menubar-item-active .p-menubar-submenu {
    display: block;
    left: 100%;
    top: 0;
}

.p-menubar-end {
    margin-left: auto;
    align-self: center;
}

.p-menubar-end:dir(rtl) {
    margin-left: 0;
    margin-right: auto;
}

.p-menubar-button {
    display: none;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    width: ${t("menubar.mobile.button.size")};
    height: ${t("menubar.mobile.button.size")};
    position: relative;
    color: ${t("menubar.mobile.button.color")};
    border: 0 none;
    background: transparent;
    border-radius: ${t("menubar.mobile.button.border.radius")};
    transition: background ${t("menubar.transition.duration")}, color ${t("menubar.transition.duration")}, outline-color ${t("menubar.transition.duration")};
    outline-color: transparent;
}

.p-menubar-button:hover {
    color: ${t("menubar.mobile.button.hover.color")};
    background: ${t("menubar.mobile.button.hover.background")};
}

.p-menubar-button:focus-visible {
    box-shadow: ${t("menubar.mobile.button.focus.ring.shadow")};
    outline: ${t("menubar.mobile.button.focus.ring.width")} ${t("menubar.mobile.button.focus.ring.style")} ${t("menubar.mobile.button.focus.ring.color")};
    outline-offset: ${t("menubar.mobile.button.focus.ring.offset")};
}

.p-menubar-mobile {
    position: relative;
}

.p-menubar-mobile .p-menubar-button {
    display: flex;
}

.p-menubar-mobile .p-menubar-root-list {
    position: absolute;
    display: none;
    width: 100%;
    padding: ${t("menubar.submenu.padding")};
    background: ${t("menubar.submenu.background")};
    border: 1px solid ${t("menubar.submenu.border.color")};
    box-shadow: ${t("menubar.submenu.shadow")};
}

.p-menubar-mobile .p-menubar-root-list > .p-menubar-item > .p-menubar-item-content {
    border-radius: ${t("menubar.item.border.radius")};
}

.p-menubar-mobile .p-menubar-root-list > .p-menubar-item > .p-menubar-item-content > .p-menubar-item-link {
    padding: ${t("menubar.item.padding")};
}

.p-menubar-mobile-active .p-menubar-root-list {
    display: flex;
    flex-direction: column;
    top: 100%;
    left: 0;
    z-index: 1;
}

.p-menubar-mobile .p-menubar-root-list:dir(rtl) {
    left: auto;
    right: 0;
}

.p-menubar-mobile .p-menubar-root-list .p-menubar-item {
    width: 100%;
    position: static;
}

.p-menubar-mobile .p-menubar-root-list .p-menubar-separator {
    border-top: 1px solid ${t("menubar.separator.border.color")};
}

.p-menubar-mobile .p-menubar-root-list > .p-menubar-item > .p-menubar-item-content .p-menubar-submenu-icon {
    margin-left: auto;
    transition: transform 0.2s;
}

.p-menubar-mobile .p-menubar-root-list > .p-menubar-item > .p-menubar-item-content .p-menubar-submenu-icon:dir(rtl) {
    margin-left: 0;
    margin-right: auto;
}

.p-menubar-mobile .p-menubar-root-list > .p-menubar-item-active > .p-menubar-item-content .p-menubar-submenu-icon {
    transform: rotate(-180deg);
}

.p-menubar-mobile .p-menubar-submenu .p-menubar-submenu-icon {
    transition: transform 0.2s;
    transform: rotate(90deg);
}

.p-menubar-mobile  .p-menubar-item-active > .p-menubar-item-content .p-menubar-submenu-icon {
    transform: rotate(-90deg);
}

.p-menubar-mobile .p-menubar-submenu {
    width: 100%;
    position: static;
    box-shadow: none;
    border: 0 none;
    padding-left: ${t("menubar.submenu.mobile.indent")};
}
`;var fc={root:({instance:t})=>["p-menubar p-component",{"p-menubar-mobile":t.queryMatches,"p-menubar-mobile-active":t.mobileActive}],start:"p-menubar-start",button:"p-menubar-button",rootList:"p-menubar-root-list",item:({instance:t,processedItem:i})=>["p-menubar-item",{"p-menubar-item-active":t.isItemActive(i),"p-focus":t.isItemFocused(i),"p-disabled":t.isItemDisabled(i)}],itemContent:"p-menubar-item-content",itemLink:"p-menubar-item-link",itemIcon:"p-menubar-item-icon",itemLabel:"p-menubar-item-label",submenuIcon:"p-menubar-submenu-icon",submenu:"p-menubar-submenu",separator:"p-menubar-separator",end:"p-menubar-end"},ds=(()=>{class t extends Ce{name="menubar";theme=mc;classes=fc;static \u0275fac=(()=>{let e;return function(r){return(e||(e=j(t)))(r||t)}})();static \u0275prov=B({token:t,factory:t.\u0275fac})}return t})();var hr=(()=>{class t{autoHide;autoHideDelay;mouseLeaves=new Xe;mouseLeft$=this.mouseLeaves.pipe(Fr(()=>Br(this.autoHideDelay)),$(e=>this.autoHide&&e));static \u0275fac=function(o){return new(o||t)};static \u0275prov=B({token:t,factory:t.\u0275fac})}return t})(),hc=(()=>{class t extends ue{items;itemTemplate;root=!1;autoZIndex=!0;baseZIndex=0;mobileActive;autoDisplay;menuId;ariaLabel;ariaLabelledBy;level=0;focusedItemId;activeItemPath;inlineStyles;submenuiconTemplate;itemClick=new V;itemMouseEnter=new V;menuFocus=new V;menuBlur=new V;menuKeydown=new V;menubarViewChild;mouseLeaveSubscriber;menubarService=y(hr);ngOnInit(){super.ngOnInit(),this.mouseLeaveSubscriber=this.menubarService.mouseLeft$.subscribe(()=>{this.cd.markForCheck()})}onItemClick(e,o){this.getItemProp(o,"command",{originalEvent:e,item:o.item}),this.itemClick.emit({originalEvent:e,processedItem:o,isFocus:!0})}getItemProp(e,o,r=null){return e&&e.item?jo(e.item[o],r):void 0}getItemId(e){return e.item&&e.item?.id?e.item.id:`${this.menuId}_${e.key}`}getItemKey(e){return this.getItemId(e)}getItemLabelId(e){return`${this.menuId}_${e.key}_label`}getItemClass(e){return ze(se({},this.getItemProp(e,"class")),{"p-menubar-item":!0,"p-menubar-item-active":this.isItemActive(e),"p-focus":this.isItemFocused(e),"p-disabled":this.isItemDisabled(e)})}getItemLabel(e){return this.getItemProp(e,"label")}getSeparatorItemClass(e){return ze(se({},this.getItemProp(e,"class")),{"p-menubar-separator":!0})}isItemVisible(e){return this.getItemProp(e,"visible")!==!1}isItemActive(e){if(this.activeItemPath)return this.activeItemPath.some(o=>o.key===e.key)}isItemDisabled(e){return this.getItemProp(e,"disabled")}isItemFocused(e){return this.focusedItemId===this.getItemId(e)}isItemGroup(e){return De(e.items)}getAriaSetSize(){return this.items.filter(e=>this.isItemVisible(e)&&!this.getItemProp(e,"separator")).length}getAriaPosInset(e){return e-this.items.slice(0,e).filter(o=>this.isItemVisible(o)&&this.getItemProp(o,"separator")).length+1}onItemMouseLeave(){this.menubarService.mouseLeaves.next(!0)}onItemMouseEnter(e){if(this.autoDisplay){this.menubarService.mouseLeaves.next(!1);let{event:o,processedItem:r}=e;this.itemMouseEnter.emit({originalEvent:o,processedItem:r})}}ngOnDestroy(){this.mouseLeaveSubscriber?.unsubscribe(),super.ngOnDestroy()}static \u0275fac=(()=>{let e;return function(r){return(e||(e=j(t)))(r||t)}})();static \u0275cmp=F({type:t,selectors:[["p-menubarSub"],["p-menubarsub"]],viewQuery:function(o,r){if(o&1&&H(fl,7),o&2){let n;I(n=S())&&(r.menubarViewChild=n.first)}},inputs:{items:"items",itemTemplate:"itemTemplate",root:[2,"root","root",T],autoZIndex:[2,"autoZIndex","autoZIndex",T],baseZIndex:[2,"baseZIndex","baseZIndex",G],mobileActive:[2,"mobileActive","mobileActive",T],autoDisplay:[2,"autoDisplay","autoDisplay",T],menuId:"menuId",ariaLabel:"ariaLabel",ariaLabelledBy:"ariaLabelledBy",level:[2,"level","level",G],focusedItemId:"focusedItemId",activeItemPath:"activeItemPath",inlineStyles:"inlineStyles",submenuiconTemplate:"submenuiconTemplate"},outputs:{itemClick:"itemClick",itemMouseEnter:"itemMouseEnter",menuFocus:"menuFocus",menuBlur:"menuBlur",menuKeydown:"menuKeydown"},features:[ae],decls:3,vars:12,consts:[["menubar",""],["listItem",""],["htmlLabel",""],["htmlRouteLabel",""],["role","menubar",3,"focus","blur","keydown","ngClass","tabindex","ngStyle"],["ngFor","",3,"ngForOf"],["role","separator",3,"style","ngClass",4,"ngIf"],["role","menuitem","pTooltip","",3,"ngStyle","ngClass","class","tooltipOptions",4,"ngIf"],["role","separator",3,"ngClass"],["role","menuitem","pTooltip","",3,"ngStyle","ngClass","tooltipOptions"],[1,"p-menubar-item-content",3,"click","mouseenter"],[4,"ngIf"],[3,"itemTemplate","items","mobileActive","autoDisplay","menuId","activeItemPath","focusedItemId","level","ariaLabelledBy","inlineStyles","itemClick","itemMouseEnter",4,"ngIf"],["pRipple","",3,"target","ngClass",4,"ngIf"],["pRipple","",3,"routerLink","queryParams","routerLinkActive","routerLinkActiveOptions","target","ngClass","fragment","queryParamsHandling","preserveFragment","skipLocationChange","replaceUrl","state",4,"ngIf"],["pRipple","",3,"target","ngClass"],["class","p-menubar-item-icon",3,"ngClass","ngStyle",4,"ngIf"],["class","p-menubar-item-label",3,"id",4,"ngIf","ngIfElse"],[3,"styleClass","value",4,"ngIf"],[1,"p-menubar-item-icon",3,"ngClass","ngStyle"],[1,"p-menubar-item-label",3,"id"],[1,"p-menubar-item-label",3,"innerHTML","id"],[3,"styleClass","value"],[4,"ngTemplateOutlet"],["class","p-menubar-submenu-icon",4,"ngIf"],[1,"p-menubar-submenu-icon"],[3,"data-pc-section"],["pRipple","",3,"routerLink","queryParams","routerLinkActive","routerLinkActiveOptions","target","ngClass","fragment","queryParamsHandling","preserveFragment","skipLocationChange","replaceUrl","state"],["class","p-menubar-item-label",4,"ngIf","ngIfElse"],[1,"p-menubar-item-label"],[1,"p-menubar-item-label",3,"innerHTML"],[4,"ngTemplateOutlet","ngTemplateOutletContext"],[3,"itemClick","itemMouseEnter","itemTemplate","items","mobileActive","autoDisplay","menuId","activeItemPath","focusedItemId","level","ariaLabelledBy","inlineStyles"]],template:function(o,r){if(o&1){let n=E();u(0,"ul",4,0),v("focus",function(c){return h(n),g(r.menuFocus.emit(c))})("blur",function(c){return h(n),g(r.menuBlur.emit(c))})("keydown",function(c){return h(n),g(r.menuKeydown.emit(c))}),f(2,ql,2,2,"ng-template",5),p()}o&2&&(s("ngClass",J(9,hl,!r.root,r.root))("tabindex",0)("ngStyle",r.inlineStyles),k("data-pc-section","menu")("aria-label",r.ariaLabel)("aria-labelledBy",r.ariaLabelledBy)("id",r.root?r.menuId:null)("aria-activedescendant",r.focusedItemId),d(2),s("ngForOf",r.items))},dependencies:[t,R,oe,Ct,fe,Ee,le,St,ao,so,Ot,Ze,it,Mi,Oi,Bt,fo,z],encapsulation:2})}return t})(),br=(()=>{class t extends ue{document;platformId;el;renderer;cd;menubarService;set model(e){this._model=e,this._processedItems=this.createProcessedItems(this._model||[])}get model(){return this._model}style;styleClass;autoZIndex=!0;baseZIndex=0;autoDisplay=!1;autoHide;breakpoint="960px";autoHideDelay=100;id;ariaLabel;ariaLabelledBy;onFocus=new V;onBlur=new V;menubutton;rootmenu;mobileActive;matchMediaListener;query;queryMatches;outsideClickListener;resizeListener;mouseLeaveSubscriber;dirty=!1;focused=!1;activeItemPath=D([]);number=D(0);focusedItemInfo=D({index:-1,level:0,parentKey:"",item:null});searchValue="";searchTimeout;_processedItems;_componentStyle=y(ds);_model;get visibleItems(){let e=this.activeItemPath().find(o=>o.key===this.focusedItemInfo().parentKey);return e?e.items:this.processedItems}get processedItems(){return(!this._processedItems||!this._processedItems.length)&&(this._processedItems=this.createProcessedItems(this.model||[])),this._processedItems}get focusedItemId(){let e=this.focusedItemInfo();return e.item&&e.item?.id?e.item.id:e.index!==-1?`${this.id}${De(e.parentKey)?"_"+e.parentKey:""}_${e.index}`:null}constructor(e,o,r,n,a,c){super(),this.document=e,this.platformId=o,this.el=r,this.renderer=n,this.cd=a,this.menubarService=c,rt(()=>{let m=this.activeItemPath();De(m)?(this.bindOutsideClickListener(),this.bindResizeListener()):(this.unbindOutsideClickListener(),this.unbindResizeListener())})}ngOnInit(){super.ngOnInit(),this.bindMatchMediaListener(),this.menubarService.autoHide=this.autoHide,this.menubarService.autoHideDelay=this.autoHideDelay,this.mouseLeaveSubscriber=this.menubarService.mouseLeft$.subscribe(()=>this.unbindOutsideClickListener()),this.id=this.id||Mt("pn_id_")}startTemplate;endTemplate;itemTemplate;menuIconTemplate;submenuIconTemplate;templates;_startTemplate;_endTemplate;_itemTemplate;_menuIconTemplate;_submenuIconTemplate;ngAfterContentInit(){this.templates?.forEach(e=>{switch(e.getType()){case"start":this._startTemplate=e.template;break;case"end":this._endTemplate=e.template;break;case"menuicon":this._menuIconTemplate=e.template;break;case"submenuicon":this._submenuIconTemplate=e.template;break;case"item":this._itemTemplate=e.template;break;default:this._itemTemplate=e.template;break}})}createProcessedItems(e,o=0,r={},n=""){let a=[];return e&&e.forEach((c,m)=>{let _=(n!==""?n+"_":"")+m,x={item:c,index:m,level:o,key:_,parent:r,parentKey:n};x.items=this.createProcessedItems(c.items,o+1,x,_),a.push(x)}),a}bindMatchMediaListener(){if(he(this.platformId)&&!this.matchMediaListener){let e=window.matchMedia(`(max-width: ${this.breakpoint})`);this.query=e,this.queryMatches=e.matches,this.matchMediaListener=()=>{this.queryMatches=e.matches,this.mobileActive=!1,this.cd.markForCheck()},e.addEventListener("change",this.matchMediaListener)}}unbindMatchMediaListener(){this.matchMediaListener&&(this.query.removeEventListener("change",this.matchMediaListener),this.matchMediaListener=null)}getItemProp(e,o){return e?jo(e[o]):void 0}menuButtonClick(e){this.toggle(e)}menuButtonKeydown(e){(e.code==="Enter"||e.code==="Space")&&this.menuButtonClick(e)}onItemClick(e){let{originalEvent:o,processedItem:r}=e,n=this.isProcessedItemGroup(r),a=ut(r.parent);if(this.isSelected(r)){let{index:m,key:_,level:x,parentKey:b,item:M}=r;this.activeItemPath.set(this.activeItemPath().filter(C=>_!==C.key&&_.startsWith(C.key))),this.focusedItemInfo.set({index:m,level:x,parentKey:b,item:M}),this.dirty=!a,te(this.rootmenu.menubarViewChild.nativeElement)}else if(n)this.onItemChange(e);else{let m=a?r:this.activeItemPath().find(_=>_.parentKey==="");this.hide(o),this.changeFocusedItemIndex(o,m?m.index:-1),this.mobileActive=!1,te(this.rootmenu.menubarViewChild.nativeElement)}}onItemMouseEnter(e){Nt()||this.mobileActive||this.onItemChange(e)}changeFocusedItemIndex(e,o){let r=this.findVisibleItem(o);if(this.focusedItemInfo().index!==o){let n=this.focusedItemInfo();this.focusedItemInfo.set(ze(se({},n),{item:r.item,index:o})),this.scrollInView()}}scrollInView(e=-1){let o=e!==-1?`${this.id}_${e}`:this.focusedItemId,r=Me(this.rootmenu.el.nativeElement,`li[id="${o}"]`);r&&r.scrollIntoView&&r.scrollIntoView({block:"nearest",inline:"nearest"})}onItemChange(e){let{processedItem:o,isFocus:r}=e;if(ut(o))return;let{index:n,key:a,level:c,parentKey:m,items:_,item:x}=o,b=De(_),M=this.activeItemPath().filter(C=>C.parentKey!==m&&C.parentKey!==a);b&&M.push(o),this.focusedItemInfo.set({index:n,level:c,parentKey:m,item:x}),this.activeItemPath.set(M),b&&(this.dirty=!0),r&&te(this.rootmenu.menubarViewChild.nativeElement)}toggle(e){this.mobileActive?(this.mobileActive=!1,qe.clear(this.rootmenu.el.nativeElement),this.hide()):(this.mobileActive=!0,qe.set("menu",this.rootmenu.el.nativeElement,this.config.zIndex.menu),setTimeout(()=>{this.show()},0)),this.bindOutsideClickListener(),e.preventDefault()}hide(e,o){this.mobileActive&&setTimeout(()=>{te(this.menubutton.nativeElement)},0),this.activeItemPath.set([]),this.focusedItemInfo.set({index:-1,level:0,parentKey:"",item:null}),o&&te(this.rootmenu?.menubarViewChild.nativeElement),this.dirty=!1}show(){let e=this.findVisibleItem(this.findFirstFocusedItemIndex());this.focusedItemInfo.set({index:this.findFirstFocusedItemIndex(),level:0,parentKey:"",item:e?.item}),te(this.rootmenu?.menubarViewChild.nativeElement)}onMenuFocus(e){this.focused=!0;let o=this.findVisibleItem(this.findFirstFocusedItemIndex()),r=this.focusedItemInfo().index!==-1?this.focusedItemInfo():{index:this.findFirstFocusedItemIndex(),level:0,parentKey:"",item:o?.item};this.focusedItemInfo.set(r),this.onFocus.emit(e)}onMenuBlur(e){this.focused=!1,this.focusedItemInfo.set({index:-1,level:0,parentKey:"",item:null}),this.searchValue="",this.dirty=!1,this.onBlur.emit(e)}onKeyDown(e){let o=e.metaKey||e.ctrlKey;switch(e.code){case"ArrowDown":this.onArrowDownKey(e);break;case"ArrowUp":this.onArrowUpKey(e);break;case"ArrowLeft":this.onArrowLeftKey(e);break;case"ArrowRight":this.onArrowRightKey(e);break;case"Home":this.onHomeKey(e);break;case"End":this.onEndKey(e);break;case"Space":this.onSpaceKey(e);break;case"Enter":this.onEnterKey(e);break;case"Escape":this.onEscapeKey(e);break;case"Tab":this.onTabKey(e);break;case"PageDown":case"PageUp":case"Backspace":case"ShiftLeft":case"ShiftRight":break;default:!o&&co(e.key)&&this.searchItems(e,e.key);break}}findVisibleItem(e){return De(this.visibleItems)?this.visibleItems[e]:null}findFirstFocusedItemIndex(){let e=this.findSelectedItemIndex();return e<0?this.findFirstItemIndex():e}findFirstItemIndex(){return this.visibleItems.findIndex(e=>this.isValidItem(e))}findSelectedItemIndex(){return this.visibleItems.findIndex(e=>this.isValidSelectedItem(e))}isProcessedItemGroup(e){return e&&De(e.items)}isSelected(e){return this.activeItemPath().some(o=>o.key===e.key)}isValidSelectedItem(e){return this.isValidItem(e)&&this.isSelected(e)}isValidItem(e){return!!e&&!this.isItemDisabled(e.item)&&!this.isItemSeparator(e.item)}isItemDisabled(e){return this.getItemProp(e,"disabled")}isItemSeparator(e){return this.getItemProp(e,"separator")}isItemMatched(e){return this.isValidItem(e)&&this.getProccessedItemLabel(e).toLocaleLowerCase().startsWith(this.searchValue.toLocaleLowerCase())}isProccessedItemGroup(e){return e&&De(e.items)}searchItems(e,o){this.searchValue=(this.searchValue||"")+o;let r=-1,n=!1;return this.focusedItemInfo().index!==-1?(r=this.visibleItems.slice(this.focusedItemInfo().index).findIndex(a=>this.isItemMatched(a)),r=r===-1?this.visibleItems.slice(0,this.focusedItemInfo().index).findIndex(a=>this.isItemMatched(a)):r+this.focusedItemInfo().index):r=this.visibleItems.findIndex(a=>this.isItemMatched(a)),r!==-1&&(n=!0),r===-1&&this.focusedItemInfo().index===-1&&(r=this.findFirstFocusedItemIndex()),r!==-1&&this.changeFocusedItemIndex(e,r),this.searchTimeout&&clearTimeout(this.searchTimeout),this.searchTimeout=setTimeout(()=>{this.searchValue="",this.searchTimeout=null},500),n}getProccessedItemLabel(e){return e?this.getItemLabel(e.item):void 0}getItemLabel(e){return this.getItemProp(e,"label")}onArrowDownKey(e){let o=this.visibleItems[this.focusedItemInfo().index];if(o?ut(o.parent):null)this.isProccessedItemGroup(o)&&(this.onItemChange({originalEvent:e,processedItem:o}),this.focusedItemInfo.set({index:-1,parentKey:o.key,item:o.item}),this.onArrowRightKey(e));else{let n=this.focusedItemInfo().index!==-1?this.findNextItemIndex(this.focusedItemInfo().index):this.findFirstFocusedItemIndex();this.changeFocusedItemIndex(e,n),e.preventDefault()}}onArrowRightKey(e){let o=this.visibleItems[this.focusedItemInfo().index];if(o?this.activeItemPath().find(n=>n.key===o.parentKey):null)this.isProccessedItemGroup(o)&&(this.onItemChange({originalEvent:e,processedItem:o}),this.focusedItemInfo.set({index:-1,parentKey:o.key,item:o.item}),this.onArrowDownKey(e));else{let n=this.focusedItemInfo().index!==-1?this.findNextItemIndex(this.focusedItemInfo().index):this.findFirstFocusedItemIndex();this.changeFocusedItemIndex(e,n),e.preventDefault()}}onArrowUpKey(e){let o=this.visibleItems[this.focusedItemInfo().index];if(ut(o.parent)){if(this.isProccessedItemGroup(o)){this.onItemChange({originalEvent:e,processedItem:o}),this.focusedItemInfo.set({index:-1,parentKey:o.key,item:o.item});let a=this.findLastItemIndex();this.changeFocusedItemIndex(e,a)}}else{let n=this.activeItemPath().find(a=>a.key===o.parentKey);if(this.focusedItemInfo().index===0){this.focusedItemInfo.set({index:-1,parentKey:n?n.parentKey:"",item:o.item}),this.searchValue="",this.onArrowLeftKey(e);let a=this.activeItemPath().filter(c=>c.parentKey!==this.focusedItemInfo().parentKey);this.activeItemPath.set(a)}else{let a=this.focusedItemInfo().index!==-1?this.findPrevItemIndex(this.focusedItemInfo().index):this.findLastFocusedItemIndex();this.changeFocusedItemIndex(e,a)}}e.preventDefault()}onArrowLeftKey(e){let o=this.visibleItems[this.focusedItemInfo().index],r=o?this.activeItemPath().find(n=>n.key===o.parentKey):null;if(r){this.onItemChange({originalEvent:e,processedItem:r});let n=this.activeItemPath().filter(a=>a.parentKey!==this.focusedItemInfo().parentKey);this.activeItemPath.set(n),e.preventDefault()}else{let n=this.focusedItemInfo().index!==-1?this.findPrevItemIndex(this.focusedItemInfo().index):this.findLastFocusedItemIndex();this.changeFocusedItemIndex(e,n),e.preventDefault()}}onHomeKey(e){this.changeFocusedItemIndex(e,this.findFirstItemIndex()),e.preventDefault()}onEndKey(e){this.changeFocusedItemIndex(e,this.findLastItemIndex()),e.preventDefault()}onSpaceKey(e){this.onEnterKey(e)}onEscapeKey(e){this.hide(e,!0),this.focusedItemInfo().index=this.findFirstFocusedItemIndex(),e.preventDefault()}onTabKey(e){if(this.focusedItemInfo().index!==-1){let o=this.visibleItems[this.focusedItemInfo().index];!this.isProccessedItemGroup(o)&&this.onItemChange({originalEvent:e,processedItem:o})}this.hide()}onEnterKey(e){if(this.focusedItemInfo().index!==-1){let o=Me(this.rootmenu.el.nativeElement,`li[id="${`${this.focusedItemId}`}"]`),r=o&&Me(o,'a[data-pc-section="action"]');r?r.click():o&&o.click()}e.preventDefault()}findLastFocusedItemIndex(){let e=this.findSelectedItemIndex();return e<0?this.findLastItemIndex():e}findLastItemIndex(){return Et(this.visibleItems,e=>this.isValidItem(e))}findPrevItemIndex(e){let o=e>0?Et(this.visibleItems.slice(0,e),r=>this.isValidItem(r)):-1;return o>-1?o:e}findNextItemIndex(e){let o=e<this.visibleItems.length-1?this.visibleItems.slice(e+1).findIndex(r=>this.isValidItem(r)):-1;return o>-1?o+e+1:e}bindResizeListener(){he(this.platformId)&&(this.resizeListener||(this.resizeListener=this.renderer.listen(this.document.defaultView,"resize",e=>{Nt()||this.hide(e,!0),this.mobileActive=!1})))}bindOutsideClickListener(){he(this.platformId)&&(this.outsideClickListener||(this.outsideClickListener=this.renderer.listen(this.document,"click",e=>{let o=this.rootmenu.el.nativeElement!==e.target&&!this.rootmenu.el.nativeElement.contains(e.target),r=this.mobileActive&&this.menubutton.nativeElement!==e.target&&!this.menubutton.nativeElement.contains(e.target);o&&(r?this.mobileActive=!1:this.hide())})))}unbindOutsideClickListener(){this.outsideClickListener&&(this.outsideClickListener(),this.outsideClickListener=null)}unbindResizeListener(){this.resizeListener&&(this.resizeListener(),this.resizeListener=null)}ngOnDestroy(){this.mouseLeaveSubscriber?.unsubscribe(),this.unbindOutsideClickListener(),this.unbindResizeListener(),this.unbindMatchMediaListener(),super.ngOnDestroy()}static \u0275fac=function(o){return new(o||t)(pe(wt),pe(oo),pe(_t),pe(Nr),pe(Jr),pe(hr))};static \u0275cmp=F({type:t,selectors:[["p-menubar"]],contentQueries:function(o,r,n){if(o&1&&(Z(n,Zl,4),Z(n,Gl,4),Z(n,Yl,4),Z(n,Xl,4),Z(n,Jl,4),Z(n,Ve,4)),o&2){let a;I(a=S())&&(r.startTemplate=a.first),I(a=S())&&(r.endTemplate=a.first),I(a=S())&&(r.itemTemplate=a.first),I(a=S())&&(r.menuIconTemplate=a.first),I(a=S())&&(r.submenuIconTemplate=a.first),I(a=S())&&(r.templates=a)}},viewQuery:function(o,r){if(o&1&&(H(ec,5),H(tc,5)),o&2){let n;I(n=S())&&(r.menubutton=n.first),I(n=S())&&(r.rootmenu=n.first)}},inputs:{model:"model",style:"style",styleClass:"styleClass",autoZIndex:[2,"autoZIndex","autoZIndex",T],baseZIndex:[2,"baseZIndex","baseZIndex",G],autoDisplay:[2,"autoDisplay","autoDisplay",T],autoHide:[2,"autoHide","autoHide",T],breakpoint:"breakpoint",autoHideDelay:[2,"autoHideDelay","autoHideDelay",G],id:"id",ariaLabel:"ariaLabel",ariaLabelledBy:"ariaLabelledBy"},outputs:{onFocus:"onFocus",onBlur:"onBlur"},features:[ve([hr,ds]),ae],ngContentSelectors:oc,decls:8,vars:26,consts:[["rootmenu",""],["legacy",""],["menubutton",""],[3,"ngClass","ngStyle"],["class","p-menubar-start",4,"ngIf"],["tabindex","0","role","button","class","p-menubar-button",3,"click","keydown",4,"ngIf"],[3,"itemClick","menuFocus","menuBlur","menuKeydown","itemMouseEnter","items","itemTemplate","menuId","root","baseZIndex","autoZIndex","mobileActive","autoDisplay","ariaLabel","ariaLabelledBy","focusedItemId","submenuiconTemplate","activeItemPath"],["class","p-menubar-end",4,"ngIf","ngIfElse"],[1,"p-menubar-start"],[4,"ngTemplateOutlet"],["tabindex","0","role","button",1,"p-menubar-button",3,"click","keydown"],[4,"ngIf"],[1,"p-menubar-end"]],template:function(o,r){if(o&1){let n=E();kt(),u(0,"div",3),f(1,nc,2,1,"div",4)(2,cc,4,7,"a",5),u(3,"p-menubarSub",6,0),v("itemClick",function(c){return h(n),g(r.onItemClick(c))})("menuFocus",function(c){return h(n),g(r.onMenuFocus(c))})("menuBlur",function(c){return h(n),g(r.onMenuBlur(c))})("menuKeydown",function(c){return h(n),g(r.onKeyDown(c))})("itemMouseEnter",function(c){return h(n),g(r.onItemMouseEnter(c))}),p(),f(5,uc,2,1,"div",7)(6,pc,2,0,"ng-template",null,1,de),p()}if(o&2){let n=X(7);W(r.styleClass),s("ngClass",J(23,rc,r.queryMatches,r.mobileActive))("ngStyle",r.style),k("data-pc-section","root")("data-pc-name","menubar"),d(),s("ngIf",r.startTemplate||r._startTemplate),d(),s("ngIf",r.model&&r.model.length>0),d(),s("items",r.processedItems)("itemTemplate",r.itemTemplate)("menuId",r.id)("root",!0)("baseZIndex",r.baseZIndex)("autoZIndex",r.autoZIndex)("mobileActive",r.mobileActive)("autoDisplay",r.autoDisplay)("ariaLabel",r.ariaLabel)("ariaLabelledBy",r.ariaLabelledBy)("focusedItemId",r.focused?r.focusedItemId:void 0)("submenuiconTemplate",r.submenuIconTemplate||r._submenuIconTemplate)("activeItemPath",r.activeItemPath()),d(2),s("ngIf",r.endTemplate||r._endTemplate)("ngIfElse",n)}},dependencies:[R,oe,fe,Ee,le,St,hc,Ze,cs,Bt,z],encapsulation:2,changeDetection:0})}return t})(),ps=(()=>{class t{static \u0275fac=function(o){return new(o||t)};static \u0275mod=ne({type:t});static \u0275inj=ie({imports:[br,z,z]})}return t})();var Zt=t=>({height:t}),bc=(t,i,e)=>({"p-select-option":!0,"p-select-option-selected":t,"p-disabled":i,"p-focus":e}),_r=t=>({$implicit:t});function _c(t,i){t&1&&w(0,"CheckIcon",4),t&2&&s("styleClass","p-select-option-check-icon")}function vc(t,i){t&1&&w(0,"BlankIcon",4),t&2&&s("styleClass","p-select-option-blank-icon")}function yc(t,i){if(t&1&&(Q(0),f(1,_c,1,1,"CheckIcon",3)(2,vc,1,1,"BlankIcon",3),q()),t&2){let e=l();d(),s("ngIf",e.selected),d(),s("ngIf",!e.selected)}}function kc(t,i){if(t&1&&(u(0,"span"),A(1),p()),t&2){let e,o=l();d(),_e((e=o.label)!==null&&e!==void 0?e:"empty")}}function wc(t,i){t&1&&U(0)}var Cc=["container"],xc=["filter"],Ic=["focusInput"],Sc=["editableInput"],Tc=["items"],Ec=["scroller"],Mc=["overlay"],Oc=["firstHiddenFocusableEl"],Bc=["lastHiddenFocusableEl"],Dc=()=>({class:"p-select-clear-icon"}),Vc=()=>({class:"p-select-dropdown-icon"}),gs=t=>({options:t}),bs=(t,i)=>({$implicit:t,options:i}),Fc=()=>({});function Lc(t,i){if(t&1&&(Q(0),A(1),q()),t&2){let e=l(2);d(),_e(e.label()==="p-emptylabel"?"\xA0":e.label())}}function Pc(t,i){if(t&1&&U(0,23),t&2){let e=l(2);s("ngTemplateOutlet",e.selectedItemTemplate)("ngTemplateOutletContext",O(2,_r,e.selectedOption))}}function Ac(t,i){if(t&1&&(u(0,"span"),A(1),p()),t&2){let e=l(3);d(),_e(e.label()==="p-emptylabel"?"\xA0":e.label())}}function Rc(t,i){if(t&1&&f(0,Ac,2,1,"span",17),t&2){let e=l(2);s("ngIf",!e.selectedOption)}}function Hc(t,i){if(t&1){let e=E();u(0,"span",21,3),v("focus",function(r){h(e);let n=l();return g(n.onInputFocus(r))})("blur",function(r){h(e);let n=l();return g(n.onInputBlur(r))})("keydown",function(r){h(e);let n=l();return g(n.onKeyDown(r))}),f(2,Lc,2,1,"ng-container",19)(3,Pc,1,4,"ng-container",22)(4,Rc,1,1,"ng-template",null,4,de),p()}if(t&2){let e,o=X(5),r=l();s("ngClass",r.inputClass)("pTooltip",r.tooltip)("tooltipPosition",r.tooltipPosition)("positionStyle",r.tooltipPositionStyle)("tooltipStyleClass",r.tooltipStyleClass)("pAutoFocus",r.autofocus),k("aria-disabled",r.disabled)("id",r.inputId)("aria-label",r.ariaLabel||(r.label()==="p-emptylabel"?void 0:r.label()))("aria-labelledby",r.ariaLabelledBy)("aria-haspopup","listbox")("aria-expanded",(e=r.overlayVisible)!==null&&e!==void 0?e:!1)("aria-controls",r.overlayVisible?r.id+"_list":null)("tabindex",r.disabled?-1:r.tabindex)("aria-activedescendant",r.focused?r.focusedOptionId:void 0)("aria-required",r.required)("required",r.required),d(2),s("ngIf",!r.selectedItemTemplate)("ngIfElse",o),d(),s("ngIf",r.selectedItemTemplate&&r.selectedOption)}}function zc(t,i){if(t&1){let e=E();u(0,"input",24,5),v("input",function(r){h(e);let n=l();return g(n.onEditableInput(r))})("keydown",function(r){h(e);let n=l();return g(n.onKeyDown(r))})("focus",function(r){h(e);let n=l();return g(n.onInputFocus(r))})("blur",function(r){h(e);let n=l();return g(n.onInputBlur(r))}),p()}if(t&2){let e=l();s("ngClass",e.inputClass)("disabled",e.disabled)("pAutoFocus",e.autofocus),k("id",e.inputId)("maxlength",e.maxlength)("placeholder",e.modelValue()===void 0||e.modelValue()===null?e.placeholder():void 0)("aria-label",e.ariaLabel||(e.label()==="p-emptylabel"?void 0:e.label()))("aria-activedescendant",e.focused?e.focusedOptionId:void 0)}}function $c(t,i){if(t&1){let e=E();u(0,"TimesIcon",26),v("click",function(r){h(e);let n=l(2);return g(n.clear(r))}),p()}t&2&&k("data-pc-section","clearicon")}function Nc(t,i){}function Uc(t,i){t&1&&f(0,Nc,0,0,"ng-template")}function jc(t,i){if(t&1){let e=E();u(0,"span",26),v("click",function(r){h(e);let n=l(2);return g(n.clear(r))}),f(1,Uc,1,0,null,27),p()}if(t&2){let e=l(2);k("data-pc-section","clearicon"),d(),s("ngTemplateOutlet",e.clearIconTemplate)("ngTemplateOutletContext",We(3,Dc))}}function Kc(t,i){if(t&1&&(Q(0),f(1,$c,1,1,"TimesIcon",25)(2,jc,2,4,"span",25),q()),t&2){let e=l();d(),s("ngIf",!e.clearIconTemplate),d(),s("ngIf",e.clearIconTemplate)}}function Wc(t,i){t&1&&U(0)}function Qc(t,i){if(t&1&&(Q(0),f(1,Wc,1,0,"ng-container",28),q()),t&2){let e=l(2);d(),s("ngTemplateOutlet",e.loadingIconTemplate)}}function qc(t,i){if(t&1&&w(0,"span",31),t&2){let e=l(3);s("ngClass","p-select-loading-icon pi-spin "+e.loadingIcon)}}function Zc(t,i){t&1&&w(0,"span",32),t&2&&W("p-select-loading-icon pi pi-spinner pi-spin")}function Gc(t,i){if(t&1&&(Q(0),f(1,qc,1,1,"span",29)(2,Zc,1,2,"span",30),q()),t&2){let e=l(2);d(),s("ngIf",e.loadingIcon),d(),s("ngIf",!e.loadingIcon)}}function Yc(t,i){if(t&1&&(Q(0),f(1,Qc,2,1,"ng-container",17)(2,Gc,3,2,"ng-container",17),q()),t&2){let e=l();d(),s("ngIf",e.loadingIconTemplate),d(),s("ngIf",!e.loadingIconTemplate)}}function Xc(t,i){if(t&1&&w(0,"span",36),t&2){let e=l(3);s("ngClass",e.dropdownIcon)}}function Jc(t,i){t&1&&w(0,"ChevronDownIcon",37),t&2&&s("styleClass","p-select-dropdown-icon")}function ed(t,i){if(t&1&&(Q(0),f(1,Xc,1,1,"span",34)(2,Jc,1,1,"ChevronDownIcon",35),q()),t&2){let e=l(2);d(),s("ngIf",e.dropdownIcon),d(),s("ngIf",!e.dropdownIcon)}}function td(t,i){}function od(t,i){t&1&&f(0,td,0,0,"ng-template")}function rd(t,i){if(t&1&&(u(0,"span",38),f(1,od,1,0,null,27),p()),t&2){let e=l(2);d(),s("ngTemplateOutlet",e.dropdownIconTemplate)("ngTemplateOutletContext",We(2,Vc))}}function id(t,i){if(t&1&&f(0,ed,3,2,"ng-container",17)(1,rd,2,3,"span",33),t&2){let e=l();s("ngIf",!e.dropdownIconTemplate),d(),s("ngIf",e.dropdownIconTemplate)}}function nd(t,i){t&1&&U(0)}function ad(t,i){t&1&&U(0)}function sd(t,i){if(t&1&&(Q(0),f(1,ad,1,0,"ng-container",27),q()),t&2){let e=l(3);d(),s("ngTemplateOutlet",e.filterTemplate)("ngTemplateOutletContext",O(2,gs,e.filterOptions))}}function ld(t,i){t&1&&w(0,"SearchIcon")}function cd(t,i){}function dd(t,i){t&1&&f(0,cd,0,0,"ng-template")}function ud(t,i){if(t&1&&(u(0,"span"),f(1,dd,1,0,null,28),p()),t&2){let e=l(4);d(),s("ngTemplateOutlet",e.filterIconTemplate)}}function pd(t,i){if(t&1){let e=E();u(0,"p-iconfield")(1,"input",45,10),v("input",function(r){h(e);let n=l(3);return g(n.onFilterInputChange(r))})("keydown",function(r){h(e);let n=l(3);return g(n.onFilterKeyDown(r))})("blur",function(r){h(e);let n=l(3);return g(n.onFilterBlur(r))}),p(),u(3,"p-inputicon"),f(4,ld,1,0,"SearchIcon",17)(5,ud,2,1,"span",17),p()()}if(t&2){let e=l(3);d(),s("value",e._filterValue()||"")("variant",e.variant),k("placeholder",e.filterPlaceholder)("aria-owns",e.id+"_list")("aria-label",e.ariaFilterLabel)("aria-activedescendant",e.focusedOptionId),d(3),s("ngIf",!e.filterIconTemplate),d(),s("ngIf",e.filterIconTemplate)}}function md(t,i){if(t&1){let e=E();u(0,"div",44),v("click",function(r){return h(e),g(r.stopPropagation())}),f(1,sd,2,4,"ng-container",19)(2,pd,6,8,"ng-template",null,9,de),p()}if(t&2){let e=X(3),o=l(2);d(),s("ngIf",o.filterTemplate)("ngIfElse",e)}}function fd(t,i){t&1&&U(0)}function hd(t,i){if(t&1&&f(0,fd,1,0,"ng-container",27),t&2){let e=i.$implicit,o=i.options;l(2);let r=X(9);s("ngTemplateOutlet",r)("ngTemplateOutletContext",J(2,bs,e,o))}}function gd(t,i){t&1&&U(0)}function bd(t,i){if(t&1&&f(0,gd,1,0,"ng-container",27),t&2){let e=i.options,o=l(4);s("ngTemplateOutlet",o.loaderTemplate)("ngTemplateOutletContext",O(2,gs,e))}}function _d(t,i){t&1&&(Q(0),f(1,bd,1,4,"ng-template",null,12,de),q())}function vd(t,i){if(t&1){let e=E();u(0,"p-scroller",46,11),v("onLazyLoad",function(r){h(e);let n=l(2);return g(n.onLazyLoad.emit(r))}),f(2,hd,1,5,"ng-template",null,2,de)(4,_d,3,0,"ng-container",17),p()}if(t&2){let e=l(2);Ne(O(8,Zt,e.scrollHeight)),s("items",e.visibleOptions())("itemSize",e.virtualScrollItemSize||e._itemSize)("autoSize",!0)("lazy",e.lazy)("options",e.virtualScrollOptions),d(4),s("ngIf",e.loaderTemplate)}}function yd(t,i){t&1&&U(0)}function kd(t,i){if(t&1&&(Q(0),f(1,yd,1,0,"ng-container",27),q()),t&2){l();let e=X(9),o=l();d(),s("ngTemplateOutlet",e)("ngTemplateOutletContext",J(3,bs,o.visibleOptions(),We(2,Fc)))}}function wd(t,i){if(t&1&&(u(0,"span"),A(1),p()),t&2){let e=l(2).$implicit,o=l(3);d(),_e(o.getOptionGroupLabel(e.optionGroup))}}function Cd(t,i){t&1&&U(0)}function xd(t,i){if(t&1&&(Q(0),u(1,"li",50),f(2,wd,2,1,"span",17)(3,Cd,1,0,"ng-container",27),p(),q()),t&2){let e=l(),o=e.$implicit,r=e.index,n=l().options,a=l(2);d(),s("ngStyle",O(5,Zt,n.itemSize+"px")),k("id",a.id+"_"+a.getOptionIndex(r,n)),d(),s("ngIf",!a.groupTemplate),d(),s("ngTemplateOutlet",a.groupTemplate)("ngTemplateOutletContext",O(7,_r,o.optionGroup))}}function Id(t,i){if(t&1){let e=E();Q(0),u(1,"p-dropdownItem",51),v("onClick",function(r){h(e);let n=l().$implicit,a=l(3);return g(a.onOptionSelect(r,n))})("onMouseEnter",function(r){h(e);let n=l().index,a=l().options,c=l(2);return g(c.onOptionMouseEnter(r,c.getOptionIndex(n,a)))}),p(),q()}if(t&2){let e=l(),o=e.$implicit,r=e.index,n=l().options,a=l(2);d(),s("id",a.id+"_"+a.getOptionIndex(r,n))("option",o)("checkmark",a.checkmark)("selected",a.isSelected(o))("label",a.getOptionLabel(o))("disabled",a.isOptionDisabled(o))("template",a.itemTemplate)("focused",a.focusedOptionIndex()===a.getOptionIndex(r,n))("ariaPosInset",a.getAriaPosInset(a.getOptionIndex(r,n)))("ariaSetSize",a.ariaSetSize)}}function Sd(t,i){if(t&1&&f(0,xd,4,9,"ng-container",17)(1,Id,2,10,"ng-container",17),t&2){let e=i.$implicit,o=l(3);s("ngIf",o.isOptionGroup(e)),d(),s("ngIf",!o.isOptionGroup(e))}}function Td(t,i){if(t&1&&A(0),t&2){let e=l(4);me(" ",e.emptyFilterMessageLabel," ")}}function Ed(t,i){t&1&&U(0,null,14)}function Md(t,i){if(t&1&&f(0,Ed,2,0,"ng-container",28),t&2){let e=l(4);s("ngTemplateOutlet",e.emptyFilterTemplate||e.emptyTemplate)}}function Od(t,i){if(t&1&&(u(0,"li",52),f(1,Td,1,1)(2,Md,1,1,"ng-container"),p()),t&2){let e=l().options,o=l(2);s("ngStyle",O(2,Zt,e.itemSize+"px")),d(),Be(!o.emptyFilterTemplate&&!o.emptyTemplate?1:2)}}function Bd(t,i){if(t&1&&A(0),t&2){let e=l(4);me(" ",e.emptyMessageLabel," ")}}function Dd(t,i){t&1&&U(0)}function Vd(t,i){if(t&1&&f(0,Dd,1,0,"ng-container",28),t&2){let e=l(4);s("ngTemplateOutlet",e.emptyTemplate)}}function Fd(t,i){if(t&1&&(u(0,"li",52),f(1,Bd,1,1)(2,Vd,1,1,"ng-container"),p()),t&2){let e=l().options,o=l(2);s("ngStyle",O(2,Zt,e.itemSize+"px")),d(),Be(o.emptyTemplate?2:1)}}function Ld(t,i){if(t&1&&(u(0,"ul",47,13),f(2,Sd,2,2,"ng-template",48)(3,Od,3,4,"li",49)(4,Fd,3,4,"li",49),p()),t&2){let e=i.$implicit,o=i.options,r=l(2);Ne(o.contentStyle),s("ngClass",o.contentStyleClass),k("id",r.id+"_list")("aria-label",r.listLabel),d(2),s("ngForOf",e),d(),s("ngIf",r.filterValue&&r.isEmpty()),d(),s("ngIf",!r.filterValue&&r.isEmpty())}}function Pd(t,i){t&1&&U(0)}function Ad(t,i){if(t&1){let e=E();u(0,"div",39)(1,"span",40,6),v("focus",function(r){h(e);let n=l();return g(n.onFirstHiddenFocus(r))}),p(),f(3,nd,1,0,"ng-container",28)(4,md,4,2,"div",41),u(5,"div",42),f(6,vd,5,10,"p-scroller",43)(7,kd,2,6,"ng-container",17)(8,Ld,5,8,"ng-template",null,7,de),p(),f(10,Pd,1,0,"ng-container",28),u(11,"span",40,8),v("focus",function(r){h(e);let n=l();return g(n.onLastHiddenFocus(r))}),p()()}if(t&2){let e=l();W(e.panelStyleClass),s("ngClass","p-select-overlay p-component")("ngStyle",e.panelStyle),d(),k("tabindex",0)("data-p-hidden-accessible",!0)("data-p-hidden-focusable",!0),d(2),s("ngTemplateOutlet",e.headerTemplate),d(),s("ngIf",e.filter),d(),Y("max-height",e.virtualScroll?"auto":e.scrollHeight||"auto"),d(),s("ngIf",e.virtualScroll),d(),s("ngIf",!e.virtualScroll),d(3),s("ngTemplateOutlet",e.footerTemplate),d(),k("tabindex",0)("data-p-hidden-accessible",!0)("data-p-hidden-focusable",!0)}}var Rd=({dt:t})=>`
.p-select {
    display: inline-flex;
    cursor: pointer;
    position: relative;
    user-select: none;
    background: ${t("select.background")};
    border: 1px solid ${t("select.border.color")};
    transition: background ${t("select.transition.duration")}, color ${t("select.transition.duration")}, border-color ${t("select.transition.duration")},
        outline-color ${t("select.transition.duration")}, box-shadow ${t("select.transition.duration")};
    border-radius: ${t("select.border.radius")};
    outline-color: transparent;
    box-shadow: ${t("select.shadow")};
}

.p-select:not(.p-disabled):hover {
    border-color: ${t("select.hover.border.color")};
}

.p-select:not(.p-disabled).p-focus {
    border-color: ${t("select.focus.border.color")};
    box-shadow: ${t("select.focus.ring.shadow")};
    outline: ${t("select.focus.ring.width")} ${t("select.focus.ring.style")} ${t("select.focus.ring.color")};
    outline-offset: ${t("select.focus.ring.offset")};
}

.p-select.p-variant-filled {
    background: ${t("select.filled.background")};
}

.p-select.p-variant-filled.p-focus {
    background: ${t("select.filled.focus.background")};
}

.p-select.p-disabled {
    opacity: 1;
    background: ${t("select.disabled.background")};
}

.p-select-clear-icon {
    position: absolute;
    top: 50%;
    margin-top: -0.5rem;
    color: ${t("select.clear.icon.color")};
    right: ${t("select.dropdown.width")};
}

.p-select-dropdown {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    background: transparent;
    color: ${t("select.dropdown.color")};
    width: ${t("select.dropdown.width")};
    border-start-end-radius: ${t("select.border.radius")};
    border-end-end-radius: ${t("select.border.radius")};
}

.p-select-label {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    flex: 1 1 auto;
    width: 1%;
    padding: ${t("select.padding.y")} ${t("select.padding.x")};
    text-overflow: ellipsis;
    cursor: pointer;
    color: ${t("select.color")};
    background: transparent;
    border: 0 none;
    outline: 0 none;
}

.p-select-label.p-placeholder {
    color: ${t("select.placeholder.color")};
}

.p-select:has(.p-select-clear-icon) .p-select-label {
    padding-right: calc(1rem + ${t("select.padding.x")});
}

.p-select.p-disabled .p-select-label {
    color: ${t("select.disabled.color")};
}

.p-select-label-empty {
    overflow: hidden;
    opacity: 0;
}

input.p-select-label {
    cursor: default;
}

.p-select .p-select-overlay {
    min-width: 100%;
}

.p-select-overlay {
    position: absolute;
    top: 0;
    left: 0;
    background: ${t("select.overlay.background")};
    color: ${t("select.overlay.color")};
    border: 1px solid ${t("select.overlay.border.color")};
    border-radius: ${t("select.overlay.border.radius")};
    box-shadow: ${t("select.overlay.shadow")};
}

.p-select-header {
    padding: ${t("select.list.header.padding")};
}

.p-select-filter {
    width: 100%;
}

.p-select-list-container {
    overflow: auto;
}

.p-select-option-group {
    cursor: auto;
    margin: 0;
    padding: ${t("select.option.group.padding")};
    background: ${t("select.option.group.background")};
    color: ${t("select.option.group.color")};
    font-weight: ${t("select.option.group.font.weight")};
}

.p-select-list {
    margin: 0;
    padding: 0;
    list-style-type: none;
    padding: ${t("select.list.padding")};
    gap: ${t("select.list.gap")};
    display: flex;
    flex-direction: column;
}

.p-select-option {
    cursor: pointer;
    font-weight: normal;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    padding: ${t("select.option.padding")};
    border: 0 none;
    color: ${t("select.option.color")};
    background: transparent;
    transition: background ${t("select.transition.duration")}, color ${t("select.transition.duration")}, border-color ${t("select.transition.duration")},
    box-shadow ${t("select.transition.duration")}, outline-color ${t("select.transition.duration")};
    border-radius: ${t("select.option.border.radius")};
}

.p-select-option:not(.p-select-option-selected):not(.p-disabled).p-focus {
    background: ${t("select.option.focus.background")};
    color: ${t("select.option.focus.color")};
}

.p-select-option.p-select-option-selected {
    background: ${t("select.option.selected.background")};
    color: ${t("select.option.selected.color")};
}

.p-select-option.p-select-option-selected.p-focus {
    background: ${t("select.option.selected.focus.background")};
    color: ${t("select.option.selected.focus.color")};
}

.p-select-option-check-icon {
    position: relative;
    margin-inline-start: ${t("select.checkmark.gutter.start")};
    margin-inline-end: ${t("select.checkmark.gutter.end")};
    color: ${t("select.checkmark.color")};
}

.p-select-empty-message {
    padding: ${t("select.empty.message.padding")};
}

.p-select-fluid {
    display: flex;
}

/*For PrimeNG*/

.p-dropdown.ng-invalid.ng-dirty,
.p-select.ng-invalid.ng-dirty {
    outline: 1px solid ${t("select.invalid.border.color")};
    outline-offset: 0;
}

.p-dropdown.ng-invalid.ng-dirty .p-dropdown-label.p-placeholder,
.p-select.ng-invalid.ng-dirty .p-select-label.p-placeholder {
    color: ${t("select.invalid.placeholder.color")};
}
`,Hd={root:({instance:t})=>["p-dropdown p-select p-component p-inputwrapper",{"p-disabled":t.disabled,"p-variant-filled":t.variant==="filled"||t.config.inputVariant()==="filled"||t.config.inputStyle()==="filled","p-focus":t.focused,"p-inputwrapper-filled":t.modelValue()!==void 0&&t.modelValue()!==null&&!t.modelValue().length,"p-inputwrapper-focus":t.focused||t.overlayVisible,"p-select-open":t.overlayVisible,"p-select-fluid":t.hasFluid,"p-select-sm p-inputfield-sm":t.size==="small","p-select-lg p-inputfield-lg":t.size==="large"}],label:({instance:t,props:i})=>["p-select-label",{"p-placeholder":!i.editable&&t.label===i.placeholder,"p-select-label-empty":!i.editable&&!t.$slots.value&&(t.label==="p-emptylabel"||t.label.length===0)}],clearIcon:"p-select-clear-icon",dropdown:"p-select-dropdown",loadingicon:"p-select-loading-icon",dropdownIcon:"p-select-dropdown-icon",overlay:"p-select-overlay p-component",header:"p-select-header",pcFilter:"p-select-filter",listContainer:"p-select-list-container",list:"p-select-list",optionGroup:"p-select-option-group",optionGroupLabel:"p-select-option-group-label",option:({instance:t,props:i,state:e,option:o,focusedOption:r})=>["p-select-option",{"p-select-option-selected":t.isSelected(o)&&i.highlightOnSelect,"p-focus":e.focusedOptionIndex===r,"p-disabled":t.isOptionDisabled(o)}],optionLabel:"p-select-option-label",optionCheckIcon:"p-select-option-check-icon",optionBlankIcon:"p-select-option-blank-icon",emptyMessage:"p-select-empty-message"},ms=(()=>{class t extends Ce{name="select";theme=Rd;classes=Hd;static \u0275fac=(()=>{let e;return function(r){return(e||(e=j(t)))(r||t)}})();static \u0275prov=B({token:t,factory:t.\u0275fac})}return t})(),fs;fs||(fs={});var zd={provide:po,useExisting:ht(()=>Nd),multi:!0},$d=(()=>{class t extends ue{id;option;selected;focused;label;disabled;visible;itemSize;ariaPosInset;ariaSetSize;template;checkmark;onClick=new V;onMouseEnter=new V;onOptionClick(e){this.onClick.emit(e)}onOptionMouseEnter(e){this.onMouseEnter.emit(e)}static \u0275fac=(()=>{let e;return function(r){return(e||(e=j(t)))(r||t)}})();static \u0275cmp=F({type:t,selectors:[["p-dropdownItem"]],inputs:{id:"id",option:"option",selected:[2,"selected","selected",T],focused:[2,"focused","focused",T],label:"label",disabled:[2,"disabled","disabled",T],visible:[2,"visible","visible",T],itemSize:[2,"itemSize","itemSize",G],ariaPosInset:"ariaPosInset",ariaSetSize:"ariaSetSize",template:"template",checkmark:[2,"checkmark","checkmark",T]},outputs:{onClick:"onClick",onMouseEnter:"onMouseEnter"},standalone:!1,features:[ae],decls:4,vars:22,consts:[["role","option","pRipple","",3,"click","mouseenter","id","ngStyle","ngClass"],[4,"ngIf"],[4,"ngTemplateOutlet","ngTemplateOutletContext"],[3,"styleClass",4,"ngIf"],[3,"styleClass"]],template:function(o,r){o&1&&(u(0,"li",0),v("click",function(a){return r.onOptionClick(a)})("mouseenter",function(a){return r.onOptionMouseEnter(a)}),f(1,yc,3,2,"ng-container",1)(2,kc,2,1,"span",1)(3,wc,1,0,"ng-container",2),p()),o&2&&(s("id",r.id)("ngStyle",O(14,Zt,r.itemSize+"px"))("ngClass",Zr(16,bc,r.selected,r.disabled,r.focused)),k("aria-label",r.label)("aria-setsize",r.ariaSetSize)("aria-posinset",r.ariaPosInset)("aria-selected",r.selected)("data-p-focused",r.focused)("data-p-highlight",r.selected)("data-p-disabled",r.disabled),d(),s("ngIf",r.checkmark),d(),s("ngIf",!r.template),d(),s("ngTemplateOutlet",r.template)("ngTemplateOutletContext",O(20,_r,r.option)))},dependencies:()=>[oe,fe,Ee,le,Ot,Wo,Qo],encapsulation:2})}return t})(),Nd=(()=>{class t extends ue{zone;filterService;id;scrollHeight="200px";filter;name;style;panelStyle;styleClass;panelStyleClass;readonly;required;editable;appendTo;tabindex=0;set placeholder(e){this._placeholder.set(e)}get placeholder(){return this._placeholder.asReadonly()}loadingIcon;filterPlaceholder;filterLocale;variant;inputId;dataKey;filterBy;filterFields;autofocus;resetFilterOnHide=!1;checkmark=!1;dropdownIcon;loading=!1;optionLabel;optionValue;optionDisabled;optionGroupLabel="label";optionGroupChildren="items";autoDisplayFirst=!0;group;showClear;emptyFilterMessage="";emptyMessage="";lazy=!1;virtualScroll;virtualScrollItemSize;virtualScrollOptions;overlayOptions;ariaFilterLabel;ariaLabel;ariaLabelledBy;filterMatchMode="contains";maxlength;tooltip="";tooltipPosition="right";tooltipPositionStyle="absolute";tooltipStyleClass;focusOnHover=!1;selectOnFocus=!1;autoOptionFocus=!0;autofocusFilter=!0;fluid;get disabled(){return this._disabled}set disabled(e){e&&(this.focused=!1,this.overlayVisible&&this.hide()),this._disabled=e,this.cd.destroyed||this.cd.detectChanges()}get itemSize(){return this._itemSize}set itemSize(e){this._itemSize=e,console.log("The itemSize property is deprecated, use virtualScrollItemSize property instead.")}_itemSize;get autoZIndex(){return this._autoZIndex}set autoZIndex(e){this._autoZIndex=e,console.log("The autoZIndex property is deprecated since v14.2.0, use overlayOptions property instead.")}_autoZIndex;get baseZIndex(){return this._baseZIndex}set baseZIndex(e){this._baseZIndex=e,console.log("The baseZIndex property is deprecated since v14.2.0, use overlayOptions property instead.")}_baseZIndex;get showTransitionOptions(){return this._showTransitionOptions}set showTransitionOptions(e){this._showTransitionOptions=e,console.log("The showTransitionOptions property is deprecated since v14.2.0, use overlayOptions property instead.")}_showTransitionOptions;get hideTransitionOptions(){return this._hideTransitionOptions}set hideTransitionOptions(e){this._hideTransitionOptions=e,console.log("The hideTransitionOptions property is deprecated since v14.2.0, use overlayOptions property instead.")}_hideTransitionOptions;get filterValue(){return this._filterValue()}set filterValue(e){setTimeout(()=>{this._filterValue.set(e)})}get options(){return this._options()}set options(e){wi(e,this._options())||this._options.set(e)}onChange=new V;onFilter=new V;onFocus=new V;onBlur=new V;onClick=new V;onShow=new V;onHide=new V;onClear=new V;onLazyLoad=new V;_componentStyle=y(ms);containerViewChild;filterViewChild;focusInputViewChild;editableInputViewChild;itemsViewChild;scroller;overlayViewChild;firstHiddenFocusableElementOnOverlay;lastHiddenFocusableElementOnOverlay;get hostClass(){return this._componentStyle.classes.root({instance:this}).map(o=>typeof o=="string"?o:Object.keys(o).filter(r=>o[r]).join(" ")).join(" ")+" "+this.styleClass}get hostStyle(){return this.style}_disabled;itemsWrapper;itemTemplate;groupTemplate;loaderTemplate;selectedItemTemplate;headerTemplate;filterTemplate;footerTemplate;emptyFilterTemplate;emptyTemplate;dropdownIconTemplate;loadingIconTemplate;clearIconTemplate;filterIconTemplate;filterOptions;_options=D(null);_placeholder=D(void 0);modelValue=D(null);value;onModelChange=()=>{};onModelTouched=()=>{};hover;focused;overlayVisible;optionsChanged;panel;selectedOptionUpdated;_filterValue=D(null);searchValue;searchTimeout;preventModelTouched;focusedOptionIndex=D(-1);clicked=D(!1);get emptyMessageLabel(){return this.emptyMessage||this.config.getTranslation(uo.EMPTY_MESSAGE)}get emptyFilterMessageLabel(){return this.emptyFilterMessage||this.config.getTranslation(uo.EMPTY_FILTER_MESSAGE)}get isVisibleClearIcon(){return this.modelValue()!=null&&this.hasSelectedOption()&&this.showClear&&!this.disabled}get listLabel(){return this.config.getTranslation(uo.ARIA).listLabel}get hasFluid(){let o=this.el.nativeElement.closest("p-fluid");return this.fluid||!!o}get inputClass(){let e=this.label();return{"p-select-label":!0,"p-placeholder":this.placeholder()&&e===this.placeholder(),"p-select-label-empty":!this.editable&&!this.selectedItemTemplate&&(e==null||e==="p-emptylabel"||e.length===0)}}get focusedOptionId(){return this.focusedOptionIndex()!==-1?`${this.id}_${this.focusedOptionIndex()}`:null}visibleOptions=ee(()=>{let e=this.getAllVisibleAndNonVisibleOptions();if(this._filterValue()){let r=!(this.filterBy||this.optionLabel)&&!this.filterFields&&!this.optionValue?this.options.filter(n=>n.label?n.label.toString().toLowerCase().indexOf(this._filterValue().toLowerCase().trim())!==-1:n.toString().toLowerCase().indexOf(this._filterValue().toLowerCase().trim())!==-1):this.filterService.filter(e,this.searchFields(),this._filterValue().trim(),this.filterMatchMode,this.filterLocale);if(this.group){let n=this.options||[],a=[];return n.forEach(c=>{let _=this.getOptionGroupChildren(c).filter(x=>r.includes(x));_.length>0&&a.push(ze(se({},c),{[typeof this.optionGroupChildren=="string"?this.optionGroupChildren:"items"]:[..._]}))}),this.flatOptions(a)}return r}return e});label=ee(()=>{let e=this.getAllVisibleAndNonVisibleOptions(),o=e.findIndex(r=>this.isOptionValueEqualsModelValue(r));return o!==-1?this.getOptionLabel(e[o]):this.placeholder()||"p-emptylabel"});filled=ee(()=>typeof this.modelValue()=="string"?!!this.modelValue():this.label()!=="p-emptylabel"&&this.modelValue()!==void 0&&this.modelValue()!==null);selectedOption;editableInputValue=ee(()=>this.getOptionLabel(this.selectedOption)||this.modelValue()||"");constructor(e,o){super(),this.zone=e,this.filterService=o,rt(()=>{let r=this.modelValue(),n=this.visibleOptions();if(n&&De(n)){let a=this.findSelectedOptionIndex();(a!==-1||r===void 0||typeof r=="string"&&r.length===0||this.isModelValueNotSet()||this.editable)&&(this.selectedOption=n[a])}ut(n)&&(r===void 0||this.isModelValueNotSet())&&De(this.selectedOption)&&(this.selectedOption=null),r!==void 0&&this.editable&&this.updateEditableLabel(),this.cd.markForCheck()})}isModelValueNotSet(){return this.modelValue()===null&&!this.isOptionValueEqualsModelValue(this.selectedOption)}getAllVisibleAndNonVisibleOptions(){return this.group?this.flatOptions(this.options):this.options||[]}ngOnInit(){super.ngOnInit(),console.log("Dropdown component is deprecated as of v18, use Select component instead."),this.id=this.id||Mt("pn_id_"),this.autoUpdateModel(),this.filterBy&&(this.filterOptions={filter:e=>this.onFilterInputChange(e),reset:()=>this.resetFilter()})}ngAfterViewChecked(){if(this.optionsChanged&&this.overlayVisible&&(this.optionsChanged=!1,this.zone.runOutsideAngular(()=>{setTimeout(()=>{this.overlayViewChild&&this.overlayViewChild.alignOverlay()},1)})),this.selectedOptionUpdated&&this.itemsWrapper){let e=Me(this.overlayViewChild?.overlayViewChild?.nativeElement,"li.p-highlight");e&&ki(this.itemsWrapper,e),this.selectedOptionUpdated=!1}}templates;ngAfterContentInit(){this.templates.forEach(e=>{switch(e.getType()){case"item":this.itemTemplate=e.template;break;case"selectedItem":this.selectedItemTemplate=e.template;break;case"header":this.headerTemplate=e.template;break;case"filter":this.filterTemplate=e.template;break;case"footer":this.footerTemplate=e.template;break;case"emptyfilter":this.emptyFilterTemplate=e.template;break;case"empty":this.emptyTemplate=e.template;break;case"group":this.groupTemplate=e.template;break;case"loader":this.loaderTemplate=e.template;break;case"dropdownicon":this.dropdownIconTemplate=e.template;break;case"loadingicon":this.loadingIconTemplate=e.template;break;case"clearicon":this.clearIconTemplate=e.template;break;case"filtericon":this.filterIconTemplate=e.template;break;default:this.itemTemplate=e.template;break}})}flatOptions(e){return(e||[]).reduce((o,r,n)=>{o.push({optionGroup:r,group:!0,index:n});let a=this.getOptionGroupChildren(r);return a&&a.forEach(c=>o.push(c)),o},[])}autoUpdateModel(){if(this.selectOnFocus&&this.autoOptionFocus&&!this.hasSelectedOption()&&(this.focusedOptionIndex.set(this.findFirstFocusedOptionIndex()),this.onOptionSelect(null,this.visibleOptions()[this.focusedOptionIndex()],!1)),this.autoDisplayFirst&&(this.modelValue()===null||this.modelValue()===void 0)&&!this.placeholder()){let e=this.findFirstOptionIndex();this.onOptionSelect(null,this.visibleOptions()[e],!1,!0)}}onOptionSelect(e,o,r=!0,n=!1){if(!this.isSelected(o)){let a=this.getOptionValue(o);this.updateModel(a,e),this.focusedOptionIndex.set(this.findSelectedOptionIndex()),n===!1&&this.onChange.emit({originalEvent:e,value:a})}r&&this.hide(!0)}onOptionMouseEnter(e,o){this.focusOnHover&&this.changeFocusedOptionIndex(e,o)}updateModel(e,o){this.value=e,this.onModelChange(e),this.modelValue.set(e),this.selectedOptionUpdated=!0}writeValue(e){this.filter&&this.resetFilter(),this.value=e,this.allowModelChange()&&this.onModelChange(e),this.modelValue.set(this.value),this.updateEditableLabel(),this.cd.markForCheck()}allowModelChange(){return this.autoDisplayFirst&&!this.placeholder()&&(this.modelValue()===void 0||this.modelValue()===null)&&!this.editable&&this.options&&this.options.length}isSelected(e){return this.isValidOption(e)&&this.isOptionValueEqualsModelValue(e)}isOptionValueEqualsModelValue(e){return Ci(this.modelValue(),this.getOptionValue(e),this.equalityKey())}ngAfterViewInit(){super.ngAfterViewInit(),this.editable&&this.updateEditableLabel(),this.updatePlaceHolderForFloatingLabel()}updatePlaceHolderForFloatingLabel(){let e=this.el.nativeElement.parentElement,o=e?.classList.contains("p-float-label");if(e&&o&&!this.selectedOption){let r=e.querySelector("label");r&&this._placeholder.set(r.textContent)}}updateEditableLabel(){this.editableInputViewChild&&(this.editableInputViewChild.nativeElement.value=this.getOptionLabel(this.selectedOption)||this.modelValue()||"")}clearEditableLabel(){this.editableInputViewChild&&(this.editableInputViewChild.nativeElement.value="")}getOptionIndex(e,o){return this.virtualScrollerDisabled?e:o&&o.getItemOptions(e).index}getOptionLabel(e){return this.optionLabel!==void 0&&this.optionLabel!==null?Tt(e,this.optionLabel):e&&e.label!==void 0?e.label:e}getOptionValue(e){return this.optionValue&&this.optionValue!==null?Tt(e,this.optionValue):!this.optionLabel&&e&&e.value!==void 0?e.value:e}isOptionDisabled(e){return this.getOptionValue(this.modelValue())===this.getOptionValue(e)||this.getOptionLabel(this.modelValue()===this.getOptionLabel(e))&&e.disabled===!1?!1:this.optionDisabled?Tt(e,this.optionDisabled):e&&e.disabled!==void 0?e.disabled:!1}getOptionGroupLabel(e){return this.optionGroupLabel!==void 0&&this.optionGroupLabel!==null?Tt(e,this.optionGroupLabel):e&&e.label!==void 0?e.label:e}getOptionGroupChildren(e){return this.optionGroupChildren!==void 0&&this.optionGroupChildren!==null?Tt(e,this.optionGroupChildren):e.items}getAriaPosInset(e){return(this.optionGroupLabel?e-this.visibleOptions().slice(0,e).filter(o=>this.isOptionGroup(o)).length:e)+1}get ariaSetSize(){return this.visibleOptions().filter(e=>!this.isOptionGroup(e)).length}resetFilter(){this._filterValue.set(null),this.filterViewChild&&this.filterViewChild.nativeElement&&(this.filterViewChild.nativeElement.value="")}registerOnChange(e){this.onModelChange=e}registerOnTouched(e){this.onModelTouched=e}setDisabledState(e){this.disabled=e,this.cd.markForCheck()}onContainerClick(e){this.disabled||this.readonly||this.loading||(this.focusInputViewChild?.nativeElement.focus({preventScroll:!0}),!(e.target.tagName==="INPUT"||e.target.getAttribute("data-pc-section")==="clearicon"||e.target.closest('[data-pc-section="clearicon"]'))&&((!this.overlayViewChild||!this.overlayViewChild.el.nativeElement.contains(e.target))&&(this.overlayVisible?this.hide(!0):this.show(!0)),this.onClick.emit(e),this.clicked.set(!0),this.cd.detectChanges()))}isEmpty(){return!this._options()||this.visibleOptions()&&this.visibleOptions().length===0}onEditableInput(e){let o=e.target.value;this.searchValue="",!this.searchOptions(e,o)&&this.focusedOptionIndex.set(-1),this.onModelChange(o),this.updateModel(o,e),setTimeout(()=>{this.onChange.emit({originalEvent:e,value:o})},1),!this.overlayVisible&&De(o)&&this.show()}show(e){this.overlayVisible=!0;let o=this.focusedOptionIndex()!==-1?this.focusedOptionIndex():this.autoOptionFocus?this.findFirstFocusedOptionIndex():this.editable?-1:this.findSelectedOptionIndex();this.focusedOptionIndex.set(o),e&&te(this.focusInputViewChild?.nativeElement),this.cd.markForCheck()}onOverlayAnimationStart(e){if(e.toState==="visible"){if(this.itemsWrapper=Me(this.overlayViewChild?.overlayViewChild?.nativeElement,this.virtualScroll?".p-scroller":".p-dropdown-items-wrapper"),this.virtualScroll&&this.scroller?.setContentEl(this.itemsViewChild?.nativeElement),this.options&&this.options.length)if(this.virtualScroll){let o=this.modelValue()?this.focusedOptionIndex():-1;o!==-1&&this.scroller?.scrollToIndex(o)}else{let o=Me(this.itemsWrapper,".p-dropdown-item.p-highlight");o&&o.scrollIntoView({block:"nearest",inline:"nearest"})}this.filterViewChild&&this.filterViewChild.nativeElement&&(this.preventModelTouched=!0,this.autofocusFilter&&!this.editable&&this.filterViewChild.nativeElement.focus()),this.onShow.emit(e)}e.toState==="void"&&(this.itemsWrapper=null,this.onModelTouched(),this.onHide.emit(e))}hide(e){this.overlayVisible=!1,this.focusedOptionIndex.set(-1),this.clicked.set(!1),this.searchValue="",this.overlayOptions?.mode==="modal"&&lo(),this.filter&&this.resetFilterOnHide&&this.resetFilter(),e&&(this.focusInputViewChild&&te(this.focusInputViewChild?.nativeElement),this.editable&&this.editableInputViewChild&&te(this.editableInputViewChild?.nativeElement)),this.cd.markForCheck()}onInputFocus(e){if(this.disabled)return;this.focused=!0;let o=this.focusedOptionIndex()!==-1?this.focusedOptionIndex():this.overlayVisible&&this.autoOptionFocus?this.findFirstFocusedOptionIndex():-1;this.focusedOptionIndex.set(o),this.overlayVisible&&this.scrollInView(this.focusedOptionIndex()),this.onFocus.emit(e)}onInputBlur(e){this.focused=!1,this.onBlur.emit(e),this.preventModelTouched||this.onModelTouched(),this.preventModelTouched=!1}onKeyDown(e,o){if(!(this.disabled||this.readonly||this.loading)){switch(e.code){case"ArrowDown":this.onArrowDownKey(e);break;case"ArrowUp":this.onArrowUpKey(e,this.editable);break;case"ArrowLeft":case"ArrowRight":this.onArrowLeftKey(e,this.editable);break;case"Delete":this.onDeleteKey(e);break;case"Home":this.onHomeKey(e,this.editable);break;case"End":this.onEndKey(e,this.editable);break;case"PageDown":this.onPageDownKey(e);break;case"PageUp":this.onPageUpKey(e);break;case"Space":this.onSpaceKey(e,o);break;case"Enter":case"NumpadEnter":this.onEnterKey(e);break;case"Escape":this.onEscapeKey(e);break;case"Tab":this.onTabKey(e);break;case"Backspace":this.onBackspaceKey(e,this.editable);break;case"ShiftLeft":case"ShiftRight":break;default:!e.metaKey&&co(e.key)&&(!this.overlayVisible&&this.show(),!this.editable&&this.searchOptions(e,e.key));break}this.clicked.set(!1)}}onFilterKeyDown(e){switch(e.code){case"ArrowDown":this.onArrowDownKey(e);break;case"ArrowUp":this.onArrowUpKey(e,!0);break;case"ArrowLeft":case"ArrowRight":this.onArrowLeftKey(e,!0);break;case"Home":this.onHomeKey(e,!0);break;case"End":this.onEndKey(e,!0);break;case"Enter":case"NumpadEnter":this.onEnterKey(e,!0);break;case"Escape":this.onEscapeKey(e);break;case"Tab":this.onTabKey(e,!0);break;default:break}}onFilterBlur(e){this.focusedOptionIndex.set(-1)}onArrowDownKey(e){if(!this.overlayVisible)this.show(),this.editable&&this.changeFocusedOptionIndex(e,this.findSelectedOptionIndex());else{let o=this.focusedOptionIndex()!==-1?this.findNextOptionIndex(this.focusedOptionIndex()):this.clicked()?this.findFirstOptionIndex():this.findFirstFocusedOptionIndex();this.changeFocusedOptionIndex(e,o)}e.preventDefault(),e.stopPropagation()}changeFocusedOptionIndex(e,o){if(this.focusedOptionIndex()!==o&&(this.focusedOptionIndex.set(o),this.scrollInView(),this.selectOnFocus)){let r=this.visibleOptions()[o];this.onOptionSelect(e,r,!1)}}get virtualScrollerDisabled(){return!this.virtualScroll}scrollInView(e=-1){let o=e!==-1?`${this.id}_${e}`:this.focusedOptionId;if(this.itemsViewChild&&this.itemsViewChild.nativeElement){let r=Me(this.itemsViewChild.nativeElement,`li[id="${o}"]`);r?r.scrollIntoView&&r.scrollIntoView({block:"nearest",inline:"nearest"}):this.virtualScrollerDisabled||setTimeout(()=>{this.virtualScroll&&this.scroller?.scrollToIndex(e!==-1?e:this.focusedOptionIndex())},0)}}hasSelectedOption(){return this.modelValue()!==void 0}isValidSelectedOption(e){return this.isValidOption(e)&&this.isSelected(e)}equalityKey(){return this.optionValue?null:this.dataKey}findFirstFocusedOptionIndex(){let e=this.findSelectedOptionIndex();return e<0?this.findFirstOptionIndex():e}findFirstOptionIndex(){return this.visibleOptions().findIndex(e=>this.isValidOption(e))}findSelectedOptionIndex(){return this.hasSelectedOption()?this.visibleOptions().findIndex(e=>this.isValidSelectedOption(e)):-1}findNextOptionIndex(e){let o=e<this.visibleOptions().length-1?this.visibleOptions().slice(e+1).findIndex(r=>this.isValidOption(r)):-1;return o>-1?o+e+1:e}findPrevOptionIndex(e){let o=e>0?Et(this.visibleOptions().slice(0,e),r=>this.isValidOption(r)):-1;return o>-1?o:e}findLastOptionIndex(){return Et(this.visibleOptions(),e=>this.isValidOption(e))}findLastFocusedOptionIndex(){let e=this.findSelectedOptionIndex();return e<0?this.findLastOptionIndex():e}isValidOption(e){return e!=null&&!(this.isOptionDisabled(e)||this.isOptionGroup(e))}isOptionGroup(e){return this.optionGroupLabel!==void 0&&this.optionGroupLabel!==null&&e.optionGroup!==void 0&&e.optionGroup!==null&&e.group}onArrowUpKey(e,o=!1){if(e.altKey&&!o){if(this.focusedOptionIndex()!==-1){let r=this.visibleOptions()[this.focusedOptionIndex()];this.onOptionSelect(e,r)}this.overlayVisible&&this.hide()}else{let r=this.focusedOptionIndex()!==-1?this.findPrevOptionIndex(this.focusedOptionIndex()):this.clicked()?this.findLastOptionIndex():this.findLastFocusedOptionIndex();this.changeFocusedOptionIndex(e,r),!this.overlayVisible&&this.show()}e.preventDefault(),e.stopPropagation()}onArrowLeftKey(e,o=!1){o&&this.focusedOptionIndex.set(-1)}onDeleteKey(e){this.showClear&&(this.clear(e),e.preventDefault())}onHomeKey(e,o=!1){if(o){let r=e.currentTarget;e.shiftKey?r.setSelectionRange(0,r.value.length):(r.setSelectionRange(0,0),this.focusedOptionIndex.set(-1))}else this.changeFocusedOptionIndex(e,this.findFirstOptionIndex()),!this.overlayVisible&&this.show();e.preventDefault()}onEndKey(e,o=!1){if(o){let r=e.currentTarget;if(e.shiftKey)r.setSelectionRange(0,r.value.length);else{let n=r.value.length;r.setSelectionRange(n,n),this.focusedOptionIndex.set(-1)}}else this.changeFocusedOptionIndex(e,this.findLastOptionIndex()),!this.overlayVisible&&this.show();e.preventDefault()}onPageDownKey(e){this.scrollInView(this.visibleOptions().length-1),e.preventDefault()}onPageUpKey(e){this.scrollInView(0),e.preventDefault()}onSpaceKey(e,o=!1){!this.editable&&!o&&this.onEnterKey(e)}onEnterKey(e,o=!1){if(!this.overlayVisible)this.focusedOptionIndex.set(-1),this.onArrowDownKey(e);else{if(this.focusedOptionIndex()!==-1){let r=this.visibleOptions()[this.focusedOptionIndex()];this.onOptionSelect(e,r)}!o&&this.hide()}e.preventDefault()}onEscapeKey(e){this.overlayVisible&&this.hide(!0),e.preventDefault()}onTabKey(e,o=!1){if(!o)if(this.overlayVisible&&this.hasFocusableElements())te(e.shiftKey?this.lastHiddenFocusableElementOnOverlay.nativeElement:this.firstHiddenFocusableElementOnOverlay.nativeElement),e.preventDefault();else{if(this.focusedOptionIndex()!==-1&&this.overlayVisible){let r=this.visibleOptions()[this.focusedOptionIndex()];this.onOptionSelect(e,r)}this.overlayVisible&&this.hide(this.filter)}e.stopPropagation()}onFirstHiddenFocus(e){let o=e.relatedTarget===this.focusInputViewChild?.nativeElement?_i(this.overlayViewChild.el?.nativeElement,":not(.p-hidden-focusable)"):this.focusInputViewChild?.nativeElement;te(o)}onLastHiddenFocus(e){let o=e.relatedTarget===this.focusInputViewChild?.nativeElement?vi(this.overlayViewChild?.overlayViewChild?.nativeElement,':not([data-p-hidden-focusable="true"])'):this.focusInputViewChild?.nativeElement;te(o)}hasFocusableElements(){return bi(this.overlayViewChild.overlayViewChild.nativeElement,':not([data-p-hidden-focusable="true"])').length>0}onBackspaceKey(e,o=!1){o&&!this.overlayVisible&&this.show()}searchFields(){return this.filterBy?.split(",")||this.filterFields||[this.optionLabel]}searchOptions(e,o){this.searchValue=(this.searchValue||"")+o;let r=-1,n=!1;return r=this.visibleOptions().findIndex(a=>this.isOptionExactMatched(a)),r===-1&&(r=this.visibleOptions().findIndex(a=>this.isOptionStartsWith(a))),r!==-1&&(n=!0),r===-1&&this.focusedOptionIndex()===-1&&(r=this.findFirstFocusedOptionIndex()),r!==-1&&this.changeFocusedOptionIndex(e,r),this.searchTimeout&&clearTimeout(this.searchTimeout),this.searchTimeout=setTimeout(()=>{this.searchValue="",this.searchTimeout=null},500),n}isOptionStartsWith(e){return this.isValidOption(e)&&this.getOptionLabel(e).toString().toLocaleLowerCase(this.filterLocale).startsWith(this.searchValue.toLocaleLowerCase(this.filterLocale))}isOptionExactMatched(e){return this.isValidOption(e)&&this.getOptionLabel(e).toString().toLocaleLowerCase(this.filterLocale)===this.searchValue.toLocaleLowerCase(this.filterLocale)}onFilterInputChange(e){let o=e.target.value;this._filterValue.set(o),this.focusedOptionIndex.set(-1),this.onFilter.emit({originalEvent:e,filter:this._filterValue()}),!this.virtualScrollerDisabled&&this.scroller.scrollToIndex(0),setTimeout(()=>{this.overlayViewChild.alignOverlay()}),this.cd.markForCheck()}applyFocus(){this.editable?Me(this.el.nativeElement,".p-dropdown-label.p-inputtext").focus():te(this.focusInputViewChild?.nativeElement)}focus(){this.applyFocus()}clear(e){this.updateModel(null,e),this.clearEditableLabel(),this.onModelTouched(),this.onChange.emit({originalEvent:e,value:this.value}),this.onClear.emit(e),this.resetFilter()}static \u0275fac=function(o){return new(o||t)(pe($e),pe(xi))};static \u0275cmp=F({type:t,selectors:[["p-dropdown"]],contentQueries:function(o,r,n){if(o&1&&Z(n,Ve,4),o&2){let a;I(a=S())&&(r.templates=a)}},viewQuery:function(o,r){if(o&1&&(H(Cc,5),H(xc,5),H(Ic,5),H(Sc,5),H(Tc,5),H(Ec,5),H(Mc,5),H(Oc,5),H(Bc,5)),o&2){let n;I(n=S())&&(r.containerViewChild=n.first),I(n=S())&&(r.filterViewChild=n.first),I(n=S())&&(r.focusInputViewChild=n.first),I(n=S())&&(r.editableInputViewChild=n.first),I(n=S())&&(r.itemsViewChild=n.first),I(n=S())&&(r.scroller=n.first),I(n=S())&&(r.overlayViewChild=n.first),I(n=S())&&(r.firstHiddenFocusableElementOnOverlay=n.first),I(n=S())&&(r.lastHiddenFocusableElementOnOverlay=n.first)}},hostVars:5,hostBindings:function(o,r){o&1&&v("click",function(a){return r.onContainerClick(a)}),o&2&&(k("id",r.id),Ne(r.hostStyle),W(r.hostClass))},inputs:{id:"id",scrollHeight:"scrollHeight",filter:[2,"filter","filter",T],name:"name",style:"style",panelStyle:"panelStyle",styleClass:"styleClass",panelStyleClass:"panelStyleClass",readonly:[2,"readonly","readonly",T],required:[2,"required","required",T],editable:[2,"editable","editable",T],appendTo:"appendTo",tabindex:[2,"tabindex","tabindex",G],placeholder:"placeholder",loadingIcon:"loadingIcon",filterPlaceholder:"filterPlaceholder",filterLocale:"filterLocale",variant:"variant",inputId:"inputId",dataKey:"dataKey",filterBy:"filterBy",filterFields:"filterFields",autofocus:[2,"autofocus","autofocus",T],resetFilterOnHide:[2,"resetFilterOnHide","resetFilterOnHide",T],checkmark:[2,"checkmark","checkmark",T],dropdownIcon:"dropdownIcon",loading:[2,"loading","loading",T],optionLabel:"optionLabel",optionValue:"optionValue",optionDisabled:"optionDisabled",optionGroupLabel:"optionGroupLabel",optionGroupChildren:"optionGroupChildren",autoDisplayFirst:[2,"autoDisplayFirst","autoDisplayFirst",T],group:[2,"group","group",T],showClear:[2,"showClear","showClear",T],emptyFilterMessage:"emptyFilterMessage",emptyMessage:"emptyMessage",lazy:[2,"lazy","lazy",T],virtualScroll:[2,"virtualScroll","virtualScroll",T],virtualScrollItemSize:[2,"virtualScrollItemSize","virtualScrollItemSize",G],virtualScrollOptions:"virtualScrollOptions",overlayOptions:"overlayOptions",ariaFilterLabel:"ariaFilterLabel",ariaLabel:"ariaLabel",ariaLabelledBy:"ariaLabelledBy",filterMatchMode:"filterMatchMode",maxlength:[2,"maxlength","maxlength",G],tooltip:"tooltip",tooltipPosition:"tooltipPosition",tooltipPositionStyle:"tooltipPositionStyle",tooltipStyleClass:"tooltipStyleClass",focusOnHover:[2,"focusOnHover","focusOnHover",T],selectOnFocus:[2,"selectOnFocus","selectOnFocus",T],autoOptionFocus:[2,"autoOptionFocus","autoOptionFocus",T],autofocusFilter:[2,"autofocusFilter","autofocusFilter",T],fluid:[2,"fluid","fluid",T],disabled:"disabled",itemSize:"itemSize",autoZIndex:"autoZIndex",baseZIndex:"baseZIndex",showTransitionOptions:"showTransitionOptions",hideTransitionOptions:"hideTransitionOptions",filterValue:"filterValue",options:"options"},outputs:{onChange:"onChange",onFilter:"onFilter",onFocus:"onFocus",onBlur:"onBlur",onClick:"onClick",onShow:"onShow",onHide:"onHide",onClear:"onClear",onLazyLoad:"onLazyLoad"},standalone:!1,features:[ve([zd,ms]),ae],decls:11,vars:15,consts:[["elseBlock",""],["overlay",""],["content",""],["focusInput",""],["defaultPlaceholder",""],["editableInput",""],["firstHiddenFocusableEl",""],["buildInItems",""],["lastHiddenFocusableEl",""],["builtInFilterElement",""],["filter",""],["scroller",""],["loader",""],["items",""],["emptyFilter",""],["role","combobox",3,"ngClass","pTooltip","tooltipPosition","positionStyle","tooltipStyleClass","pAutoFocus","focus","blur","keydown",4,"ngIf"],["type","text","aria-haspopup","listbox",3,"ngClass","disabled","pAutoFocus","input","keydown","focus","blur",4,"ngIf"],[4,"ngIf"],["role","button","aria-label","dropdown trigger","aria-haspopup","listbox",1,"p-select-dropdown"],[4,"ngIf","ngIfElse"],[3,"visibleChange","onAnimationStart","onHide","visible","options","target","appendTo","autoZIndex","baseZIndex","showTransitionOptions","hideTransitionOptions"],["role","combobox",3,"focus","blur","keydown","ngClass","pTooltip","tooltipPosition","positionStyle","tooltipStyleClass","pAutoFocus"],[3,"ngTemplateOutlet","ngTemplateOutletContext",4,"ngIf"],[3,"ngTemplateOutlet","ngTemplateOutletContext"],["type","text","aria-haspopup","listbox",3,"input","keydown","focus","blur","ngClass","disabled","pAutoFocus"],["class","p-select-clear-icon",3,"click",4,"ngIf"],[1,"p-select-clear-icon",3,"click"],[4,"ngTemplateOutlet","ngTemplateOutletContext"],[4,"ngTemplateOutlet"],["aria-hidden","true",3,"ngClass",4,"ngIf"],["aria-hidden","true",3,"class",4,"ngIf"],["aria-hidden","true",3,"ngClass"],["aria-hidden","true"],["class","p-select-dropdown-icon",4,"ngIf"],["class","p-select-dropdown-icon",3,"ngClass",4,"ngIf"],[3,"styleClass",4,"ngIf"],[1,"p-select-dropdown-icon",3,"ngClass"],[3,"styleClass"],[1,"p-select-dropdown-icon"],[3,"ngClass","ngStyle"],["role","presentation",1,"p-hidden-accessible","p-hidden-focusable",3,"focus"],["class","p-select-header",3,"click",4,"ngIf"],[1,"p-select-list-container"],[3,"items","style","itemSize","autoSize","lazy","options","onLazyLoad",4,"ngIf"],[1,"p-select-header",3,"click"],["pInputText","","type","text","role","searchbox","autocomplete","off",1,"p-select-filter",3,"input","keydown","blur","value","variant"],[3,"onLazyLoad","items","itemSize","autoSize","lazy","options"],["role","listbox",1,"p-select-list",3,"ngClass"],["ngFor","",3,"ngForOf"],["class","p-select-empty-message","role","option",3,"ngStyle",4,"ngIf"],["role","option",1,"p-select-option-group",3,"ngStyle"],[3,"onClick","onMouseEnter","id","option","checkmark","selected","label","disabled","template","focused","ariaPosInset","ariaSetSize"],["role","option",1,"p-select-empty-message",3,"ngStyle"]],template:function(o,r){if(o&1){let n=E();f(0,Hc,6,20,"span",15)(1,zc,2,8,"input",16)(2,Kc,3,2,"ng-container",17),u(3,"div",18),f(4,Yc,3,2,"ng-container",19)(5,id,2,2,"ng-template",null,0,de),p(),u(7,"p-overlay",20,1),qr("visibleChange",function(c){return h(n),Qr(r.overlayVisible,c)||(r.overlayVisible=c),g(c)}),v("onAnimationStart",function(c){return h(n),g(r.onOverlayAnimationStart(c))})("onHide",function(){return h(n),g(r.hide())}),f(9,Ad,13,17,"ng-template",null,2,de),p()}if(o&2){let n,a=X(6);s("ngIf",!r.editable),d(),s("ngIf",r.editable),d(),s("ngIf",r.isVisibleClearIcon),d(),k("aria-expanded",(n=r.overlayVisible)!==null&&n!==void 0?n:!1)("data-pc-section","trigger"),d(),s("ngIf",r.loading)("ngIfElse",a),d(3),Wr("visible",r.overlayVisible),s("options",r.overlayOptions)("target","@parent")("appendTo",r.appendTo)("autoZIndex",r.autoZIndex)("baseZIndex",r.baseZIndex)("showTransitionOptions",r.showTransitionOptions)("hideTransitionOptions",r.hideTransitionOptions)}},dependencies:()=>[oe,Ct,fe,Ee,le,Pi,it,er,mo,Go,qo,Zo,Fi,Yo,Xo,$d],encapsulation:2,changeDetection:0})}return t})(),_s=(()=>{class t{static \u0275fac=function(o){return new(o||t)};static \u0275mod=ne({type:t});static \u0275inj=ie({imports:[R,Jo,z,Ze,er,Vi,Go,qo,Zo,Wo,Qo,Li,Yo,Xo,Jo,z]})}return t})();var Ud=["pMenuItemContent",""],ys=t=>({"p-disabled":t}),Bo=t=>({$implicit:t}),jd=()=>({exact:!1});function Kd(t,i){t&1&&U(0)}function Wd(t,i){if(t&1&&(u(0,"a",6),f(1,Kd,1,0,"ng-container",7),p()),t&2){let e=l(2),o=X(4);s("target",e.item.target)("ngClass",O(9,ys,e.item.disabled)),k("title",e.item.title)("href",e.item.url||null,st)("data-automationid",e.item.automationId)("tabindex",-1)("data-pc-section","action"),d(),s("ngTemplateOutlet",o)("ngTemplateOutletContext",O(11,Bo,e.item))}}function Qd(t,i){t&1&&U(0)}function qd(t,i){if(t&1&&(u(0,"a",8),f(1,Qd,1,0,"ng-container",7),p()),t&2){let e=l(2),o=X(4);s("routerLink",e.item.routerLink)("queryParams",e.item.queryParams)("routerLinkActiveOptions",e.item.routerLinkActiveOptions||We(17,jd))("target",e.item.target)("ngClass",O(18,ys,e.item.disabled))("fragment",e.item.fragment)("queryParamsHandling",e.item.queryParamsHandling)("preserveFragment",e.item.preserveFragment)("skipLocationChange",e.item.skipLocationChange)("replaceUrl",e.item.replaceUrl)("state",e.item.state),k("data-automationid",e.item.automationId)("tabindex",-1)("data-pc-section","action")("title",e.item.title),d(),s("ngTemplateOutlet",o)("ngTemplateOutletContext",O(20,Bo,e.item))}}function Zd(t,i){if(t&1&&(Q(0),f(1,Wd,2,13,"a",4)(2,qd,2,22,"a",5),q()),t&2){let e=l();d(),s("ngIf",!(e.item!=null&&e.item.routerLink)),d(),s("ngIf",e.item==null?null:e.item.routerLink)}}function Gd(t,i){}function Yd(t,i){t&1&&f(0,Gd,0,0,"ng-template")}function Xd(t,i){if(t&1&&(Q(0),f(1,Yd,1,0,null,7),q()),t&2){let e=l();d(),s("ngTemplateOutlet",e.itemTemplate)("ngTemplateOutletContext",O(2,Bo,e.item))}}function Jd(t,i){if(t&1&&w(0,"span",12),t&2){let e=l(2);W(e.item.iconClass),s("ngClass",e.item.icon)("ngStyle",e.item.iconStyle)}}function eu(t,i){if(t&1&&(u(0,"span",13),A(1),p()),t&2){let e=l(2);d(),_e(e.item.label)}}function tu(t,i){if(t&1&&(w(0,"span",14),Le(1,"safeHtml")),t&2){let e=l(2);s("innerHTML",Pe(1,1,e.item.label),vt)}}function ou(t,i){if(t&1&&w(0,"p-badge",15),t&2){let e=l(2);s("styleClass",e.item.badgeStyleClass)("value",e.item.badge)}}function ru(t,i){if(t&1&&f(0,Jd,1,4,"span",9)(1,eu,2,1,"span",10)(2,tu,2,3,"ng-template",null,1,de)(4,ou,1,2,"p-badge",11),t&2){let e=X(3),o=l();s("ngIf",o.item.icon),d(),s("ngIf",o.item.escape!==!1)("ngIfElse",e),d(3),s("ngIf",o.item.badge)}}var iu=["start"],nu=["end"],au=["header"],su=["item"],lu=["submenuheader"],cu=["list"],du=["container"],uu=t=>({"p-menu p-component":!0,"p-menu-overlay":t}),pu=(t,i)=>({showTransitionParams:t,hideTransitionParams:i}),mu=t=>({value:"visible",params:t}),fu=(t,i)=>({"p-hidden":t,flex:i}),ks=(t,i)=>({"p-focus":t,"p-disabled":i});function hu(t,i){t&1&&U(0)}function gu(t,i){if(t&1&&(u(0,"div",9),f(1,hu,1,0,"ng-container",10),p()),t&2){let e,o=l(2);k("data-pc-section","start"),d(),s("ngTemplateOutlet",(e=o.startTemplate)!==null&&e!==void 0?e:o._startTemplate)}}function bu(t,i){t&1&&w(0,"li",14)}function _u(t,i){if(t&1&&(u(0,"span"),A(1),p()),t&2){let e=l(3).$implicit;d(),_e(e.label)}}function vu(t,i){if(t&1&&(w(0,"span",18),Le(1,"safeHtml")),t&2){let e=l(3).$implicit;s("innerHTML",Pe(1,1,e.label),vt)}}function yu(t,i){if(t&1&&(Q(0),f(1,_u,2,1,"span",17)(2,vu,2,3,"ng-template",null,2,de),q()),t&2){let e=X(3),o=l(2).$implicit;d(),s("ngIf",o.escape!==!1)("ngIfElse",e)}}function ku(t,i){t&1&&U(0)}function wu(t,i){if(t&1&&(u(0,"li",15),f(1,yu,4,2,"ng-container",7)(2,ku,1,0,"ng-container",16),p()),t&2){let e,o=l(),r=o.$implicit,n=o.index,a=l(3);s("ngClass",J(7,fu,r.visible===!1,r.visible))("tooltipOptions",r.tooltipOptions),k("data-automationid",r.automationId)("id",a.menuitemId(r,a.id,n)),d(),s("ngIf",!a.submenuHeaderTemplate&&!a._submenuHeaderTemplate),d(),s("ngTemplateOutlet",(e=a.submenuHeaderTemplate)!==null&&e!==void 0?e:a._submenuHeaderTemplate)("ngTemplateOutletContext",O(10,Bo,r))}}function Cu(t,i){t&1&&w(0,"li",14)}function xu(t,i){if(t&1){let e=E();u(0,"li",20),v("onMenuItemClick",function(r){h(e);let n=l(),a=n.$implicit,c=n.index,m=l().index,_=l(3);return g(_.itemClick(r,_.menuitemId(a,_.id,m,c)))}),p()}if(t&2){let e,o=l(),r=o.$implicit,n=o.index,a=l().index,c=l(3);W(r.styleClass),s("pMenuItemContent",r)("itemTemplate",(e=c.itemTemplate)!==null&&e!==void 0?e:c._itemTemplate)("ngClass",J(13,ks,c.focusedOptionId()&&c.menuitemId(r,c.id,a,n)===c.focusedOptionId(),c.disabled(r.disabled)))("ngStyle",r.style)("tooltipOptions",r.tooltipOptions),k("data-pc-section","menuitem")("aria-label",c.label(r.label))("data-p-focused",c.isItemFocused(c.menuitemId(r,c.id,a,n)))("data-p-disabled",c.disabled(r.disabled))("aria-disabled",c.disabled(r.disabled))("id",c.menuitemId(r,c.id,a,n))}}function Iu(t,i){if(t&1&&f(0,Cu,1,0,"li",12)(1,xu,1,16,"li",19),t&2){let e=i.$implicit,o=l().$implicit;s("ngIf",e.separator&&(e.visible!==!1||o.visible!==!1)),d(),s("ngIf",!e.separator&&e.visible!==!1&&(e.visible!==void 0||o.visible!==!1))}}function Su(t,i){if(t&1&&f(0,bu,1,0,"li",12)(1,wu,3,12,"li",13)(2,Iu,2,2,"ng-template",11),t&2){let e=i.$implicit;s("ngIf",e.separator&&e.visible!==!1),d(),s("ngIf",!e.separator),d(),s("ngForOf",e.items)}}function Tu(t,i){if(t&1&&f(0,Su,3,3,"ng-template",11),t&2){let e=l(2);s("ngForOf",e.model)}}function Eu(t,i){t&1&&w(0,"li",14)}function Mu(t,i){if(t&1){let e=E();u(0,"li",20),v("onMenuItemClick",function(r){h(e);let n=l(),a=n.$implicit,c=n.index,m=l(3);return g(m.itemClick(r,m.menuitemId(a,m.id,c)))}),p()}if(t&2){let e,o=l(),r=o.$implicit,n=o.index,a=l(3);W(r.styleClass),s("pMenuItemContent",r)("itemTemplate",(e=a.itemTemplate)!==null&&e!==void 0?e:a._itemTemplate)("ngClass",J(13,ks,a.focusedOptionId()&&a.menuitemId(r,a.id,n)===a.focusedOptionId(),a.disabled(r.disabled)))("ngStyle",r.style)("tooltipOptions",r.tooltipOptions),k("data-pc-section","menuitem")("aria-label",a.label(r.label))("data-p-focused",a.isItemFocused(a.menuitemId(r,a.id,n)))("data-p-disabled",a.disabled(r.disabled))("aria-disabled",a.disabled(r.disabled))("id",a.menuitemId(r,a.id,n))}}function Ou(t,i){if(t&1&&f(0,Eu,1,0,"li",12)(1,Mu,1,16,"li",19),t&2){let e=i.$implicit;s("ngIf",e.separator&&e.visible!==!1),d(),s("ngIf",!e.separator&&e.visible!==!1)}}function Bu(t,i){if(t&1&&f(0,Ou,2,2,"ng-template",11),t&2){let e=l(2);s("ngForOf",e.model)}}function Du(t,i){t&1&&U(0)}function Vu(t,i){if(t&1&&(u(0,"div",21),f(1,Du,1,0,"ng-container",10),p()),t&2){let e,o=l(2);k("data-pc-section","end"),d(),s("ngTemplateOutlet",(e=o.endTemplate)!==null&&e!==void 0?e:o._endTemplate)}}function Fu(t,i){if(t&1){let e=E();u(0,"div",4,0),v("click",function(r){h(e);let n=l();return g(n.onOverlayClick(r))})("@overlayAnimation.start",function(r){h(e);let n=l();return g(n.onOverlayAnimationStart(r))})("@overlayAnimation.done",function(r){h(e);let n=l();return g(n.onOverlayAnimationEnd(r))}),f(2,gu,2,2,"div",5),u(3,"ul",6,1),v("focus",function(r){h(e);let n=l();return g(n.onListFocus(r))})("blur",function(r){h(e);let n=l();return g(n.onListBlur(r))})("keydown",function(r){h(e);let n=l();return g(n.onListKeyDown(r))}),f(5,Tu,1,1,null,7)(6,Bu,1,1,null,7),p(),f(7,Vu,2,2,"div",8),p()}if(t&2){let e,o,r=l();W(r.styleClass),s("ngClass",O(18,uu,r.popup))("ngStyle",r.style)("@overlayAnimation",O(23,mu,J(20,pu,r.showTransitionOptions,r.hideTransitionOptions)))("@.disabled",r.popup!==!0),k("data-pc-name","menu")("id",r.id),d(2),s("ngIf",(e=r.startTemplate)!==null&&e!==void 0?e:r._startTemplate),d(),k("id",r.id+"_list")("tabindex",r.getTabIndexValue())("data-pc-section","menu")("aria-activedescendant",r.activedescendant())("aria-label",r.ariaLabel)("aria-labelledBy",r.ariaLabelledBy),d(2),s("ngIf",r.hasSubMenu()),d(),s("ngIf",!r.hasSubMenu()),d(),s("ngIf",(o=r.endTemplate)!==null&&o!==void 0?o:r._endTemplate)}}var Lu=({dt:t})=>`
.p-menu {
    background: ${t("menu.background")};
    color: ${t("menu.color")};
    border: 1px solid ${t("menu.border.color")};
    border-radius: ${t("menu.border.radius")};
    min-width: 12.5rem;
}

.p-menu-list {
    margin: 0;
    padding: ${t("menu.list.padding")};
    outline: 0 none;
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: ${t("menu.list.gap")};
}

.p-menu-item-content {
    transition: background ${t("menu.transition.duration")}, color ${t("menu.transition.duration")};
    border-radius: ${t("menu.item.border.radius")};
    color: ${t("menu.item.color")};
}

.p-menu-item-link {
    cursor: pointer;
    display: flex;
    align-items: center;
    text-decoration: none;
    overflow: hidden;
    position: relative;
    color: inherit;
    padding: ${t("menu.item.padding")};
    gap: ${t("menu.item.gap")};
    user-select: none;
    outline: 0 none;
}

.p-menu-item-label {
    line-height: 1;
}

.p-menu-item-icon {
    color: ${t("menu.item.icon.color")};
}

.p-menu-item.p-focus .p-menu-item-content {
    color: ${t("menu.item.focus.color")};
    background: ${t("menu.item.focus.background")};
}

.p-menu-item.p-focus .p-menu-item-icon {
    color: ${t("menu.item.icon.focus.color")};
}

.p-menu-item:not(.p-disabled) .p-menu-item-content:hover {
    color: ${t("menu.item.focus.color")};
    background: ${t("menu.item.focus.background")};
}

.p-menu-item:not(.p-disabled) .p-menu-item-content:hover .p-menu-item-icon {
    color: ${t("menu.item.icon.focus.color")};
}

.p-menu-overlay {
    box-shadow: ${t("menu.shadow")};
}

.p-menu-submenu-label {
    background: ${t("menu.submenu.label.background")};
    padding: ${t("menu.submenu.label.padding")};
    color: ${t("menu.submenu.label.color")};
    font-weight: ${t("menu.submenu.label.font.weight")};
}

.p-menu-separator {
    border-top: 1px solid ${t("menu.separator.border.color")};
}

/* For PrimeNG */
.p-menu-overlay {
    position: absolute;
}
`,Pu={root:({props:t})=>["p-menu p-component",{"p-menu-overlay":t.popup}],start:"p-menu-start",list:"p-menu-list",submenuLabel:"p-menu-submenu-label",separator:"p-menu-separator",end:"p-menu-end",item:({instance:t})=>["p-menu-item",{"p-focus":t.id===t.focusedOptionId,"p-disabled":t.disabled()}],itemContent:"p-menu-item-content",itemLink:"p-menu-item-link",itemIcon:"p-menu-item-icon",itemLabel:"p-menu-item-label"},vs=(()=>{class t extends Ce{name="menu";theme=Lu;classes=Pu;static \u0275fac=(()=>{let e;return function(r){return(e||(e=j(t)))(r||t)}})();static \u0275prov=B({token:t,factory:t.\u0275fac})}return t})();var ws=(()=>{class t{platformId;sanitizer;constructor(e,o){this.platformId=e,this.sanitizer=o}transform(e){return!e||!he(this.platformId)?e:this.sanitizer.bypassSecurityTrustHtml(e)}static \u0275fac=function(o){return new(o||t)(pe(oo,16),pe(ai,16))};static \u0275pipe=jr({name:"safeHtml",type:t,pure:!0})}return t})(),Au=(()=>{class t{item;itemTemplate;onMenuItemClick=new V;menu;constructor(e){this.menu=e}onItemClick(e,o){this.onMenuItemClick.emit({originalEvent:e,item:o})}static \u0275fac=function(o){return new(o||t)(pe(ht(()=>At)))};static \u0275cmp=F({type:t,selectors:[["","pMenuItemContent",""]],inputs:{item:[0,"pMenuItemContent","item"],itemTemplate:"itemTemplate"},outputs:{onMenuItemClick:"onMenuItemClick"},attrs:Ud,decls:5,vars:3,consts:[["itemContent",""],["htmlLabel",""],[1,"p-menu-item-content",3,"click"],[4,"ngIf"],["class","p-menu-item-link","pRipple","",3,"target","ngClass",4,"ngIf"],["routerLinkActive","p-menu-item-link-active","class","p-menu-item-link","pRipple","",3,"routerLink","queryParams","routerLinkActiveOptions","target","ngClass","fragment","queryParamsHandling","preserveFragment","skipLocationChange","replaceUrl","state",4,"ngIf"],["pRipple","",1,"p-menu-item-link",3,"target","ngClass"],[4,"ngTemplateOutlet","ngTemplateOutletContext"],["routerLinkActive","p-menu-item-link-active","pRipple","",1,"p-menu-item-link",3,"routerLink","queryParams","routerLinkActiveOptions","target","ngClass","fragment","queryParamsHandling","preserveFragment","skipLocationChange","replaceUrl","state"],["class","p-menu-item-icon",3,"ngClass","class","ngStyle",4,"ngIf"],["class","p-menu-item-label",4,"ngIf","ngIfElse"],[3,"styleClass","value",4,"ngIf"],[1,"p-menu-item-icon",3,"ngClass","ngStyle"],[1,"p-menu-item-label"],[1,"p-menu-item-label",3,"innerHTML"],[3,"styleClass","value"]],template:function(o,r){if(o&1){let n=E();u(0,"div",2),v("click",function(c){return h(n),g(r.onItemClick(c,r.item))}),f(1,Zd,3,2,"ng-container",3)(2,Xd,2,4,"ng-container",3)(3,ru,5,4,"ng-template",null,0,de),p()}o&2&&(k("data-pc-section","content"),d(),s("ngIf",!r.itemTemplate),d(),s("ngIf",r.itemTemplate))},dependencies:[R,oe,fe,Ee,le,St,ao,so,Ot,Ze,Bt,fo,z,ws],encapsulation:2})}return t})(),At=(()=>{class t extends ue{overlayService;model;popup;style;styleClass;appendTo;autoZIndex=!0;baseZIndex=0;showTransitionOptions=".12s cubic-bezier(0, 0, 0.2, 1)";hideTransitionOptions=".1s linear";ariaLabel;ariaLabelledBy;id;tabindex=0;onShow=new V;onHide=new V;onBlur=new V;onFocus=new V;listViewChild;containerViewChild;container;scrollHandler;documentClickListener;documentResizeListener;preventDocumentDefault;target;visible;focusedOptionId=ee(()=>this.focusedOptionIndex()!==-1?this.focusedOptionIndex():null);focusedOptionIndex=D(-1);selectedOptionIndex=D(-1);focused=!1;overlayVisible=!1;relativeAlign;_componentStyle=y(vs);constructor(e){super(),this.overlayService=e,this.id=this.id||Mt("pn_id_")}toggle(e){this.visible?this.hide():this.show(e),this.preventDocumentDefault=!0}show(e){this.target=e.currentTarget,this.relativeAlign=e.relativeAlign,this.visible=!0,this.preventDocumentDefault=!0,this.overlayVisible=!0,this.cd.markForCheck()}ngOnInit(){super.ngOnInit(),this.popup||this.bindDocumentClickListener()}startTemplate;_startTemplate;endTemplate;_endTemplate;headerTemplate;_headerTemplate;itemTemplate;_itemTemplate;submenuHeaderTemplate;_submenuHeaderTemplate;templates;ngAfterContentInit(){this.templates?.forEach(e=>{switch(e.getType()){case"start":this._startTemplate=e.template;break;case"end":this._endTemplate=e.template;break;case"item":this._itemTemplate=e.template;break;case"submenuheader":this._submenuHeaderTemplate=e.template;break;default:this._itemTemplate=e.template;break}})}getTabIndexValue(){return this.tabindex!==void 0?this.tabindex.toString():null}onOverlayAnimationStart(e){switch(e.toState){case"visible":this.popup&&(this.container=e.element,this.moveOnTop(),this.onShow.emit({}),this.appendOverlay(),this.alignOverlay(),this.bindDocumentClickListener(),this.bindDocumentResizeListener(),this.bindScrollListener(),te(this.listViewChild.nativeElement));break;case"void":this.onOverlayHide(),this.onHide.emit({});break}}onOverlayAnimationEnd(e){switch(e.toState){case"void":this.autoZIndex&&qe.clear(e.element);break}}alignOverlay(){this.relativeAlign?hi(this.container,this.target):fi(this.container,this.target)}appendOverlay(){this.appendTo&&(this.appendTo==="body"?this.renderer.appendChild(this.document.body,this.container):gi(this.appendTo,this.container))}restoreOverlayAppend(){this.container&&this.appendTo&&this.renderer.appendChild(this.el.nativeElement,this.container)}moveOnTop(){this.autoZIndex&&qe.set("menu",this.container,this.baseZIndex+this.config.zIndex.menu)}hide(){this.visible=!1,this.relativeAlign=!1,this.cd.markForCheck()}onWindowResize(){this.visible&&!Nt()&&this.hide()}menuitemId(e,o,r,n){return e?.id??`${o}_${r}${n!==void 0?"_"+n:""}`}isItemFocused(e){return this.focusedOptionId()===e}label(e){return typeof e=="function"?e():e}disabled(e){return typeof e=="function"?e():typeof e>"u"?!1:e}activedescendant(){return this.focused?this.focusedOptionId():void 0}onListFocus(e){this.focused||(this.focused=!0,this.onFocus.emit(e))}onListBlur(e){this.focused&&(this.focused=!1,this.changeFocusedOptionIndex(-1),this.selectedOptionIndex.set(-1),this.focusedOptionIndex.set(-1),this.onBlur.emit(e))}onListKeyDown(e){switch(e.code){case"ArrowDown":this.onArrowDownKey(e);break;case"ArrowUp":this.onArrowUpKey(e);break;case"Home":this.onHomeKey(e);break;case"End":this.onEndKey(e);break;case"Enter":this.onEnterKey(e);break;case"NumpadEnter":this.onEnterKey(e);break;case"Space":this.onSpaceKey(e);break;case"Escape":case"Tab":this.popup&&(te(this.target),this.hide()),this.overlayVisible&&this.hide();break;default:break}}onArrowDownKey(e){let o=this.findNextOptionIndex(this.focusedOptionIndex());this.changeFocusedOptionIndex(o),e.preventDefault()}onArrowUpKey(e){if(e.altKey&&this.popup)te(this.target),this.hide(),e.preventDefault();else{let o=this.findPrevOptionIndex(this.focusedOptionIndex());this.changeFocusedOptionIndex(o),e.preventDefault()}}onHomeKey(e){this.changeFocusedOptionIndex(0),e.preventDefault()}onEndKey(e){this.changeFocusedOptionIndex($t(this.containerViewChild.nativeElement,'li[data-pc-section="menuitem"][data-p-disabled="false"]').length-1),e.preventDefault()}onEnterKey(e){let o=Me(this.containerViewChild.nativeElement,`li[id="${`${this.focusedOptionIndex()}`}"]`),r=o&&Me(o,'a[data-pc-section="action"]');this.popup&&te(this.target),r?r.click():o&&o.click(),e.preventDefault()}onSpaceKey(e){this.onEnterKey(e)}findNextOptionIndex(e){let r=[...$t(this.containerViewChild.nativeElement,'li[data-pc-section="menuitem"][data-p-disabled="false"]')].findIndex(n=>n.id===e);return r>-1?r+1:0}findPrevOptionIndex(e){let r=[...$t(this.containerViewChild.nativeElement,'li[data-pc-section="menuitem"][data-p-disabled="false"]')].findIndex(n=>n.id===e);return r>-1?r-1:0}changeFocusedOptionIndex(e){let o=$t(this.containerViewChild.nativeElement,'li[data-pc-section="menuitem"][data-p-disabled="false"]');if(o.length>0){let r=e>=o.length?o.length-1:e<0?0:e;r>-1&&this.focusedOptionIndex.set(o[r].getAttribute("id"))}}itemClick(e,o){let{originalEvent:r,item:n}=e;if(this.focused||(this.focused=!0,this.onFocus.emit()),n.disabled){r.preventDefault();return}!n.url&&!n.routerLink&&r.preventDefault(),n.command&&n.command({originalEvent:r,item:n}),this.popup&&this.hide(),!this.popup&&this.focusedOptionIndex()!==o&&this.focusedOptionIndex.set(o)}onOverlayClick(e){this.popup&&this.overlayService.add({originalEvent:e,target:this.el.nativeElement}),this.preventDocumentDefault=!0}bindDocumentClickListener(){if(!this.documentClickListener&&he(this.platformId)){let e=this.el?this.el.nativeElement.ownerDocument:"document";this.documentClickListener=this.renderer.listen(e,"click",o=>{let r=this.containerViewChild?.nativeElement&&!this.containerViewChild?.nativeElement.contains(o.target),n=!(this.target&&(this.target===o.target||this.target.contains(o.target)));!this.popup&&r&&n&&this.onListBlur(o),this.preventDocumentDefault&&this.overlayVisible&&r&&n&&(this.hide(),this.preventDocumentDefault=!1)})}}unbindDocumentClickListener(){this.documentClickListener&&(this.documentClickListener(),this.documentClickListener=null)}bindDocumentResizeListener(){if(!this.documentResizeListener&&he(this.platformId)){let e=this.document.defaultView;this.documentResizeListener=this.renderer.listen(e,"resize",this.onWindowResize.bind(this))}}unbindDocumentResizeListener(){this.documentResizeListener&&(this.documentResizeListener(),this.documentResizeListener=null)}bindScrollListener(){!this.scrollHandler&&he(this.platformId)&&(this.scrollHandler=new Di(this.target,()=>{this.visible&&this.hide()})),this.scrollHandler?.bindScrollListener()}unbindScrollListener(){this.scrollHandler&&(this.scrollHandler.unbindScrollListener(),this.scrollHandler=null)}onOverlayHide(){this.unbindDocumentClickListener(),this.unbindDocumentResizeListener(),this.unbindScrollListener(),this.preventDocumentDefault=!1,this.cd.destroyed||(this.target=null)}ngOnDestroy(){this.popup&&(this.scrollHandler&&(this.scrollHandler.destroy(),this.scrollHandler=null),this.container&&this.autoZIndex&&qe.clear(this.container),this.restoreOverlayAppend(),this.onOverlayHide()),this.popup||this.unbindDocumentClickListener(),super.ngOnDestroy()}hasSubMenu(){return this.model?.some(e=>e.items)??!1}isItemHidden(e){return e.separator?e.visible===!1||e.items&&e.items.some(o=>o.visible!==!1):e.visible===!1}static \u0275fac=function(o){return new(o||t)(pe(Ii))};static \u0275cmp=F({type:t,selectors:[["p-menu"]],contentQueries:function(o,r,n){if(o&1&&(Z(n,iu,4),Z(n,nu,4),Z(n,au,4),Z(n,su,4),Z(n,lu,4),Z(n,Ve,4)),o&2){let a;I(a=S())&&(r.startTemplate=a.first),I(a=S())&&(r.endTemplate=a.first),I(a=S())&&(r.headerTemplate=a.first),I(a=S())&&(r.itemTemplate=a.first),I(a=S())&&(r.submenuHeaderTemplate=a.first),I(a=S())&&(r.templates=a)}},viewQuery:function(o,r){if(o&1&&(H(cu,5),H(du,5)),o&2){let n;I(n=S())&&(r.listViewChild=n.first),I(n=S())&&(r.containerViewChild=n.first)}},inputs:{model:"model",popup:[2,"popup","popup",T],style:"style",styleClass:"styleClass",appendTo:"appendTo",autoZIndex:[2,"autoZIndex","autoZIndex",T],baseZIndex:[2,"baseZIndex","baseZIndex",G],showTransitionOptions:"showTransitionOptions",hideTransitionOptions:"hideTransitionOptions",ariaLabel:"ariaLabel",ariaLabelledBy:"ariaLabelledBy",id:"id",tabindex:[2,"tabindex","tabindex",G]},outputs:{onShow:"onShow",onHide:"onHide",onBlur:"onBlur",onFocus:"onFocus"},features:[ve([vs]),ae],decls:1,vars:1,consts:[["container",""],["list",""],["htmlSubmenuLabel",""],[3,"ngClass","class","ngStyle","click",4,"ngIf"],[3,"click","ngClass","ngStyle"],["class","p-menu-start",4,"ngIf"],["role","menu",1,"p-menu-list","p-reset",3,"focus","blur","keydown"],[4,"ngIf"],["class","p-menu-end",4,"ngIf"],[1,"p-menu-start"],[4,"ngTemplateOutlet"],["ngFor","",3,"ngForOf"],["class","p-menu-separator","role","separator",4,"ngIf"],["class","p-menu-submenu-label","pTooltip","","role","none",3,"ngClass","tooltipOptions",4,"ngIf"],["role","separator",1,"p-menu-separator"],["pTooltip","","role","none",1,"p-menu-submenu-label",3,"ngClass","tooltipOptions"],[4,"ngTemplateOutlet","ngTemplateOutletContext"],[4,"ngIf","ngIfElse"],[3,"innerHTML"],["class","p-menu-item","pTooltip","","role","menuitem",3,"pMenuItemContent","itemTemplate","ngClass","ngStyle","class","tooltipOptions","onMenuItemClick",4,"ngIf"],["pTooltip","","role","menuitem",1,"p-menu-item",3,"onMenuItemClick","pMenuItemContent","itemTemplate","ngClass","ngStyle","tooltipOptions"],[1,"p-menu-end"]],template:function(o,r){o&1&&f(0,Fu,8,25,"div",3),o&2&&s("ngIf",!r.popup||r.visible)},dependencies:[R,oe,Ct,fe,Ee,le,St,Au,Ze,it,Bt,z,ws],encapsulation:2,data:{animation:[zt("overlayAnimation",[we(":enter",[ke({opacity:0,transform:"scaleY(0.8)"}),ye("{{showTransitionParams}}")]),we(":leave",[ye("{{hideTransitionParams}}",ke({opacity:0}))])])]},changeDetection:0})}return t})(),Rt=(()=>{class t{static \u0275fac=function(o){return new(o||t)};static \u0275mod=ne({type:t});static \u0275inj=ie({imports:[At,z,z]})}return t})();var at=class t{constructor(){this.primeng=y(Si);this.themes=[{mode:"default",label:"\u9ED8\u8BA4\u4E3B\u9898",color:"#FFF5EB",className:"app-default",preset:Kt},{mode:"green",label:"\u7EFF\u8272\u4E3B\u9898",color:"#C9E0CB",className:"app-green",preset:qa},{mode:"dark",label:"\u6697\u9ED1\u4E3B\u9898",color:"#333333",className:"app-dark",preset:Kt}];this.theme=D(this.getStoredMode()||"default");this.currentThemeInfo=ee(()=>this.themes.find(i=>i.mode===this.theme())||this.themes[0]);this.currentFontSize=D(this.#e());this.setTheme(this.theme()),rt(()=>{document.documentElement.style.fontSize=`${this.currentFontSize()}px`})}setTheme(i){let e=this.themes.find(o=>o.mode===i);e&&(this.themes.forEach(o=>{o.className&&document.documentElement.classList.remove(o.className)}),e.className&&document.documentElement.classList.add(e.className),e.preset&&this.primeng.theme.set({preset:e.preset,options:{darkModeSelector:".app-dark"}}),this.theme.set(i),localStorage.setItem("themeMode",i))}changeFontSize(i){let e=parseFloat(getComputedStyle(document.documentElement).fontSize),o=i==="+"?e*1.1:e/1.1;this.currentFontSize.set(o),localStorage.setItem("fontSize",o.toString())}getStoredMode(){let i=localStorage.getItem("themeMode");return this.themes.some(e=>e.mode===i)?i:null}#e(){let i=localStorage.getItem("fontSize");if(!i)return null;let e=parseFloat(i);return isNaN(e)?null:e}static{this.\u0275fac=function(e){return new(e||t)}}static{this.\u0275prov=B({token:t,factory:t.\u0275fac,providedIn:"root"})}};function Hu(t,i){if(t&1){let e=E();u(0,"div",4),v("click",function(){h(e);let r=l();return g(r.navigateToHome())}),w(1,"img",5),p()}}function zu(t,i){if(t&1){let e=E();u(0,"div",6)(1,"div",7),w(2,"i",8)(3,"p-menu",9,0),u(5,"p-button",10),v("click",function(r){h(e);let n=X(4);return g(n.toggle(r))}),p()(),u(6,"p-button",11),v("onClick",function(){h(e);let r=l();return g(r.drawerService.openSettings())}),p(),u(7,"p-button",12),v("onClick",function(){h(e);let r=l();return g(r.drawerService.openPlayer())}),p()()}if(t&2){let e=l();d(3),s("model",e.displayLanguages())("popup",!0),d(2),s("label",e.i18nService.currentLanguageInfo().label)("text",!0),d(),s("text",!0)("rounded",!0)("pTooltip",e.i18nService.translate("common.settings")),d(),s("text",!0)("rounded",!0)("pTooltip",e.i18nService.translate("common.play"))}}function $u(t,i){t&1&&w(0,"i",21)}function Nu(t,i){if(t&1){let e=E();u(0,"p",19),v("click",function(){let r=h(e).$implicit,n=l(2);return g(n.handleMenuItem(r))})("mouseenter",function(){let r=h(e).$implicit,n=l(2);return g(n.toggleSubMenu(r))}),u(1,"a",20),A(2),f(3,$u,1,0,"i",21),p()()}if(t&2){let e=i.$implicit;d(2),me(" ",e.label," "),d(),Be(e.items&&e.items.length>0?3:-1)}}function Uu(t,i){if(t&1){let e=E();u(0,"p",27),v("click",function(){let r=h(e).$implicit;return g(r.command==null?null:r.command({}))}),A(1),p()}if(t&2){let e=i.$implicit;d(),me(" ",e.label," ")}}function ju(t,i){if(t&1){let e=E();u(0,"div",24)(1,"p",25),v("click",function(){let r=h(e).$implicit;return g(r.command==null?null:r.command({}))}),A(2),p(),je(3,Uu,2,1,"p",26,Ue),p()}if(t&2){let e=i.$implicit;d(2),me(" ",e.label," "),d(),Ke(e.items)}}function Ku(t,i){if(t&1){let e=E();u(0,"div",22),v("mouseleave",function(){h(e);let r=l(2);return g(r.activeItem.set(null))}),u(1,"div",23),je(2,ju,5,1,"div",24,Ue),p()()}if(t&2){let e=l(2);d(2),Ke(e.activeItem().items)}}function Wu(t,i){if(t&1){let e=E();u(0,"div",13)(1,"div",14)(2,"div",4),v("click",function(){h(e);let r=l();return g(r.navigateToHome())}),w(3,"img",5),p()(),u(4,"div",15),je(5,Nu,4,2,"p",16,Ue),p(),u(7,"div",17)(8,"div",6)(9,"div",7),w(10,"i",8)(11,"p-menu",9,0),u(13,"p-button",10),v("click",function(r){h(e);let n=X(12);return g(n.toggle(r))}),p()(),u(14,"p-button",11),v("onClick",function(){h(e);let r=l();return g(r.drawerService.openSettings())}),p(),u(15,"p-button",12),v("onClick",function(){h(e);let r=l();return g(r.drawerService.openPlayer())}),p()()()(),f(16,Ku,4,0,"div",18)}if(t&2){let e=l();d(5),Ke(e.menuItems),d(6),s("model",e.displayLanguages())("popup",!0),d(2),s("label",e.i18nService.currentLanguageInfo().label)("text",!0),d(),s("text",!0)("rounded",!0)("pTooltip",e.i18nService.translate("settings")),d(),s("text",!0)("rounded",!0)("pTooltip",e.i18nService.translate("play")),d(),Be(e.activeItem()&&e.activeItem().items&&e.activeItem().items.length>0?16:-1)}}var Do=class t{constructor(){this.i18nService=y(Ge);this.drawerService=y(nt);this.themeService=y(at);this.channelIdContentCodeService=y(sn);this.subs=new xr;this.loadingService=y(yo);this.mobileService=y(rn);this.menuItems=[];this.activeItem=D(null);this.displayLanguages=ee(()=>this.i18nService.supportedLanguages.map(i=>({label:i.label,command:()=>this.selectDisplayLanguage(i)})));this.#e=y(an);this.#t=y(Ji);this.router=y(dt);this.elementRef=y(_t)}#e;#t;ngOnInit(){let i=localStorage.getItem("menuItems")?JSON.parse(localStorage.getItem("menuItems")||"[]"):[];this.menuItems=i;let e=this.i18nService.language$.subscribe(o=>{this.loadChannelTree()});this.subs.add(e)}loadChannelTree(){this.loadingService.show(),this.#e.getChannelTree(this.i18nService.language()).subscribe({next:i=>{let e=this.initializeMenuItems(i);e.push({label:this.i18nService.translate("search"),command:()=>this.router.navigateByUrl("/search")}),this.menuItems=e,localStorage.setItem("menuItems",JSON.stringify(this.menuItems||[])),this.loadingService.hide()},error:i=>{console.error("\u83B7\u53D6\u9891\u9053\u6811\u6570\u636E\u5931\u8D25:",i),this.loadingService.hide()}})}initializeMenuItems(i){return i.map(e=>(this.channelIdContentCodeService.setChannelIdContentCode(e.id,e.contentCode||""),{label:this.i18nService.translate(e.name||""),items:this.initializeMenuItems(e.children),command:e.children.length?void 0:this.navigateToChannel.bind(this,e)}))}navigateToChannel(i){switch(i.channelSource){case 3:this.activeItem.set(null),this.router.navigateByUrl(`/virtual-folder/list/${i.id}`);break;case 2:this.activeItem.set(null),this.router.navigateByUrl(`/ebooks/ebook-card/${i.id}`);break;case 4:this.activeItem.set(null),this.router.navigateByUrl(`/podcast/album-card/${i.id}`);break;case 1:this.activeItem.set(null),this.navigateToCollection(i);break}}navigateToCollection(i){this.#t.getFirstByChannelId(i.id).subscribe({next:e=>{if(e.id)switch(e.listStyle){case 0:this.router.navigateByUrl(`/home/<USER>/${e.id}`);break;case 1:this.router.navigateByUrl(`/home/<USER>/${e.id}`);break;case 8:this.router.navigateByUrl(`/home/<USER>/${e.id}`);break;case 9:this.router.navigateByUrl(`/home/<USER>/${e.id}`);break;case 10:this.router.navigateByUrl(`/home/<USER>/${e.id}`);break}}})}navigateToHome(){this.router.navigate(["/landing"])}selectDisplayLanguage(i){this.i18nService.setLanguage(i.code)}toggleSubMenu(i){this.activeItem.set(i)}onClick(i){let e=i.target;this.elementRef.nativeElement.contains(e)||this.activeItem.set(null)}ngOnDestroy(){this.subs.unsubscribe()}handleMenuItem(i){i.items&&i.items.length>0?this.toggleSubMenu(i):i.command&&i.command({})}static{this.\u0275fac=function(e){return new(e||t)}}static{this.\u0275cmp=F({type:t,selectors:[["app-navigation-menu"]],hostBindings:function(e,o){e&1&&v("click",function(n){return o.onClick(n)},!1,ro)},decls:6,vars:8,consts:[["menu2",""],[3,"model"],["pTemplate","start"],["pTemplate","end"],[1,"flex","align-items-center","cursor-pointer",3,"click"],["src","assets/images/logo.svg","alt","","srcset","",1,"w-10"],[1,"control-area"],[1,"language-selector"],["tooltipPosition","bottom",1,"pi","pi-language"],["appendTo","body",3,"model","popup"],["size","small",1,"language-button",3,"click","label","text"],["icon","pi pi-cog","severity","secondary","tooltipPosition","bottom",3,"onClick","text","rounded","pTooltip"],["icon","pi pi-play-circle","severity","primary","tooltipPosition","bottom",3,"onClick","text","rounded","pTooltip"],[1,"flex","gap-2","relative","bg-[#fff]","py-2","custom-menu","p-menubar"],[1,"ml-2"],[1,"flex-1","flex"],[1,"p-menubar-item-content","custom-menu-item"],[1,"mr-2"],[1,"absolute","w-screen","custom-submenu","p-2","shadow-lg","z-10","rounded-b-2xl"],[1,"p-menubar-item-content","custom-menu-item",3,"click","mouseenter"],[1,"p-menubar-item-link"],[1,"pi","pi-angle-down"],[1,"absolute","w-screen","custom-submenu","p-2","shadow-lg","z-10","rounded-b-2xl",3,"mouseleave"],[1,"flex","gap-2"],[1,"flex-1","flex","flex-col"],[1,"font-bold","px-2","hover:bg-gray-100","p-2","rounded-lg","cursor-pointer","custom-menu-item",3,"click"],[1,"px-2","hover:bg-gray-100","p-2","rounded-lg","cursor-pointer","custom-menu-item"],[1,"px-2","hover:bg-gray-100","p-2","rounded-lg","cursor-pointer","custom-menu-item",3,"click"]],template:function(e,o){e&1&&(u(0,"p-menubar",1),Le(1,"async"),f(2,Hu,2,0,"ng-template",2)(3,zu,8,10,"ng-template",3),p(),f(4,Wu,17,11),Le(5,"async")),e&2&&(Y("display",Pe(1,4,o.mobileService.isMobile)?"block":"none"),s("model",o.menuItems),d(4),Be(Pe(5,6,o.mobileService.isMobile)?-1:4))},dependencies:[R,xt,Bi,ps,br,Ve,Dt,ho,_s,Ze,it,Rt,At],styles:["[_nghost-%COMP%]{height:56px;position:sticky;top:0;z-index:50}.language-icon[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;height:1.5rem;line-height:1;font-size:.875rem}.language-button[_ngcontent-%COMP%]     .p-button{padding:.25rem .5rem;font-size:.875rem}.language-selector[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.25rem;height:2rem}.control-area[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.75rem;height:2.5rem}.custom-menu-item[_ngcontent-%COMP%]:hover{color:var(--p-tree-node-selected-color);background-color:var(--p-menubar-item-focus-background)}.custom-submenu[_ngcontent-%COMP%]{background-color:var(--p-menubar-submenu-background)}"]})}};var Qu=t=>({"!text-white":t});function qu(t,i){if(t&1&&(u(0,"li",2),A(1),p()),t&2){let e=i.$implicit,o=i.$index,r=l();s("ngClass",O(2,Qu,r.activeIndex()===o)),d(),me(" ",e.text," ")}}var Vo=class t{constructor(){this.currentTime=D(0);this.activeIndex=ee(()=>this.findIndex(this.currentTime()));this.offset=ee(()=>this.setOffset(this.currentTime()));this.lrcData=[];this.elementRef=y(_t)}ngOnInit(){this.parseLrc(`
[00:12.39]\u4F60\u7684\u201C\u5FC3\u610F\u81EA\u6211\u201D\uFF0C\u4EC0\u4E48\u90FD\u505A\u4E0D\u4E86
[00:18.19]\u5B9E\u76F8\u4E2D\uFF0C\u4F60\u4EC0\u4E48\u90FD\u6CA1\u6709\u505A
[00:23.90]\u73B0\u5B9E\u4E2D\uFF0C\u505A\u4E0E\u6240\u505A\u7684\u201C\u4F60\u201D
[00:27.46]\u53EA\u662F\u6050\u60E7\u7684\u5FC3
[00:30.33]\u5728\u5BFB\u6C42\u5B89\u5B81\u7684\u5C45\u6240
[00:34.00]
[00:35.27]\u5C06\u4F60\u7684\u8EAB\u5FC3\u610F\u8BC6\uFF0C\u4EA4\u4ED8\u4E8E\u5723\u4E3B
[00:40.97]\u4F53\u9A8C\u8EAB\u5FC3\u5185\u5728\uFF0C \u6DF1\u9083\u7684\u5E73\u5B89\u4E0E\u6E29\u6696
[00:47.22]\u4F60\u771F\u5B9E\u7684\u8EAB\u4EFD\uFF0C\u53EA\u662F\u7EAF\u51C0\u7684\u201C\u7075\u201D
[00:52.38]\u4F60\u539F\u672C\u7684\u5FC3\u6001\uFF0C\u5C31\u662F\u65E0\u9650\u7684\u201C\u7231\u201D
[01:00.00]
[01:00.74]\u8BA9\u73B0\u5B9E\u7684\u81EA\u6211\uFF0C\u6C89\u6D78\u5728\u7231\u4E4B\u4E2D
[01:06.35]\u5F53\u65E0\u9650\u6E29\u6696\u7684\u5B89\u5B81\u4E4B\u7231\uFF0C \u7184\u706D\u4E86\u81EA\u6211
[01:12.75]\u5E73\u5B89\u4E4B\u4E2D\uFF0C \u5C06\u82CF\u9192\u201C\u4F60\u201D\u7684\u6C38\u6052
[01:22.00]
[01:53.97]\u4F60\u7684\u201C\u5FC3\u610F\u81EA\u6211\u201D,\u4EC0\u4E48\u90FD\u505A\u4E0D\u4E86
[01:59.89]\u5B9E\u76F8\u4E2D\uFF0C\u4F60\u4EC0\u4E48\u90FD\u6CA1\u6709\u505A
[02:05.62]\u73B0\u5B9E\u4E2D\uFF0C\u505A\u4E0E\u6240\u505A\u7684\u201C\u4F60\u201D
[02:09.88]\u53EA\u662F\u6050\u60E7\u7684\u5FC3\uFF0C\u5728\u5BFB\u6C42\u5B89\u5B81\u7684\u5C45\u6240
[02:16.00]
[02:16.92]\u5C06\u4F60\u7684\u8EAB\u5FC3\u610F\u8BC6\uFF0C\u4EA4\u4ED8\u4E8E\u5723\u4E3B
[02:22.49]\u4F53\u9A8C\u8EAB\u5FC3\u5185\u5728\uFF0C\u6DF1\u9083\u7684\u5E73\u5B89\u4E0E\u6E29\u6696
[02:28.60]\u4F60\u771F\u5B9E\u7684\u8EAB\u4EFD\uFF0C\u53EA\u662F\u7EAF\u51C0\u7684\u201C\u7075\u201D
[02:34.38]\u4F60\u539F\u672C\u7684\u5FC3\u6001\uFF0C\u5C31\u662F\u65E0\u9650\u7684\u201C\u7231\u201D
[02:41.00]
[02:42.22]\u8BA9\u73B0\u5B9E\u7684\u81EA\u6211\uFF0C\u6C89\u6D78\u5728\u7231\u4E4B\u4E2D
[02:47.89]\u5F53\u65E0\u9650\u6E29\u6696\u7684\u5B89\u5B81\u4E4B\u7231\uFF0C \u7184\u706D\u4E86\u81EA\u6211
[02:54.32]\u5E73\u5B89\u4E4B\u4E2D\uFF0C \u5C06\u82CF\u9192\u201C\u4F60\u201D\u7684\u6C38\u6052
[03:03.32]
    `)}parseLrc(i){let e=i.split(`
`),o=[];for(let r=0;r<e.length;r++){let n=e[r].trim();if(!n)continue;let a=n.match(/\[(\d{2}):(\d{2}\.\d{2})\]/),c=n.replace(/\[\d{2}:\d{2}\.\d{2}\]/g,"").trim();if(c===""||!a)continue;let m=parseInt(a[1],10),_=parseFloat(a[2]),b={time:m*60+_,text:c};o.push(b)}this.lrcData=o}findIndex(i){for(let e=0;e<this.lrcData.length;e++)if(i<this.lrcData[e].time)return e-1;return this.lrcData.length-1}getContainerHeight(){return this.elementRef.nativeElement.offsetHeight}getLiHeight(){let i=this.elementRef.nativeElement.querySelector("li");return i?i.offsetHeight:0}setOffset(i){let e=this.getContainerHeight(),o=this.getLiHeight(),r=this.findIndex(i),n=o*r+o/2-e/2;return n<0&&(n=0),n}static{this.\u0275fac=function(e){return new(e||t)}}static{this.\u0275cmp=F({type:t,selectors:[["app-lyric-scroll"]],inputs:{currentTime:"currentTime"},decls:4,vars:2,consts:[[1,"h-[6rem]","overflow-y-hidden"],[1,"w-full","flex","flex-col","transition-transform","duration-300","ease-in-out"],[1,"flex","justify-center","text-white/50","py-1",3,"ngClass"]],template:function(e,o){e&1&&(u(0,"div",0)(1,"ul",1),je(2,qu,2,4,"li",2,Ue),p()()),e&2&&(d(),Y("transform","translateY(-"+o.offset()+"px)"),d(),Ke(o.lrcData))},dependencies:[R,oe],encapsulation:2})}};var Zu=["drawerContainer"],Gu=["*",[["","slot","footer"]]],Yu=["*","[slot=footer]"];function Xu(t,i){if(t&1){let e=E();u(0,"div",5),v("click",function(){h(e);let r=l();return g(r.onOverlayClick())}),p()}if(t&2){let e=l();io("show",e.showOverlay()),s("@fadeInOut",e.showOverlay()?"in":"out")}}function Ju(t,i){t&1&&(u(0,"div",6),lt(1,1),p())}var Fo=class t{constructor(){this.visible=D(!1);this.position="right";this.size="medium";this.modal=!0;this.dismissible=!0;this.closeOnEscape=!0;this.blockScroll=!0;this.appendTo=null;this.width="";this.height="";this.styleClass="";this.contentPadding="1rem";this.showHeader=!0;this.showCloseIcon=!0;this.title="";this.hasHeaderContent=!1;this.hasFooterContent=!1;this.onShow=new V;this.onHide=new V;this.onAnimationStart=new V;this.onAnimationDoneEmit=new V;this.showOverlay=D(!1);this.isAnimating=D(!1);this.animationState=ee(()=>{let i=this.position,e=this.visible();return`${i}-${e?"in":"out"}`});this.drawerClasses=ee(()=>{let i=["drawer",`drawer-${this.position}`,`drawer-${this.size}`];return this.styleClass&&i.push(this.styleClass),this.modal&&i.push("drawer-modal"),this.dismissible&&i.push("drawer-dismissible"),i.join(" ")});this.drawerStyles=ee(()=>{let i={};return this.width&&(i.width=this.width),this.height&&(i.height=this.height),i});rt(()=>{this.visible()?this.show():this.hide()})}ngOnInit(){this.checkContentProjection()}ngOnDestroy(){this.restoreBodyScroll()}checkContentProjection(){this.hasHeaderContent=!1,this.hasFooterContent=!1}show(){this.isAnimating()||(this.onShow.emit(),this.showOverlay.set(!0),this.blockScroll&&this.preventBodyScroll(),this.appendTo&&this.appendTo.appendChild(this.drawerContainer?.nativeElement))}hide(){this.isAnimating()||(this.onHide.emit(),this.showOverlay.set(!1),this.restoreBodyScroll())}close(){this.visible.set(!1)}onOverlayClick(){this.dismissible&&this.close()}onAnimationDone(i){this.isAnimating.set(!1),this.onAnimationDoneEmit.emit(i)}onEscapeKey(i){this.closeOnEscape&&this.visible()&&this.dismissible&&this.close()}preventBodyScroll(){document.body.style.overflow="hidden"}restoreBodyScroll(){document.body.style.overflow=""}static{this.\u0275fac=function(e){return new(e||t)}}static{this.\u0275cmp=F({type:t,selectors:[["app-drawer"]],viewQuery:function(e,o){if(e&1&&H(Zu,5),e&2){let r;I(r=S())&&(o.drawerContainer=r.first)}},hostBindings:function(e,o){e&1&&v("keydown.escape",function(n){return o.onEscapeKey(n)},!1,ro)},inputs:{visible:"visible",position:"position",size:"size",modal:"modal",dismissible:"dismissible",closeOnEscape:"closeOnEscape",blockScroll:"blockScroll",appendTo:"appendTo",width:"width",height:"height",styleClass:"styleClass",contentPadding:"contentPadding",showHeader:"showHeader",showCloseIcon:"showCloseIcon",title:"title"},outputs:{onShow:"onShow",onHide:"onHide",onAnimationStart:"onAnimationStart",onAnimationDoneEmit:"onAnimationDoneEmit"},ngContentSelectors:Yu,decls:6,vars:9,consts:[["drawerContainer",""],["class","drawer-overlay",3,"show","click",4,"ngIf"],[1,"drawer-container"],[1,"drawer-content"],["class","drawer-footer",4,"ngIf"],[1,"drawer-overlay",3,"click"],[1,"drawer-footer"]],template:function(e,o){if(e&1){let r=E();kt(Gu),f(0,Xu,1,3,"div",1),u(1,"div",2,0),v("@slideInOut.done",function(a){return h(r),g(o.onAnimationDone(a))}),u(3,"div",3),lt(4),p(),f(5,Ju,2,0,"div",4),p()}e&2&&(s("ngIf",o.visible()&&o.modal),d(),Ne(o.drawerStyles()),W(o.drawerClasses()),s("@slideInOut",o.animationState()),d(2),Y("padding",o.contentPadding),d(2),s("ngIf",o.hasFooterContent))},dependencies:[R,fe],styles:[".drawer-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:100%;background-color:#0006;z-index:1000;opacity:0;transition:opacity .2s ease}.drawer-overlay.show[_ngcontent-%COMP%]{opacity:1}.drawer-container[_ngcontent-%COMP%]{position:fixed;z-index:1001;background:#565656;box-shadow:0 4px 6px -1px #0000001a,0 2px 4px -1px #0000000f;display:flex;flex-direction:column;overflow:hidden}.drawer-container.drawer-left[_ngcontent-%COMP%]{top:0;left:0;height:100vh;border-right:1px solid #e5e7eb;transform:translate(-100%)}.drawer-container.drawer-right[_ngcontent-%COMP%]{top:0;right:0;height:100vh;border-left:1px solid #e5e7eb;transform:translate(100%)}.drawer-container.drawer-top[_ngcontent-%COMP%]{top:0;left:0;width:100vw;border-bottom:1px solid #e5e7eb;transform:translateY(-100%)}.drawer-container.drawer-bottom[_ngcontent-%COMP%]{bottom:0;left:0;width:100vw;border-top:1px solid #e5e7eb;transform:translateY(100%)}.drawer-container.drawer-small.drawer-left[_ngcontent-%COMP%], .drawer-container.drawer-small.drawer-right[_ngcontent-%COMP%]{width:20rem}.drawer-container.drawer-small.drawer-top[_ngcontent-%COMP%], .drawer-container.drawer-small.drawer-bottom[_ngcontent-%COMP%]{height:20rem}.drawer-container.drawer-medium.drawer-left[_ngcontent-%COMP%], .drawer-container.drawer-medium.drawer-right[_ngcontent-%COMP%]{width:26rem}.drawer-container.drawer-medium.drawer-top[_ngcontent-%COMP%], .drawer-container.drawer-medium.drawer-bottom[_ngcontent-%COMP%]{height:26rem}.drawer-container.drawer-large.drawer-left[_ngcontent-%COMP%], .drawer-container.drawer-large.drawer-right[_ngcontent-%COMP%]{width:36rem}.drawer-container.drawer-large.drawer-top[_ngcontent-%COMP%], .drawer-container.drawer-large.drawer-bottom[_ngcontent-%COMP%]{height:36rem}.drawer-container.drawer-full.drawer-left[_ngcontent-%COMP%], .drawer-container.drawer-full.drawer-right[_ngcontent-%COMP%]{width:100vw}.drawer-container.drawer-full.drawer-top[_ngcontent-%COMP%], .drawer-container.drawer-full.drawer-bottom[_ngcontent-%COMP%]{height:100vh}.drawer-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:1.25rem 1.5rem;background:#565656;flex-shrink:0}.drawer-header[_ngcontent-%COMP%]   .drawer-title[_ngcontent-%COMP%]{font-size:1.125rem;font-weight:600;color:#111827}.drawer-header[_ngcontent-%COMP%]   .drawer-close-btn[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:2rem;height:2rem;border:none;background:transparent;border-radius:.375rem;color:#6b7280;cursor:pointer;transition:all .15s ease}.drawer-header[_ngcontent-%COMP%]   .drawer-close-btn[_ngcontent-%COMP%]:hover{background:#e5e7eb;color:#374151}.drawer-header[_ngcontent-%COMP%]   .drawer-close-btn[_ngcontent-%COMP%]:focus{outline:none;box-shadow:0 0 0 2px #3b82f6}.drawer-content[_ngcontent-%COMP%]{flex:1;overflow-y:auto;overflow-x:hidden}.drawer-footer[_ngcontent-%COMP%]{padding:1rem 1.5rem;border-top:1px solid #e5e7eb;background:#f9fafb;flex-shrink:0}@media (max-width: 768px){.drawer-container.drawer-left[_ngcontent-%COMP%], .drawer-container.drawer-right[_ngcontent-%COMP%]{width:100vw!important}}@media (prefers-color-scheme: dark){.drawer-container[_ngcontent-%COMP%]{background:#1f2937;color:#f9fafb}.drawer-container[_ngcontent-%COMP%]   .drawer-header[_ngcontent-%COMP%]{background:#374151;border-bottom-color:#4b5563}.drawer-container[_ngcontent-%COMP%]   .drawer-header[_ngcontent-%COMP%]   .drawer-title[_ngcontent-%COMP%]{color:#f9fafb}.drawer-container[_ngcontent-%COMP%]   .drawer-header[_ngcontent-%COMP%]   .drawer-close-btn[_ngcontent-%COMP%]{color:#d1d5db}.drawer-container[_ngcontent-%COMP%]   .drawer-header[_ngcontent-%COMP%]   .drawer-close-btn[_ngcontent-%COMP%]:hover{background:#4b5563;color:#f9fafb}.drawer-container[_ngcontent-%COMP%]   .drawer-footer[_ngcontent-%COMP%]{background:#374151;border-top-color:#4b5563}}"],data:{animation:[zt("slideInOut",[Ae("left-in",ke({transform:"translateX(0)"})),Ae("left-out",ke({transform:"translateX(-100%)"})),Ae("right-in",ke({transform:"translateX(0)"})),Ae("right-out",ke({transform:"translateX(100%)"})),Ae("top-in",ke({transform:"translateY(0)"})),Ae("top-out",ke({transform:"translateY(-100%)"})),Ae("bottom-in",ke({transform:"translateY(0)"})),Ae("bottom-out",ke({transform:"translateY(100%)"})),we("left-out => left-in",ye("300ms cubic-bezier(0.25, 0.8, 0.25, 1)")),we("left-in => left-out",ye("250ms cubic-bezier(0.25, 0.8, 0.25, 1)")),we("right-out => right-in",ye("300ms cubic-bezier(0.25, 0.8, 0.25, 1)")),we("right-in => right-out",ye("250ms cubic-bezier(0.25, 0.8, 0.25, 1)")),we("top-out => top-in",ye("300ms cubic-bezier(0.25, 0.8, 0.25, 1)")),we("top-in => top-out",ye("250ms cubic-bezier(0.25, 0.8, 0.25, 1)")),we("bottom-out => bottom-in",ye("300ms cubic-bezier(0.25, 0.8, 0.25, 1)")),we("bottom-in => bottom-out",ye("250ms cubic-bezier(0.25, 0.8, 0.25, 1)"))]),zt("fadeInOut",[Ae("in",ke({opacity:1})),Ae("out",ke({opacity:0})),we("out => in",ye("200ms ease-in")),we("in => out",ye("150ms ease-out"))])]}})}};var ep=["sliderHandle"],tp=["sliderHandleStart"],op=["sliderHandleEnd"],rp=(t,i,e,o)=>({"p-slider p-component":!0,"p-disabled":t,"p-slider-horizontal":i,"p-slider-vertical":e,"p-slider-animate":o}),ip=(t,i)=>({position:"absolute","inset-inline-start":t,width:i}),np=(t,i)=>({position:"absolute",bottom:t,height:i}),ap=t=>({position:"absolute",height:t}),sp=t=>({position:"absolute",width:t}),vr=(t,i)=>({position:"absolute","inset-inline-start":t,bottom:i}),Is=t=>({"p-slider-handle-active":t});function lp(t,i){if(t&1&&w(0,"span",8),t&2){let e=l();s("ngStyle",J(2,ip,e.offset!==null&&e.offset!==void 0?e.offset+"%":e.handleValues[0]+"%",e.diff?e.diff+"%":e.handleValues[1]-e.handleValues[0]+"%")),k("data-pc-section","range")}}function cp(t,i){if(t&1&&w(0,"span",8),t&2){let e=l();s("ngStyle",J(2,np,e.offset!==null&&e.offset!==void 0?e.offset+"%":e.handleValues[0]+"%",e.diff?e.diff+"%":e.handleValues[1]-e.handleValues[0]+"%")),k("data-pc-section","range")}}function dp(t,i){if(t&1&&w(0,"span",8),t&2){let e=l();s("ngStyle",O(2,ap,e.handleValue+"%")),k("data-pc-section","range")}}function up(t,i){if(t&1&&w(0,"span",8),t&2){let e=l();s("ngStyle",O(2,sp,e.handleValue+"%")),k("data-pc-section","range")}}function pp(t,i){if(t&1){let e=E();u(0,"span",9,0),v("touchstart",function(r){h(e);let n=l();return g(n.onDragStart(r))})("touchmove",function(r){h(e);let n=l();return g(n.onDrag(r))})("touchend",function(r){h(e);let n=l();return g(n.onDragEnd(r))})("mousedown",function(r){h(e);let n=l();return g(n.onMouseDown(r))})("keydown",function(r){h(e);let n=l();return g(n.onKeyDown(r))}),p()}if(t&2){let e=l();Y("transition",e.dragging?"none":null),s("ngStyle",J(12,vr,e.orientation=="horizontal"?e.handleValue+"%":null,e.orientation=="vertical"?e.handleValue+"%":null))("pAutoFocus",e.autofocus),k("tabindex",e.disabled?null:e.tabindex)("aria-valuemin",e.min)("aria-valuenow",e.value)("aria-valuemax",e.max)("aria-labelledby",e.ariaLabelledBy)("aria-label",e.ariaLabel)("aria-orientation",e.orientation)("data-pc-section","handle")}}function mp(t,i){if(t&1){let e=E();u(0,"span",10,1),v("keydown",function(r){h(e);let n=l();return g(n.onKeyDown(r,0))})("mousedown",function(r){h(e);let n=l();return g(n.onMouseDown(r,0))})("touchstart",function(r){h(e);let n=l();return g(n.onDragStart(r,0))})("touchmove",function(r){h(e);let n=l();return g(n.onDrag(r))})("touchend",function(r){h(e);let n=l();return g(n.onDragEnd(r))}),p()}if(t&2){let e=l();Y("transition",e.dragging?"none":null),s("ngStyle",J(13,vr,e.rangeStartLeft,e.rangeStartBottom))("ngClass",O(16,Is,e.handleIndex==0))("pAutoFocus",e.autofocus),k("tabindex",e.disabled?null:e.tabindex)("aria-valuemin",e.min)("aria-valuenow",e.value?e.value[0]:null)("aria-valuemax",e.max)("aria-labelledby",e.ariaLabelledBy)("aria-label",e.ariaLabel)("aria-orientation",e.orientation)("data-pc-section","startHandler")}}function fp(t,i){if(t&1){let e=E();u(0,"span",11,2),v("keydown",function(r){h(e);let n=l();return g(n.onKeyDown(r,1))})("mousedown",function(r){h(e);let n=l();return g(n.onMouseDown(r,1))})("touchstart",function(r){h(e);let n=l();return g(n.onDragStart(r,1))})("touchmove",function(r){h(e);let n=l();return g(n.onDrag(r))})("touchend",function(r){h(e);let n=l();return g(n.onDragEnd(r))}),p()}if(t&2){let e=l();Y("transition",e.dragging?"none":null),s("ngStyle",J(12,vr,e.rangeEndLeft,e.rangeEndBottom))("ngClass",O(15,Is,e.handleIndex==1)),k("tabindex",e.disabled?null:e.tabindex)("aria-valuemin",e.min)("aria-valuenow",e.value?e.value[1]:null)("aria-valuemax",e.max)("aria-labelledby",e.ariaLabelledBy)("aria-label",e.ariaLabel)("aria-orientation",e.orientation)("data-pc-section","endHandler")}}var hp=({dt:t})=>`
.p-slider {
    position: relative;
    background: ${t("slider.track.background")};
    border-radius: ${t("slider.border.radius")};
}

.p-slider-handle {
    cursor: grab;
    touch-action: none;
    display: flex;
    justify-content: center;
    align-items: center;
    height: ${t("slider.handle.height")};
    width: ${t("slider.handle.width")};
    background: ${t("slider.handle.background")};
    border-radius: ${t("slider.handle.border.radius")};
    transition: background ${t("slider.transition.duration")}, color ${t("slider.transition.duration")}, border-color ${t("slider.transition.duration")}, box-shadow ${t("slider.transition.duration")}, outline-color ${t("slider.transition.duration")};
    outline-color: transparent;
}

.p-slider-handle::before {
    content: "";
    width: ${t("slider.handle.content.width")};
    height: ${t("slider.handle.content.height")};
    display: block;
    background: ${t("slider.handle.content.background")};
    border-radius: ${t("slider.handle.content.border.radius")};
    box-shadow: ${t("slider.handle.content.shadow")};
    transition: background ${t("slider.transition.duration")};
}

.p-slider:not(.p-disabled) .p-slider-handle:hover {
    background: ${t("slider.handle.hover.background")};
}

.p-slider:not(.p-disabled) .p-slider-handle:hover::before {
    background: ${t("slider.handle.content.hover.background")};
}

.p-slider-handle:focus-visible {
    border-color: ${t("slider.handle.focus.border.color")};
    box-shadow: ${t("slider.handle.focus.ring.shadow")};
    outline: ${t("slider.handle.focus.ring.width")} ${t("slider.handle.focus.ring.style")} ${t("slider.handle.focus.ring.color")};
    outline-offset: ${t("slider.handle.focus.ring.offset")};
}

.p-slider-range {
    display: block;
    background: ${t("slider.range.background")};
    border-radius: ${t("slider.border.radius")};
}

.p-slider.p-slider-horizontal {
    height: ${t("slider.track.size")};
}

.p-slider-horizontal .p-slider-range {
    top: 0;
    inset-inline-start: 0;
    height: 100%;
}

.p-slider-horizontal .p-slider-handle {
    top: 50%;
    margin-top: calc(-1 * calc(${t("slider.handle.height")} / 2));
    margin-inline-start: calc(-1 * calc(${t("slider.handle.width")} / 2));
}

.p-slider-vertical {
    min-height: 100px;
    width: ${t("slider.track.size")};
}

.p-slider-vertical .p-slider-handle {
    inset-inline-start: 50%;
    margin-inline-start: calc(-1 * calc(${t("slider.handle.width")} / 2));
    margin-bottom: calc(-1 * calc(${t("slider.handle.height")} / 2));
}

.p-slider-vertical .p-slider-range {
    bottom: 0;
    inset-inline-start: 0;
    width: 100%;
}
`,gp={handle:{position:"absolute"},range:{position:"absolute"}},bp={root:({props:t})=>["p-slider p-component",{"p-disabled":t.disabled,"p-slider-horizontal":t.orientation==="horizontal","p-slider-vertical":t.orientation==="vertical"}],range:"p-slider-range",handle:"p-slider-handle"},xs=(()=>{class t extends Ce{name="slider";theme=hp;classes=bp;inlineStyles=gp;static \u0275fac=(()=>{let e;return function(r){return(e||(e=j(t)))(r||t)}})();static \u0275prov=B({token:t,factory:t.\u0275fac})}return t})();var _p={provide:po,useExisting:ht(()=>Ss),multi:!0},Ss=(()=>{class t extends ue{animate;disabled;min=0;max=100;orientation="horizontal";step;range;style;styleClass;ariaLabel;ariaLabelledBy;tabindex=0;autofocus;onChange=new V;onSlideEnd=new V;sliderHandle;sliderHandleStart;sliderHandleEnd;_componentStyle=y(xs);value;values;handleValue;handleValues=[];diff;offset;bottom;onModelChange=()=>{};onModelTouched=()=>{};dragging;dragListener;mouseupListener;initX;initY;barWidth;barHeight;sliderHandleClick;handleIndex=0;startHandleValue;startx;starty;ngZone=y($e);onMouseDown(e,o){this.disabled||(this.dragging=!0,this.updateDomData(),this.sliderHandleClick=!0,this.range&&this.handleValues&&this.handleValues[0]===this.max?this.handleIndex=0:this.handleIndex=o,this.bindDragListeners(),e.target.focus(),e.preventDefault(),this.animate&&Uo(this.el.nativeElement.children[0],"p-slider-animate"))}onDragStart(e,o){if(!this.disabled){var r=e.changedTouches[0];this.startHandleValue=this.range?this.handleValues[o]:this.handleValue,this.dragging=!0,this.range&&this.handleValues&&this.handleValues[0]===this.max?this.handleIndex=0:this.handleIndex=o,this.orientation==="horizontal"?(this.startx=parseInt(r.clientX,10),this.barWidth=this.el.nativeElement.children[0].offsetWidth):(this.starty=parseInt(r.clientY,10),this.barHeight=this.el.nativeElement.children[0].offsetHeight),this.animate&&Uo(this.el.nativeElement.children[0],"p-slider-animate"),e.preventDefault()}}onDrag(e){if(!this.disabled){var o=e.changedTouches[0],r=0;this.orientation==="horizontal"?r=Math.floor((parseInt(o.clientX,10)-this.startx)*100/this.barWidth)+this.startHandleValue:r=Math.floor((this.starty-parseInt(o.clientY,10))*100/this.barHeight)+this.startHandleValue,this.setValueFromHandle(e,r),e.preventDefault()}}onDragEnd(e){this.disabled||(this.dragging=!1,this.range?this.onSlideEnd.emit({originalEvent:e,values:this.values}):this.onSlideEnd.emit({originalEvent:e,value:this.value}),this.animate&&No(this.el.nativeElement.children[0],"p-slider-animate"),e.preventDefault())}onBarClick(e){this.disabled||(this.sliderHandleClick||(this.updateDomData(),this.handleChange(e),this.range?this.onSlideEnd.emit({originalEvent:e,values:this.values}):this.onSlideEnd.emit({originalEvent:e,value:this.value})),this.sliderHandleClick=!1)}onKeyDown(e,o){switch(this.handleIndex=o,e.code){case"ArrowDown":case"ArrowLeft":this.decrementValue(e,o),e.preventDefault();break;case"ArrowUp":case"ArrowRight":this.incrementValue(e,o),e.preventDefault();break;case"PageDown":this.decrementValue(e,o,!0),e.preventDefault();break;case"PageUp":this.incrementValue(e,o,!0),e.preventDefault();break;case"Home":this.updateValue(this.min,e),e.preventDefault();break;case"End":this.updateValue(this.max,e),e.preventDefault();break;default:break}}decrementValue(e,o,r=!1){let n;this.range?this.step?n=this.values[o]-this.step:n=this.values[o]-1:this.step?n=this.value-this.step:!this.step&&r?n=this.value-10:n=this.value-1,this.updateValue(n,e),e.preventDefault()}incrementValue(e,o,r=!1){let n;this.range?this.step?n=this.values[o]+this.step:n=this.values[o]+1:this.step?n=this.value+this.step:!this.step&&r?n=this.value+10:n=this.value+1,this.updateValue(n,e),e.preventDefault()}handleChange(e){let o=this.calculateHandleValue(e);this.setValueFromHandle(e,o)}bindDragListeners(){he(this.platformId)&&this.ngZone.runOutsideAngular(()=>{let e=this.el?this.el.nativeElement.ownerDocument:this.document;this.dragListener||(this.dragListener=this.renderer.listen(e,"mousemove",o=>{this.dragging&&this.ngZone.run(()=>{this.handleChange(o)})})),this.mouseupListener||(this.mouseupListener=this.renderer.listen(e,"mouseup",o=>{this.dragging&&(this.dragging=!1,this.ngZone.run(()=>{this.range?this.onSlideEnd.emit({originalEvent:o,values:this.values}):this.onSlideEnd.emit({originalEvent:o,value:this.value}),this.animate&&No(this.el.nativeElement.children[0],"p-slider-animate")}))}))})}unbindDragListeners(){this.dragListener&&(this.dragListener(),this.dragListener=null),this.mouseupListener&&(this.mouseupListener(),this.mouseupListener=null)}setValueFromHandle(e,o){let r=this.getValueFromHandle(o);this.range?this.step?this.handleStepChange(r,this.values[this.handleIndex]):(this.handleValues[this.handleIndex]=o,this.updateValue(r,e)):this.step?this.handleStepChange(r,this.value):(this.handleValue=o,this.updateValue(r,e)),this.cd.markForCheck()}handleStepChange(e,o){let r=e-o,n=o,a=this.step;r<0?n=o+Math.ceil(e/a-o/a)*a:r>0&&(n=o+Math.floor(e/a-o/a)*a),this.updateValue(n),this.updateHandleValue()}writeValue(e){this.range?this.values=e||[0,0]:this.value=e||0,this.updateHandleValue(),this.updateDiffAndOffset(),this.cd.markForCheck()}registerOnChange(e){this.onModelChange=e}registerOnTouched(e){this.onModelTouched=e}setDisabledState(e){this.disabled=e,this.cd.markForCheck()}get rangeStartLeft(){return this.isVertical()?null:this.handleValues[0]>100?"100%":this.handleValues[0]+"%"}get rangeStartBottom(){return this.isVertical()?this.handleValues[0]+"%":"auto"}get rangeEndLeft(){return this.isVertical()?null:this.handleValues[1]+"%"}get rangeEndBottom(){return this.isVertical()?this.handleValues[1]+"%":"auto"}isVertical(){return this.orientation==="vertical"}updateDomData(){let e=this.el.nativeElement.children[0].getBoundingClientRect();this.initX=e.left+pi(),this.initY=e.top+mi(),this.barWidth=this.el.nativeElement.children[0].offsetWidth,this.barHeight=this.el.nativeElement.children[0].offsetHeight}calculateHandleValue(e){return this.orientation==="horizontal"?yi(this.el.nativeElement)?(this.initX+this.barWidth-e.pageX)*100/this.barWidth:(e.pageX-this.initX)*100/this.barWidth:(this.initY+this.barHeight-e.pageY)*100/this.barHeight}updateHandleValue(){this.range?(this.handleValues[0]=(this.values[0]<this.min?0:this.values[0]-this.min)*100/(this.max-this.min),this.handleValues[1]=(this.values[1]>this.max?100:this.values[1]-this.min)*100/(this.max-this.min)):this.value<this.min?this.handleValue=0:this.value>this.max?this.handleValue=100:this.handleValue=(this.value-this.min)*100/(this.max-this.min),this.step&&this.updateDiffAndOffset()}updateDiffAndOffset(){this.diff=this.getDiff(),this.offset=this.getOffset()}getDiff(){return Math.abs(this.handleValues[0]-this.handleValues[1])}getOffset(){return Math.min(this.handleValues[0],this.handleValues[1])}updateValue(e,o){if(this.range){let r=e;this.handleIndex==0?(r<this.min?(r=this.min,this.handleValues[0]=0):r>this.values[1]&&r>this.max&&(r=this.max,this.handleValues[0]=100),this.sliderHandleStart?.nativeElement.focus()):(r>this.max?(r=this.max,this.handleValues[1]=100,this.offset=this.handleValues[1]):r<this.min?(r=this.min,this.handleValues[1]=0):r<this.values[0]&&(this.offset=this.handleValues[1]),this.sliderHandleEnd?.nativeElement.focus()),this.step?this.updateHandleValue():this.updateDiffAndOffset(),this.values[this.handleIndex]=this.getNormalizedValue(r);let n=[this.minVal,this.maxVal];this.onModelChange(n),this.onChange.emit({event:o,values:this.values})}else e<this.min?(e=this.min,this.handleValue=0):e>this.max&&(e=this.max,this.handleValue=100),this.value=this.getNormalizedValue(e),this.onModelChange(this.value),this.onChange.emit({event:o,value:this.value}),this.sliderHandle?.nativeElement.focus();this.updateHandleValue()}getValueFromHandle(e){return(this.max-this.min)*(e/100)+this.min}getDecimalsCount(e){return e&&Math.floor(e)!==e&&e.toString().split(".")[1].length||0}getNormalizedValue(e){let o=this.getDecimalsCount(this.step);return o>0?+parseFloat(e.toString()).toFixed(o):Math.floor(e)}ngOnDestroy(){this.unbindDragListeners(),super.ngOnDestroy()}get minVal(){return Math.min(this.values[1],this.values[0])}get maxVal(){return Math.max(this.values[1],this.values[0])}static \u0275fac=(()=>{let e;return function(r){return(e||(e=j(t)))(r||t)}})();static \u0275cmp=F({type:t,selectors:[["p-slider"]],viewQuery:function(o,r){if(o&1&&(H(ep,5),H(tp,5),H(op,5)),o&2){let n;I(n=S())&&(r.sliderHandle=n.first),I(n=S())&&(r.sliderHandleStart=n.first),I(n=S())&&(r.sliderHandleEnd=n.first)}},inputs:{animate:[2,"animate","animate",T],disabled:[2,"disabled","disabled",T],min:[2,"min","min",G],max:[2,"max","max",G],orientation:"orientation",step:[2,"step","step",G],range:[2,"range","range",T],style:"style",styleClass:"styleClass",ariaLabel:"ariaLabel",ariaLabelledBy:"ariaLabelledBy",tabindex:[2,"tabindex","tabindex",G],autofocus:[2,"autofocus","autofocus",T]},outputs:{onChange:"onChange",onSlideEnd:"onSlideEnd"},features:[ve([_p,xs]),ae],decls:8,vars:18,consts:[["sliderHandle",""],["sliderHandleStart",""],["sliderHandleEnd",""],[3,"click","ngStyle","ngClass"],["class","p-slider-range",3,"ngStyle",4,"ngIf"],["class","p-slider-handle","role","slider",3,"transition","ngStyle","pAutoFocus","touchstart","touchmove","touchend","mousedown","keydown",4,"ngIf"],["class","p-slider-handle","role","slider",3,"transition","ngStyle","ngClass","pAutoFocus","keydown","mousedown","touchstart","touchmove","touchend",4,"ngIf"],["class","p-slider-handle","role","slider",3,"transition","ngStyle","ngClass","keydown","mousedown","touchstart","touchmove","touchend",4,"ngIf"],[1,"p-slider-range",3,"ngStyle"],["role","slider",1,"p-slider-handle",3,"touchstart","touchmove","touchend","mousedown","keydown","ngStyle","pAutoFocus"],["role","slider",1,"p-slider-handle",3,"keydown","mousedown","touchstart","touchmove","touchend","ngStyle","ngClass","pAutoFocus"],["role","slider",1,"p-slider-handle",3,"keydown","mousedown","touchstart","touchmove","touchend","ngStyle","ngClass"]],template:function(o,r){o&1&&(u(0,"div",3),v("click",function(a){return r.onBarClick(a)}),f(1,lp,1,5,"span",4)(2,cp,1,5,"span",4)(3,dp,1,4,"span",4)(4,up,1,4,"span",4)(5,pp,2,15,"span",5)(6,mp,2,18,"span",6)(7,fp,2,17,"span",7),p()),o&2&&(W(r.styleClass),s("ngStyle",r.style)("ngClass",Gr(13,rp,r.disabled,r.orientation=="horizontal",r.orientation=="vertical",r.animate)),k("data-pc-name","slider")("data-pc-section","root"),d(),s("ngIf",r.range&&r.orientation=="horizontal"),d(),s("ngIf",r.range&&r.orientation=="vertical"),d(),s("ngIf",!r.range&&r.orientation=="vertical"),d(),s("ngIf",!r.range&&r.orientation=="horizontal"),d(),s("ngIf",!r.range),d(),s("ngIf",r.range),d(),s("ngIf",r.range))},dependencies:[R,oe,fe,le,mo,z],encapsulation:2,changeDetection:0})}return t})(),Ts=(()=>{class t{static \u0275fac=function(o){return new(o||t)};static \u0275mod=ne({type:t});static \u0275inj=ie({imports:[Ss,z,z]})}return t})();var vp=["videoElement"],yp=["volumeTrack"],kp=["volumeHandle"];function wp(t,i){if(t&1&&w(0,"app-lyric-scroll",20),t&2){let e=l();s("currentTime",e.currentTime)}}function Cp(t,i){t&1&&w(0,"i",26)}function xp(t,i){if(t&1){let e=E();u(0,"li",25),f(1,Cp,1,0,"i",26),u(2,"span",27),v("click",function(){let r=h(e).$implicit,n=l();return g(n.playVideo(r))}),A(3),Le(4,"removeExtension"),p(),u(5,"i",28),v("click",function(){let r=h(e).$implicit,n=l();return g(n.playerService.removeFromPlayerList(r))}),p()()}if(t&2){let e=i.$implicit,o=l();io("active",o.activeItem().fileId===e.fileId),d(),Be(o.activeItem().fileId===e.fileId?1:-1),d(2),me(" ",Pe(4,4,e.fileName)," ")}}var Lo=class t{constructor(){this.currentTime=D(0);this.duration=D(0);this.isPlaying=D(!1);this.drawerService=y(nt);this.i18nService=y(Ge);this.themeService=y(at);this.playerService=y(tn);this.cacheVideoService=y(en);this.playMode=D("loop");this.volume=D(70);this.isMuted=D(!1);this.lastVolume=70;this.isDragging=!1;this.activeItem=D({})}onVolumeTrackClick(i){if(this.isDragging)return;let o=this.volumeTrack.nativeElement.getBoundingClientRect(),r=i.clientX-o.left,n=o.width,a=Math.round(r/n*100);this.setVolume(Math.max(0,Math.min(100,a)))}onVolumeHandleMouseDown(i){i.preventDefault(),i.stopPropagation(),this.isDragging=!0;let e=this.volumeTrack.nativeElement,o=this.volumeHandle.nativeElement;o.classList.add("scale-125");let r=a=>{let c=e.getBoundingClientRect(),m=a.clientX-c.left,_=c.width,x=m/_*100;x=Math.max(0,Math.min(100,x)),this.setVolume(Math.round(x))},n=()=>{this.isDragging=!1,o.classList.remove("scale-125"),document.removeEventListener("mousemove",r),document.removeEventListener("mouseup",n),document.removeEventListener("mouseleave",n)};document.addEventListener("mousemove",r),document.addEventListener("mouseup",n),document.addEventListener("mouseleave",n)}setVolume(i){let e=Math.max(0,Math.min(100,i));this.volume.set(e),this.updateVideoVolume(),e>0&&this.isMuted()&&this.isMuted.set(!1)}toggleMute(){this.isMuted()?(this.isMuted.set(!1),this.setVolume(this.lastVolume)):(this.lastVolume=this.volume(),this.isMuted.set(!0),this.setVolume(0))}updateVideoVolume(){this.videoElement?.nativeElement&&(this.videoElement.nativeElement.volume=this.volume()/100,this.videoElement.nativeElement.muted=this.isMuted())}ngAfterViewInit(){setTimeout(()=>{this.updateVideoVolume(),this.#e()}),this.playerService.activeItem$.subscribe(i=>{i&&this.playVideo(i)})}#e(){let i=this.videoElement.nativeElement;this.playerService.drawerService,i.addEventListener("timeupdate",()=>{this.currentTime.set(i.currentTime),this.duration.set(i.duration)})}tooglePlay(){let i=this.videoElement.nativeElement;this.isPlaying()?(i.pause(),this.isPlaying.set(!1)):(i.play(),this.isPlaying.set(!0))}changePlayMode(){this.playMode()==="loop"?this.playMode.set("single"):this.playMode()==="single"?this.playMode.set("random"):this.playMode.set("loop")}playVideo(i){return xe(this,null,function*(){let e=yield this.cacheVideoService.getCachedVideo(i.fileUrl);e?this.videoElement.nativeElement.src=URL.createObjectURL(e.blob):(this.videoElement.nativeElement.src=i.fileUrl,this.cacheVideoService.smartCacheVideo(i.fileUrl,i.fileName)),this.isPlaying.set(!0),this.activeItem.set(i)})}playNext(){if(this.playMode()!=="single"){if(this.playMode()==="loop"){let i=this.playerService.getPlayerList();if(i.length<=1)return;let e=i.findIndex(r=>r.fileId===this.activeItem().fileId);if(e===-1)return;let o=(e+1)%i.length;this.playVideo(i[o])}else if(this.playMode()==="random"){let i=this.playerService.getPlayerList();if(i.length<=1)return;let e=Math.floor(Math.random()*i.length);this.playVideo(i[e])}}}static{this.\u0275fac=function(e){return new(e||t)}}static{this.\u0275cmp=F({type:t,selectors:[["app-player-drawer"]],viewQuery:function(e,o){if(e&1&&(H(vp,5),H(yp,5),H(kp,5)),e&2){let r;I(r=S())&&(o.videoElement=r.first),I(r=S())&&(o.volumeTrack=r.first),I(r=S())&&(o.volumeHandle=r.first)}},decls:31,vars:15,consts:[["videoElement",""],["volumeTrack",""],["volumeHandle",""],["volumeButton",""],["position","right",1,"player-drawer",3,"onHide","visible","modal"],[1,"pi","pi-times","text-[#ccc]","hover:text-[#fff]","cursor-pointer","p-2","rounded-full","mb-2",3,"click"],["controls","","src","","autoplay","",1,"bg-[#000]","w-full","h-[13.5rem]"],[1,"flex","justify-between","items-center","py-2"],[1,"flex","gap-4"],["src","assets/images/pre.svg","alt","Pre"],["alt","Play",3,"click","src"],["src","assets/images/next.svg","alt","Next",3,"click"],[1,"flex","gap-2"],[1,"flex","items-center","gap-2","relative","hover:bg-[#333]","rounded-full","px-2","cursor-pointer","transition","duration-300"],[1,"volume-slider","w-20","h-1","bg-[#ccc]","rounded-full","cursor-pointer","relative",3,"click"],[1,"volume-fill","h-full","bg-[#fff]","rounded-full"],[1,"volume-handle","w-3","h-3","bg-[#fff]","rounded-full","absolute","cursor-pointer",3,"mousedown"],["src","assets/images/volume.svg","alt","\u97F3\u91CF",1,"cursor-pointer"],[1,"flex","items-center",3,"click"],["alt","",3,"src"],[3,"currentTime"],[1,"text-[#fff]"],[1,"flex","items-center","justify-between","mt-6","mb-2"],["src","assets/images/delete.svg","alt","",3,"click"],[1,"flex","items-center","justify-between","py-2","text-[#ccc]","hover:text-[#fff]","cursor-pointer",3,"active"],[1,"flex","items-center","justify-between","py-2","text-[#ccc]","hover:text-[#fff]","cursor-pointer"],[1,"pi","pi-volume-up","mr-2"],[1,"truncate","flex-1","mr-4","cursor-pointer",3,"click"],[1,"pi","pi-times","flex-shrink-0",3,"click"]],template:function(e,o){if(e&1){let r=E();u(0,"app-drawer",4),v("onHide",function(){return h(r),g(o.drawerService.closePlayer())}),u(1,"i",5),v("click",function(){return h(r),g(o.drawerService.playerVisible.set(!1))}),p(),w(2,"video",6,0),u(4,"div",7)(5,"div",8),w(6,"img",9),u(7,"img",10),v("click",function(){return h(r),g(o.tooglePlay())}),p(),u(8,"img",11),v("click",function(){return h(r),g(o.playNext())}),p()(),u(9,"div",12)(10,"div",13)(11,"div",14,1),v("click",function(a){return h(r),g(o.onVolumeTrackClick(a))}),w(13,"div",15),u(14,"div",16,2),v("mousedown",function(a){return h(r),g(o.onVolumeHandleMouseDown(a))}),p()(),w(16,"img",17,3),p(),u(18,"div",18),v("click",function(){return h(r),g(o.changePlayMode())}),w(19,"img",19),p()()(),u(20,"div"),f(21,wp,1,1,"app-lyric-scroll",20),p(),u(22,"div",21)(23,"ul")(24,"li",22)(25,"span"),A(26,"Play List"),p(),u(27,"img",23),v("click",function(){return h(r),g(o.playerService.clearPlayerList())}),p()(),je(28,xp,6,6,"li",24,Ue),Le(30,"async"),p()()()}e&2&&(s("visible",o.drawerService.playerVisible)("modal",!0),d(7),s("src",o.isPlaying()?"assets/images/pause.svg":"assets/images/play.svg",st),d(6),Y("width",o.volume(),"%"),d(),Y("left",o.volume(),"%")("transform","translateX(-50%) translateY(-50%)")("top","50%"),d(5),s("src",o.playMode()==="loop"?"assets/images/loop.svg":o.playMode()==="single"?"assets/images/single.svg":"assets/images/random.svg",st),d(2),Be(o.drawerService.playerVisible()?21:-1),d(7),Ke(Pe(30,13,o.playerService.playerList$)))},dependencies:[R,xt,ko,go,Dt,Rt,on,Vo,Fo,Ts],styles:['.player-drawer.p-drawer{background:#565656;width:25rem}  .player-drawer.p-drawer .p-button-secondary:hover{background:#27272a}img[_ngcontent-%COMP%]{cursor:pointer;transition-property:transform;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.2s}img[_ngcontent-%COMP%]:hover{--tw-scale-x: 1.1;--tw-scale-y: 1.1;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.volume-control[_ngcontent-%COMP%]{position:relative}[_ngcontent-%COMP%]:global(.volume-dropdown)   .p-overlaypanel-content[_ngcontent-%COMP%]{padding:0}.volume-slider[_ngcontent-%COMP%]{min-width:80px}.volume-slider[_ngcontent-%COMP%]   .volume-slider-container[_ngcontent-%COMP%]{position:relative}.volume-slider[_ngcontent-%COMP%]   .volume-slider-container[_ngcontent-%COMP%]   .volume-fill[_ngcontent-%COMP%]{background:linear-gradient(to top,#3b82f6,#60a5fa)}.volume-slider[_ngcontent-%COMP%]   .volume-slider-container[_ngcontent-%COMP%]   .volume-handle[_ngcontent-%COMP%]{transform:translate(-50%)}.volume-slider[_ngcontent-%COMP%]   .volume-slider-container[_ngcontent-%COMP%]   .volume-handle[_ngcontent-%COMP%]:hover{transform:translate(-50%) scale(1.1)}.volume-slider[_ngcontent-%COMP%]   .volume-slider-container[_ngcontent-%COMP%]   .volume-handle.dragging[_ngcontent-%COMP%]{transform:translate(-50%) scale(1.2)}.volume-presets[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{transition:all .2s ease}.volume-presets[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{background-color:#e5e7eb;transform:translateY(-1px)}.volume-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{transition:all .2s ease}.volume-icon[_ngcontent-%COMP%]   i.pi-volume-off[_ngcontent-%COMP%]{color:#ef4444}.volume-icon[_ngcontent-%COMP%]   i.pi-volume-down[_ngcontent-%COMP%]{color:#f59e0b}.volume-icon[_ngcontent-%COMP%]   i.pi-volume-up[_ngcontent-%COMP%]{color:#10b981}.custom-video-controls[_ngcontent-%COMP%]::-webkit-media-controls-play-button{display:none!important}.custom-video-controls[_ngcontent-%COMP%]::-webkit-media-controls-mute-button{display:none!important}.custom-video-controls[_ngcontent-%COMP%]::-webkit-media-controls-volume-slider{display:none!important}.custom-video-controls[_ngcontent-%COMP%]::-moz-media-controls-play-button{display:none!important}.custom-video-controls[_ngcontent-%COMP%]::-moz-media-controls-mute-button{display:none!important}.custom-video-controls[_ngcontent-%COMP%]::-moz-media-controls-volume-slider{display:none!important}.active[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}  .p-slider-handle{height:10px;width:10px}  .p-slider-handle:before{content:"";width:10px;height:10px;display:block;background:var(--p-slider-handle-content-background);border-radius:var(--p-slider-handle-content-border-radius);box-shadow:var(--p-slider-handle-content-shadow);transition:background var(--p-slider-transition-duration)}']})}};var Ip=t=>({"background-color":t});function Sp(t,i){if(t&1){let e=E();u(0,"div",11),v("click",function(){let r=h(e).$implicit,n=l();return g(n.themeService.setTheme(r.mode))}),p()}if(t&2){let e=i.$implicit;Ne(O(2,Ip,e.color))}}var Po=class t{constructor(){this.drawerService=y(nt);this.i18nService=y(Ge);this.themeService=y(at);this.audioLanguages=ee(()=>this.i18nService.supportedAudioDevices.map(i=>({label:i.label,command:()=>this.i18nService.setAudioDevice(i.code)})))}changeFontSize(i){this.themeService.changeFontSize(i)}static{this.\u0275fac=function(e){return new(e||t)}}static{this.\u0275cmp=F({type:t,selectors:[["app-setting-drawer"]],decls:21,vars:11,consts:[["menu1",""],[1,"w-50",3,"onHide","visible","header","position","modal"],[1,"drawer-content"],[1,"setting-item","flex","justify-between","items-center"],[1,"language-selector","flex","items-center"],["appendTo","body",3,"model","popup"],["size","small",3,"click","label","text"],[1,"language-selector","flex","items-center","gap-2"],[1,"w-6","h-6","rounded-full","cursor-pointer",3,"style"],["label","A-","outlined","","size","small",3,"click"],["label","A+","outlined","","size","small",3,"click"],[1,"w-6","h-6","rounded-full","cursor-pointer",3,"click"]],template:function(e,o){if(e&1){let r=E();u(0,"p-drawer",1),v("onHide",function(){return h(r),g(o.drawerService.closeSettings())}),u(1,"div",2)(2,"div",3)(3,"label"),A(4),p(),u(5,"div",4),w(6,"p-menu",5,0),u(8,"p-button",6),v("click",function(a){h(r);let c=X(7);return g(c.toggle(a))}),p()()(),u(9,"div",3)(10,"label"),A(11),p(),u(12,"div",7),je(13,Sp,1,4,"div",8,Ue),p()(),u(15,"div",3)(16,"label"),A(17),p(),u(18,"div",7)(19,"p-button",9),v("click",function(){return h(r),g(o.changeFontSize("-"))}),p(),u(20,"p-button",10),v("click",function(){return h(r),g(o.changeFontSize("+"))}),p()()()()()}e&2&&(s("visible",o.drawerService.settingVisible())("header",o.i18nService.translate("settings"))("position","right")("modal",!0),d(4),_e(o.i18nService.translate("audio_lan_settings")),d(2),s("model",o.audioLanguages())("popup",!0),d(2),s("label",o.i18nService.currentAudioDeviceInfo().label)("text",!0),d(3),_e(o.i18nService.translate("theme")),d(2),Ke(o.themeService.themes),d(4),_e(o.i18nService.translate("font_size")))},dependencies:[R,ko,nn,go,Dt,ho,Rt,At],styles:[".drawer-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:1rem 0 .5rem;font-weight:600;font-size:1rem}.drawer-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]:first-child{margin-top:0}.drawer-content[_ngcontent-%COMP%]   .setting-item[_ngcontent-%COMP%]{margin-bottom:1.5rem}.drawer-content[_ngcontent-%COMP%]   .setting-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-weight:500;font-size:.875rem;display:block;margin-bottom:.25rem}.drawer-content[_ngcontent-%COMP%]   .setting-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.875rem;margin:0;line-height:1.4}"]})}};var Tp=["content"],Ep=["mask"],Mp=["*"],Op=t=>({"p-blockui-mask-document":t,"p-blockui p-blockui-mask p-overlay-mask":!0}),Bp=()=>({display:"none"});function Dp(t,i){t&1&&U(0)}var Vp=({dt:t})=>`
.p-blockui {
    position: relative;
}

.p-blockui-mask {
    border-radius: ${t("blockui.border.radius")};
}

.p-blockui-mask.p-overlay-mask {
    position: absolute;
}

.p-blockui-mask-document.p-overlay-mask {
    position: fixed;
}
`,Fp={root:"p-blockui"},Es=(()=>{class t extends Ce{name="blockui";theme=Vp;classes=Fp;static \u0275fac=(()=>{let e;return function(r){return(e||(e=j(t)))(r||t)}})();static \u0275prov=B({token:t,factory:t.\u0275fac})}return t})();var yr=(()=>{class t extends ue{target;autoZIndex=!0;baseZIndex=0;styleClass;get blocked(){return this._blocked}set blocked(e){this.mask&&this.mask.nativeElement?e?this.block():this.unblock():this._blocked=e}contentTemplate;mask;_blocked=!1;animationEndListener;_componentStyle=y(Es);constructor(){super()}ngAfterViewInit(){if(super.ngAfterViewInit(),this._blocked&&this.block(),this.target&&!this.target.getBlockableElement)throw"Target of BlockUI must implement BlockableUI interface"}_contentTemplate;templates;ngAfterContentInit(){this.templates.forEach(e=>{switch(e.getType()){case"content":this.contentTemplate=e.template;break;default:this.contentTemplate=e.template;break}})}block(){he(this.platformId)&&(this._blocked=!0,this.mask.nativeElement.style.display="flex",this.target?(this.target.getBlockableElement().appendChild(this.mask.nativeElement),this.target.getBlockableElement().style.position="relative"):(this.renderer.appendChild(this.document.body,this.mask.nativeElement),ui()),this.autoZIndex&&qe.set("modal",this.mask.nativeElement,this.baseZIndex+this.config.zIndex.modal))}unblock(){he(this.platformId)&&this.mask&&!this.animationEndListener&&this.destroyModal()}destroyModal(){this._blocked=!1,this.mask&&he(this.platformId)&&(qe.clear(this.mask.nativeElement),this.renderer.removeChild(this.el.nativeElement,this.mask.nativeElement),lo()),this.unbindAnimationEndListener(),this.cd.markForCheck()}unbindAnimationEndListener(){this.animationEndListener&&this.mask&&(this.animationEndListener(),this.animationEndListener=null)}ngOnDestroy(){this.unblock(),this.destroyModal(),super.ngOnDestroy()}static \u0275fac=function(o){return new(o||t)};static \u0275cmp=F({type:t,selectors:[["p-blockUI"],["p-blockui"],["p-block-ui"]],contentQueries:function(o,r,n){if(o&1&&(Z(n,Tp,4),Z(n,Ve,4)),o&2){let a;I(a=S())&&(r.contentTemplate=a.first),I(a=S())&&(r.templates=a)}},viewQuery:function(o,r){if(o&1&&H(Ep,5),o&2){let n;I(n=S())&&(r.mask=n.first)}},inputs:{target:"target",autoZIndex:[2,"autoZIndex","autoZIndex",T],baseZIndex:[2,"baseZIndex","baseZIndex",G],styleClass:"styleClass",blocked:[2,"blocked","blocked",T]},features:[ve([Es]),ae],ngContentSelectors:Mp,decls:4,vars:11,consts:[["mask",""],[3,"ngClass","ngStyle"],[4,"ngTemplateOutlet"]],template:function(o,r){o&1&&(kt(),u(0,"div",1,0),lt(2),f(3,Dp,1,0,"ng-container",2),p()),o&2&&(W(r.styleClass),s("ngClass",O(8,Op,!r.target))("ngStyle",We(10,Bp)),k("aria-busy",r.blocked)("data-pc-name","blockui")("data-pc-section","root"),d(3),s("ngTemplateOutlet",r.contentTemplate||r._contentTemplate))},dependencies:[R,oe,Ee,le,z],encapsulation:2,changeDetection:0})}return t})(),Ms=(()=>{class t{static \u0275fac=function(o){return new(o||t)};static \u0275mod=ne({type:t});static \u0275inj=ie({imports:[yr,z,z]})}return t})();var Pp=({dt:t})=>`
.p-progressspinner {
    position: relative;
    margin: 0 auto;
    width: 100px;
    height: 100px;
    display: inline-block;
}

.p-progressspinner::before {
    content: "";
    display: block;
    padding-top: 100%;
}

.p-progressspinner-spin {
    height: 100%;
    transform-origin: center center;
    width: 100%;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    animation: p-progressspinner-rotate 2s linear infinite;
}

.p-progressspinner-circle {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: 0;
    stroke: ${t("progressspinner.colorOne")};
    animation: p-progressspinner-dash 1.5s ease-in-out infinite, p-progressspinner-color 6s ease-in-out infinite;
    stroke-linecap: round;
}

@keyframes p-progressspinner-rotate {
    100% {
        transform: rotate(360deg);
    }
}
@keyframes p-progressspinner-dash {
    0% {
        stroke-dasharray: 1, 200;
        stroke-dashoffset: 0;
    }
    50% {
        stroke-dasharray: 89, 200;
        stroke-dashoffset: -35px;
    }
    100% {
        stroke-dasharray: 89, 200;
        stroke-dashoffset: -124px;
    }
}
@keyframes p-progressspinner-color {
    100%,
    0% {
        stroke: ${t("progressspinner.colorOne")};
    }
    40% {
        stroke: ${t("progressspinner.colorTwo")};
    }
    66% {
        stroke: ${t("progressspinner.colorThree")};
    }
    80%,
    90% {
        stroke: ${t("progressspinner.colorFour")};
    }
}
`,Ap={root:"p-progressspinner",spin:"p-progressspinner-spin",circle:"p-progressspinner-circle"},Os=(()=>{class t extends Ce{name="progressspinner";theme=Pp;classes=Ap;static \u0275fac=(()=>{let e;return function(r){return(e||(e=j(t)))(r||t)}})();static \u0275prov=B({token:t,factory:t.\u0275fac})}return t})();var Rp=(()=>{class t extends ue{styleClass;style;strokeWidth="2";fill="none";animationDuration="2s";ariaLabel;_componentStyle=y(Os);static \u0275fac=(()=>{let e;return function(r){return(e||(e=j(t)))(r||t)}})();static \u0275cmp=F({type:t,selectors:[["p-progressSpinner"],["p-progress-spinner"],["p-progressspinner"]],inputs:{styleClass:"styleClass",style:"style",strokeWidth:"strokeWidth",fill:"fill",animationDuration:"animationDuration",ariaLabel:"ariaLabel"},features:[ve([Os]),ae],decls:3,vars:11,consts:[["role","progressbar",1,"p-progressspinner",3,"ngStyle","ngClass"],["viewBox","25 25 50 50",1,"p-progressspinner-spin"],["cx","50","cy","50","r","20","stroke-miterlimit","10",1,"p-progressspinner-circle"]],template:function(o,r){o&1&&(u(0,"div",0),to(),u(1,"svg",1),w(2,"circle",2),p()()),o&2&&(s("ngStyle",r.style)("ngClass",r.styleClass),k("aria-label",r.ariaLabel)("aria-busy",!0)("data-pc-name","progressspinner")("data-pc-section","root"),d(),Y("animation-duration",r.animationDuration),k("data-pc-section","root"),d(),k("fill",r.fill)("stroke-width",r.strokeWidth))},dependencies:[R,oe,le,z],encapsulation:2,changeDetection:0})}return t})(),Bs=(()=>{class t{static \u0275fac=function(o){return new(o||t)};static \u0275mod=ne({type:t});static \u0275inj=ie({imports:[Rp,z,z]})}return t})();var Hp=["content"],zp=(t,i)=>({"p-progressbar p-component":!0,"p-progressbar-determinate":t,"p-progressbar-indeterminate":i}),$p=t=>({$implicit:t});function Np(t,i){if(t&1&&(u(0,"div"),A(1),p()),t&2){let e=l(2);Y("display",e.value!=null&&e.value!==0?"flex":"none"),k("data-pc-section","label"),d(),Kr("",e.value,"",e.unit,"")}}function Up(t,i){t&1&&U(0)}function jp(t,i){if(t&1&&(u(0,"div",3)(1,"div",4),f(2,Np,2,5,"div",5)(3,Up,1,0,"ng-container",6),p()()),t&2){let e=l();W(e.valueStyleClass),Y("width",e.value+"%")("background",e.color),s("ngClass","p-progressbar-value p-progressbar-value-animate"),k("data-pc-section","value"),d(2),s("ngIf",e.showValue&&!e.contentTemplate&&!e._contentTemplate),d(),s("ngTemplateOutlet",e.contentTemplate||e._contentTemplate)("ngTemplateOutletContext",O(11,$p,e.value))}}function Kp(t,i){if(t&1&&(u(0,"div",7),w(1,"div",8),p()),t&2){let e=l();W(e.valueStyleClass),s("ngClass","p-progressbar-indeterminate-container"),k("data-pc-section","container"),d(),Y("background",e.color),k("data-pc-section","value")}}var Wp=({dt:t})=>`
.p-progressbar {
    position: relative;
    overflow: hidden;
    height: ${t("progressbar.height")};
    background: ${t("progressbar.background")};
    border-radius: ${t("progressbar.border.radius")};
}

.p-progressbar-value {
    margin: 0;
    background: ${t("progressbar.value.background")};
}

.p-progressbar-label {
    color: ${t("progressbar.label.color")};
    font-size: ${t("progressbar.label.font.size")};
    font-weight: ${t("progressbar.label.font.weight")};
}

.p-progressbar-determinate .p-progressbar-value {
    height: 100%;
    width: 0%;
    position: absolute;
    display: none;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    transition: width 1s ease-in-out;
}

.p-progressbar-determinate .p-progressbar-label {
    display: inline-flex;
}

.p-progressbar-indeterminate .p-progressbar-value::before {
    content: "";
    position: absolute;
    background: inherit;
    top: 0;
    inset-inline-start: 0;
    bottom: 0;
    will-change: inset-inline-start, inset-inline-end;
    animation: p-progressbar-indeterminate-anim 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite;
}

.p-progressbar-indeterminate .p-progressbar-value::after {
    content: "";
    position: absolute;
    background: inherit;
    top: 0;
    inset-inline-start: 0;
    bottom: 0;
    will-change: inset-inline-start, inset-inline-end;
    animation: p-progressbar-indeterminate-anim-short 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) infinite;
    animation-delay: 1.15s;
}

@-webkit-keyframes p-progressbar-indeterminate-anim {
    0% {
        inset-inline-start: -35%;
        inset-inline-end: 100%;
    }
    60% {
        inset-inline-start: 100%;
        inset-inline-end: -90%;
    }
    100% {
        inset-inline-start: 100%;
        inset-inline-end: -90%;
    }
}
@keyframes p-progressbar-indeterminate-anim {
    0% {
        inset-inline-start: -35%;
        inset-inline-end: 100%;
    }
    60% {
        inset-inline-start: 100%;
        inset-inline-end: -90%;
    }
    100% {
        inset-inline-start: 100%;
        inset-inline-end: -90%;
    }
}
@-webkit-keyframes p-progressbar-indeterminate-anim-short {
    0% {
        inset-inline-start: -200%;
        inset-inline-end: 100%;
    }
    60% {
        inset-inline-start: 107%;
        inset-inline-end: -8%;
    }
    100% {
        inset-inline-start: 107%;
        inset-inline-end: -8%;
    }
}
@keyframes p-progressbar-indeterminate-anim-short {
    0% {
        inset-inline-start: -200%;
        inset-inline-end: 100%;
    }
    60% {
        inset-inline-start: 107%;
        inset-inline-end: -8%;
    }
    100% {
        inset-inline-start: 107%;
        inset-inline-end: -8%;
    }
}
`,Qp={root:({instance:t})=>["p-progressbar p-component",{"p-progressbar-determinate":t.determinate,"p-progressbar-indeterminate":t.indeterminate}],value:"p-progressbar-value",label:"p-progressbar-label"},Ds=(()=>{class t extends Ce{name="progressbar";theme=Wp;classes=Qp;static \u0275fac=(()=>{let e;return function(r){return(e||(e=j(t)))(r||t)}})();static \u0275prov=B({token:t,factory:t.\u0275fac})}return t})();var kr=(()=>{class t extends ue{value;showValue=!0;styleClass;valueStyleClass;style;unit="%";mode="determinate";color;contentTemplate;_componentStyle=y(Ds);templates;_contentTemplate;ngAfterContentInit(){this.templates?.forEach(e=>{switch(e.getType()){case"content":this._contentTemplate=e.template;break;default:this._contentTemplate=e.template}})}static \u0275fac=(()=>{let e;return function(r){return(e||(e=j(t)))(r||t)}})();static \u0275cmp=F({type:t,selectors:[["p-progressBar"],["p-progressbar"],["p-progress-bar"]],contentQueries:function(o,r,n){if(o&1&&(Z(n,Hp,4),Z(n,Ve,4)),o&2){let a;I(a=S())&&(r.contentTemplate=a.first),I(a=S())&&(r.templates=a)}},inputs:{value:[2,"value","value",G],showValue:[2,"showValue","showValue",T],styleClass:"styleClass",valueStyleClass:"valueStyleClass",style:"style",unit:"unit",mode:"mode",color:"color"},features:[ve([Ds]),ae],decls:3,vars:15,consts:[["role","progressbar",3,"ngStyle","ngClass"],["style","display:flex",3,"ngClass","class","width","background",4,"ngIf"],[3,"ngClass","class",4,"ngIf"],[2,"display","flex",3,"ngClass"],[1,"p-progressbar-label"],[3,"display",4,"ngIf"],[4,"ngTemplateOutlet","ngTemplateOutletContext"],[3,"ngClass"],[1,"p-progressbar-value","p-progressbar-value-animate"]],template:function(o,r){o&1&&(u(0,"div",0),f(1,jp,4,13,"div",1)(2,Kp,2,7,"div",2),p()),o&2&&(W(r.styleClass),s("ngStyle",r.style)("ngClass",J(12,zp,r.mode==="determinate",r.mode==="indeterminate")),k("aria-valuemin",0)("aria-valuenow",r.value)("aria-valuemax",100)("data-pc-name","progressbar")("data-pc-section","root")("aria-label",r.value+r.unit),d(),s("ngIf",r.mode==="determinate"),d(),s("ngIf",r.mode==="indeterminate"))},dependencies:[R,oe,fe,Ee,le,z],encapsulation:2,changeDetection:0})}return t})(),Vs=(()=>{class t{static \u0275fac=function(o){return new(o||t)};static \u0275mod=ne({type:t});static \u0275inj=ie({imports:[kr,z,z]})}return t})();function Zp(t,i){if(t&1&&(u(0,"div",0),w(1,"p-progressbar",1),p()),t&2){let e=l();d(),s("value",e.downloadService.progress())}}var Ao=class t{constructor(){this.downloadService=y(ln)}static{this.\u0275fac=function(e){return new(e||t)}}static{this.\u0275cmp=F({type:t,selectors:[["app-download-progress"]],decls:1,vars:1,consts:[[1,"fixed","bottom-5","right-5","w-[30rem]"],[3,"value"]],template:function(e,o){e&1&&f(0,Zp,2,1,"div",0),e&2&&Be(o.downloadService.showProgress()?0:-1)},dependencies:[Vs,kr],encapsulation:2})}};var Ro=class t{constructor(){this.title="Holybless";this.drawerService=y(nt);this.loadingService=y(yo);this.i18nService=y(Ge)}static{this.\u0275fac=function(e){return new(e||t)}}static{this.\u0275cmp=F({type:t,selectors:[["app-root"]],decls:13,vars:4,consts:[[1,"custom-loading",3,"blocked"],[1,"w-full","flex","flex-col","items-center","justify-center","h-screen"],[1,"pi","pi-spin","pi-spinner","text-[#fff]",2,"font-size","4rem"],[1,"mt-4","text-white"],[1,"app-container"],[1,"app-content"]],template:function(e,o){e&1&&(u(0,"p-blockUI",0),Le(1,"async"),u(2,"div",1),w(3,"i",2),u(4,"p",3),A(5),p()()(),u(6,"div",4),w(7,"app-navigation-menu"),u(8,"main",5),w(9,"router-outlet"),p()(),w(10,"app-setting-drawer")(11,"app-player-drawer")(12,"app-download-progress")),e&2&&(s("blocked",Pe(1,2,o.loadingService.loading$)),d(5),me("",o.i18nService.translate("loading"),"..."))},dependencies:[si,R,xt,Do,Po,Lo,Ms,yr,Bs,Ao],styles:['@charset "UTF-8";.app-container[_ngcontent-%COMP%]{min-height:100vh;display:flex;flex-direction:column;position:relative}.app-content[_ngcontent-%COMP%]{flex:1;display:flex}.app-container.drawer-open[_ngcontent-%COMP%]:before{content:"";position:fixed;inset:0;background-color:#0000004d;z-index:1000;pointer-events:auto}']})}};function Gp(t){let i=t;return 5}var Fs=["zh-Hans",[["\u4E0A\u5348","\u4E0B\u5348"],void 0,void 0],void 0,[["\u65E5","\u4E00","\u4E8C","\u4E09","\u56DB","\u4E94","\u516D"],["\u5468\u65E5","\u5468\u4E00","\u5468\u4E8C","\u5468\u4E09","\u5468\u56DB","\u5468\u4E94","\u5468\u516D"],["\u661F\u671F\u65E5","\u661F\u671F\u4E00","\u661F\u671F\u4E8C","\u661F\u671F\u4E09","\u661F\u671F\u56DB","\u661F\u671F\u4E94","\u661F\u671F\u516D"],["\u5468\u65E5","\u5468\u4E00","\u5468\u4E8C","\u5468\u4E09","\u5468\u56DB","\u5468\u4E94","\u5468\u516D"]],void 0,[["1","2","3","4","5","6","7","8","9","10","11","12"],["1\u6708","2\u6708","3\u6708","4\u6708","5\u6708","6\u6708","7\u6708","8\u6708","9\u6708","10\u6708","11\u6708","12\u6708"],["\u4E00\u6708","\u4E8C\u6708","\u4E09\u6708","\u56DB\u6708","\u4E94\u6708","\u516D\u6708","\u4E03\u6708","\u516B\u6708","\u4E5D\u6708","\u5341\u6708","\u5341\u4E00\u6708","\u5341\u4E8C\u6708"]],void 0,[["\u516C\u5143\u524D","\u516C\u5143"],void 0,void 0],0,[6,0],["y/M/d","y\u5E74M\u6708d\u65E5",void 0,"y\u5E74M\u6708d\u65E5EEEE"],["HH:mm","HH:mm:ss","z HH:mm:ss","zzzz HH:mm:ss"],["{1} {0}",void 0,void 0,void 0],[".",",",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","\xA4#,##0.00","#E0"],"CNY","\xA5","\u4EBA\u6C11\u5E01",{AUD:["AU$","$"],BYN:[void 0,"\u0440."],CNY:["\xA5"],ILR:["ILS"],JPY:["JP\xA5","\xA5"],KRW:["\uFFE6","\u20A9"],PHP:[void 0,"\u20B1"],RUR:[void 0,"\u0440."],TWD:["NT$"],USD:["US$","$"],XXX:[]},"ltr",Gp];ct(Mo,"zh");ct(Fs,"zh-Hans");ct(Oo,"en");ti(Ro,ze(se({},fr),{providers:[...fr.providers??[],{provide:Xr,useValue:"zh-Hans"}]})).catch(t=>console.error(t));
