import{p}from"./chunk-7BEB76UR.js";import{_ as a,da as i}from"./chunk-6TTFYGC3.js";import{a as r}from"./chunk-QWWW7GFA.js";var s=class o{constructor(e){this.restService=e;this.apiName="Default";this.getChapterTreeByEBookId=(e,t)=>this.restService.request({method:"GET",url:`/api/app/read-only-eBook/chapter-tree-by-eBook-id/${e}`},r({apiName:this.apiName},t));this.getEBooksByChannelId=(e,t)=>this.restService.request({method:"GET",url:`/api/app/read-only-eBook/e-books-by-channel-id/${e}`},r({apiName:this.apiName},t))}static{this.\u0275fac=function(t){return new(t||o)(i(p))}}static{this.\u0275prov=a({token:o,factory:o.\u0275fac,providedIn:"root"})}};export{s as a};
