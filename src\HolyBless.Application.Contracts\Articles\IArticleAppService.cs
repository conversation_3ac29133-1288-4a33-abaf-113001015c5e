using System.Collections.Generic;
using System.Threading.Tasks;
using HolyBless.Articles.Dtos;
using HolyBless.Tags.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace HolyBless.Articles
{
    public interface IArticleAppService : IReadOnlyArticleAppService, ICrudAppService< // Defines CRUD methods
            ArticleDto, // Used to show bucket files
            int, // Primary key of the bucket file entity
            ArticleAdminSearchDto, // Used for paging/sorting
            CreateUpdateArticleDto> // Used to create/update a bucket file
    {
        Task<List<ArticleToTagDto>> AddTagsAsync(int articleId, List<int> tagIds);

        Task<List<TagDto>> GetTagsAsync(int articleId);

        Task AddTeacherArticleLinks(int studentArticleId, List<TeacherArticleLinkDto> teacherLinks);
        Task LinkToCollection(int collectionId, int articleId);

        Task LinkToCollection(int collectionId, List<int> articleIds);

        Task UnlinkFromCollection(int collectionId, int articleId);

        Task UnlinkFromCollection(int collectionId, List<int> articleIds);
    }
}