using HolyBless.Books.Dtos;
using HolyBless.Entities.Books;
using HolyBless.Enums;
using HolyBless.Permissions;
using HolyBless.TreeJsonSnapshots;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using static HolyBless.Permissions.HolyBlessPermissions;

namespace HolyBless.Books;

public class ChapterAppService : ApplicationService, IChapterAppService
{
    private readonly IRepository<Chapter, int> _chapterRepository;
    private readonly IRepository<ChapterToArticle> _chapterToArticleRepository;
    private readonly ITreeJsonSnapshotAppService _treeSnapshotAppService;

    private static readonly System.Text.Json.JsonSerializerOptions JsonOptions = new()
    {
        PropertyNameCaseInsensitive = true
    };

    public ChapterAppService(
        IRepository<Chapter, int> chapterRepository,
        IRepository<ChapterToArticle> chapterToArticleRepository,
        ITreeJsonSnapshotAppService treeSnapshotAppService)
    {
        _chapterRepository = chapterRepository;
        _chapterToArticleRepository = chapterToArticleRepository;
        _treeSnapshotAppService = treeSnapshotAppService;
    }

    public async Task<ChapterDto> GetAsync(int id)
    {
        var chapter = await _chapterRepository.GetAsync(id);
        return ObjectMapper.Map<Chapter, ChapterDto>(chapter);
    }

    public async Task<PagedResultDto<ChapterDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        var queryable = await _chapterRepository.GetQueryableAsync();
        var query = queryable
            .OrderBy(c => c.Weight)
            .ThenBy(c => c.Title)
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount);

        var chapters = await AsyncExecuter.ToListAsync(query);
        var totalCount = await AsyncExecuter.CountAsync(queryable);

        return new PagedResultDto<ChapterDto>(
            totalCount,
            ObjectMapper.Map<List<Chapter>, List<ChapterDto>>(chapters)
        );
    }

    public async Task<ChapterDto> CreateAsync(CreateUpdateChapterDto input)
    {
        var chapter = ObjectMapper.Map<CreateUpdateChapterDto, Chapter>(input);
        await _chapterRepository.InsertAsync(chapter);
        await _treeSnapshotAppService.ClearCacheAsync(TreeType.EBook, input.EBookId);
        return ObjectMapper.Map<Chapter, ChapterDto>(chapter);
    }

    public async Task<ChapterDto> UpdateAsync(int id, CreateUpdateChapterDto input)
    {
        var chapter = await _chapterRepository.GetAsync(id);
        ObjectMapper.Map(input, chapter);
        await _chapterRepository.UpdateAsync(chapter);
        await _treeSnapshotAppService.ClearCacheAsync(TreeType.EBook, input.EBookId);
        return ObjectMapper.Map<Chapter, ChapterDto>(chapter);
    }

    public async Task DeleteAsync(int id)
    {
        var chapter = await _chapterRepository.GetAsync(id);
        await _chapterRepository.DeleteAsync(id);
        await _treeSnapshotAppService.ClearCacheAsync(TreeType.EBook, chapter.EBookId);
    }

    public async Task<List<ChapterDto>> GetChaptersByEBookIdAsync(int eBookId)
    {
        var queryable = await _chapterRepository.GetQueryableAsync();
        var chapters = await queryable
            .Where(c => c.EBookId == eBookId)
            .OrderBy(c => c.Weight)
            .ThenBy(c => c.Title)
            .ToListAsync();

        return ObjectMapper.Map<List<Chapter>, List<ChapterDto>>(chapters);
    }

    public async Task<List<ChapterDto>> GetChildChaptersAsync(int parentChapterId)
    {
        var queryable = await _chapterRepository.GetQueryableAsync();
        var chapters = await queryable
            .Where(c => c.ParentChapterId == parentChapterId)
            .OrderBy(c => c.Weight)
            .ThenBy(c => c.Title)
            .ToListAsync();

        return ObjectMapper.Map<List<Chapter>, List<ChapterDto>>(chapters);
    }

    public async Task<List<ChapterToArticleDto>> GetChapterArticlesAsync(int chapterId)
    {
        var queryable = await _chapterToArticleRepository.GetQueryableAsync();
        var chapterArticles = await queryable
            .Include(ca => ca.Chapter)
            .Include(ca => ca.Article)
            .Where(ca => ca.ChapterId == chapterId)
            .OrderBy(ca => ca.Weight)
            .ToListAsync();

        return ObjectMapper.Map<List<ChapterToArticle>, List<ChapterToArticleDto>>(chapterArticles);
    }

    public async Task<ChapterToArticleDto> AddArticleToChapterAsync(CreateUpdateChapterToArticleDto input)
    {
        var chapterToArticle = ObjectMapper.Map<CreateUpdateChapterToArticleDto, ChapterToArticle>(input);
        await _chapterToArticleRepository.InsertAsync(chapterToArticle);

        // Load the related entities for the response
        var queryable = await _chapterToArticleRepository.GetQueryableAsync();
        var result = await queryable
            .Include(ca => ca.Chapter)
            .Include(ca => ca.Article)
            .FirstAsync(ca => ca.ChapterId == input.ChapterId && ca.ArticleId == input.ArticleId);

        return ObjectMapper.Map<ChapterToArticle, ChapterToArticleDto>(result);
    }

    public async Task RemoveArticleFromChapterAsync(int chapterId, int articleId)
    {
        var chapterToArticle = await _chapterToArticleRepository.FirstOrDefaultAsync(
            ca => ca.ChapterId == chapterId && ca.ArticleId == articleId);

        if (chapterToArticle != null)
        {
            await _chapterToArticleRepository.DeleteAsync(chapterToArticle);
        }
    }

    /// <summary>
    /// Get the chapter tree structure for a specific eBook with caching.
    /// Usage: Ebook Detail Page, render left side chapter tree
    /// </summary>
    /// <param name="eBookId"></param>
    /// <returns></returns>
    public async Task<List<ChapterTreeDto>> GetChapterTreeByEBookIdAsync(int eBookId)
    {
        // Try to read cached snapshot first
        var cachedResult = await TryGetCachedChapterTreeAsync(eBookId);
        if (cachedResult != null && cachedResult.Count > 0)
        {
            return cachedResult;
        }

        // Build from database
        var chapterQueryable = await _chapterRepository.GetQueryableAsync();
        var chapters = await chapterQueryable
            .Where(c => c.EBookId == eBookId)
            .OrderBy(c => c.Weight)
            .ThenBy(c => c.Title)
            .ToListAsync();

        // Get article counts for each chapter
        var chapterIds = chapters.Select(c => c.Id).ToList();
        var articleQueryable = await _chapterToArticleRepository.GetQueryableAsync();
        var articleCounts = await articleQueryable
            .Where(ca => chapterIds.Contains(ca.ChapterId))
            .GroupBy(ca => ca.ChapterId)
            .Select(g => new { ChapterId = g.Key, Count = g.Count() })
            .ToListAsync();

        var articleCountDict = articleCounts.ToDictionary(ac => ac.ChapterId, ac => ac.Count);

        // Map to DTOs
        var chapterDtos = chapters.Select(c => new ChapterTreeDto
        {
            Id = c.Id,
            EBookId = c.EBookId,
            ParentChapterId = c.ParentChapterId,
            Title = c.Title,
            Content = c.Content,
            Weight = c.Weight,
            Views = c.Views,
            IsRoot = c.ParentChapterId == null,
            ArticleCount = articleCountDict.GetValueOrDefault(c.Id, 0),
            Children = new List<ChapterTreeDto>()
        }).ToList();

        // Build the tree structure
        var rootChapters = chapterDtos.Where(c => c.ParentChapterId == null).ToList();
        var childChapters = chapterDtos.Where(c => c.ParentChapterId != null).ToList();

        BuildChapterTree(rootChapters, childChapters);

        // Update snapshot after reading from database
        await UpdateChapterTreeSnapshotAsync(eBookId, rootChapters);

        return rootChapters;
    }

    private void BuildChapterTree(List<ChapterTreeDto> parentChapters, List<ChapterTreeDto> allChildChapters)
    {
        foreach (var parent in parentChapters)
        {
            var children = allChildChapters.Where(c => c.ParentChapterId == parent.Id).ToList();
            parent.Children = children;

            if (children.Any())
            {
                BuildChapterTree(children, allChildChapters);
            }
        }
    }

    /// <summary>
    /// Try to get cached chapter tree from snapshot.
    /// </summary>
    /// <param name="eBookId">The eBook ID used as root</param>
    /// <returns>Cached chapter tree or null if not available</returns>
    private async Task<List<ChapterTreeDto>?> TryGetCachedChapterTreeAsync(int eBookId)
    {
        try
        {
            var json = await _treeSnapshotAppService.GetTreeJsonAsync(TreeType.EBook, eBookId);
            if (!string.IsNullOrWhiteSpace(json))
            {
                var cached = System.Text.Json.JsonSerializer.Deserialize<List<ChapterTreeDto>>(json, JsonOptions);
                return cached;
            }
        }
        catch
        {
            // ignore and return null to build from DB
        }

        return null;
    }

    /// <summary>
    /// Update the chapter tree snapshot with the latest data.
    /// </summary>
    /// <param name="eBookId">The eBook ID used as root</param>
    /// <param name="chapterTree">The chapter tree data to cache</param>
    private async Task UpdateChapterTreeSnapshotAsync(int eBookId, List<ChapterTreeDto> chapterTree)
    {
        try
        {
            var json = System.Text.Json.JsonSerializer.Serialize(chapterTree);
            await _treeSnapshotAppService.UpdateTreeJsonAsync(TreeType.EBook, eBookId, json);
        }
        catch
        {
            // ignore snapshot update failures
        }
    }

    /// <summary>
    /// Move a chapter to a new parent and reorder by weight relative to a beforeId.
    /// </summary>
    /// <param name="chapterId">The chapter being moved</param>
    /// <param name="toParentId">The new parent chapter id (nullable for root)</param>
    /// <param name="beforeId">The chapter that the moved chapter should be placed before. If null, places at the end.</param>
    public async Task MoveChapterAsync(int chapterId, int? toParentId, int? beforeId)
    {
        var chapterEntity = await _chapterRepository.GetAsync(chapterId);
        var eBookId = chapterEntity.EBookId;
        
        var tree = await GetChapterTreeByEBookIdAsync(eBookId);
        var allChapters = tree.Flatten(new List<ChapterTreeDto>());

        var chapter = allChapters.FirstOrDefault(c => c.Id == chapterId);
        var toParent = toParentId.HasValue ? allChapters.FirstOrDefault(c => c.Id == toParentId.Value) : null;

        if (chapter == null)
        {
            throw new UserFriendlyException(L["ChapterNotFound"]);
        }

        // Validation: Cannot move under chapter's own child chapter (deep check)
        if (toParent != null)
        {
            var descendantIds = new HashSet<int>(chapter.GetDescendantIds());
            if (descendantIds.Contains(toParent.Id))
            {
                throw new UserFriendlyException(L["CannotMoveChapterUnderChildChapter"]);
            }
        }

        // Update ParentId logic
        chapterEntity.ParentChapterId = toParentId;

        // Update Weight logic
        var queryable = await _chapterRepository.GetQueryableAsync();
        
        if (beforeId.HasValue)
        {
            // Normal case: insert before a specific chapter
            var beforeEntity = await _chapterRepository.GetAsync(beforeId.Value);
            var insertWeight = beforeEntity.Weight;

            // Increase weight by +1 for siblings under the same parent of BeforeId with weight >= BeforeId's weight
            var siblingsToShift = await AsyncExecuter.ToListAsync(
                queryable.Where(x => x.ParentChapterId == beforeEntity.ParentChapterId
                    && x.Id != chapterId // Exclude the chapter being moved
                    && x.Weight >= insertWeight)
            );

            foreach (var s in siblingsToShift)
            {
                s.Weight += 1;
                await _chapterRepository.UpdateAsync(s, true);
            }

            // Set the moved chapter's weight to the original weight of the 'before' chapter
            chapterEntity.Weight = insertWeight;
        }
        else
        {
            // beforeId is null: move to the last position
            // Find the highest weight among siblings under the target parent
            var siblings = await AsyncExecuter.ToListAsync(
                queryable.Where(x => x.ParentChapterId == toParentId
                    && x.EBookId == eBookId
                    && x.Id != chapterId) // Exclude the chapter being moved
            );

            var maxWeight = siblings.Any() ? siblings.Max(x => x.Weight) : 0;
            chapterEntity.Weight = maxWeight + 1;
        }

        await _chapterRepository.UpdateAsync(chapterEntity, true);

        // Clear chapter tree cache so next read rebuilds the snapshot
        await _treeSnapshotAppService.ClearCacheAsync(TreeType.EBook, eBookId);
    }
}