import{p as l}from"./chunk-7BEB76UR.js";import{_ as a,da as i}from"./chunk-6TTFYGC3.js";import{a as t}from"./chunk-QWWW7GFA.js";var s=class o{constructor(e){this.restService=e;this.apiName="Default";this.getAllFolderFileUrlsByFolderId=(e,r)=>this.restService.request({method:"GET",url:`/api/app/read-only-virtual-folder/folder-file-urls/${e}`},t({apiName:this.apiName},r));this.getAllFolderFilesByFolderId=(e,r)=>this.restService.request({method:"GET",url:`/api/app/read-only-virtual-folder/folder-files/${e}`},t({apiName:this.apiName},r));this.getFolderFiles=(e,r)=>this.restService.request({method:"GET",url:"/api/app/read-only-virtual-folder/folder-files",params:{folderId:e.folderId,sorting:e.sorting,skipCount:e.skipCount,maxResultCount:e.maxResultCount}},t({apiName:this.apiName},r));this.getList=(e,r)=>this.restService.request({method:"GET",url:"/api/app/read-only-virtual-folder",params:{channelId:e.channelId,sorting:e.sorting,skipCount:e.skipCount,maxResultCount:e.maxResultCount}},t({apiName:this.apiName},r));this.getVirtualFolderTree=(e,r)=>this.restService.request({method:"GET",url:`/api/app/read-only-virtual-folder/virtual-folder-tree/${e}`},t({apiName:this.apiName},r))}static{this.\u0275fac=function(r){return new(r||o)(i(l))}}static{this.\u0275prov=a({token:o,factory:o.\u0275fac,providedIn:"root"})}};export{s as a};
