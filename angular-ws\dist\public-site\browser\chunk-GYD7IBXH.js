import{a as j}from"./chunk-Y47MM6IF.js";import{a as $}from"./chunk-HVFBHGMG.js";import{a as B}from"./chunk-IQKOEMCW.js";import{a as x}from"./chunk-KMQZ7S6W.js";import{A as O,z as k}from"./chunk-7BEB76UR.js";import{b as I}from"./chunk-HMYHKKL3.js";import{c as T,d as D}from"./chunk-C42ROIQ5.js";import"./chunk-EO3QMPVM.js";import"./chunk-GXIFCJEC.js";import"./chunk-TRW3QJR4.js";import"./chunk-BMA7WWEI.js";import{E as L,H as P,N as M,h as V,k as R}from"./chunk-O4TW5EFJ.js";import{Db as _,Eb as y,Fb as a,Gb as l,Hb as p,Lb as w,Qb as f,Rb as F,Ua as b,Za as d,a as m,ea as r,ib as S,la as C,ma as v,qc as g,rc as h,wb as u,za as s}from"./chunk-6TTFYGC3.js";import"./chunk-QWWW7GFA.js";var H=(n,e)=>e.id;function N(n,e){if(n&1){let i=w();a(0,"div",5),f("click",function(){let o=C(i).$implicit,c=F();return v(c.navigateToFolderDetail(o.id))}),a(1,"p",6),p(2,"img",7),l(),p(3,"p",8),l()}if(n&2){let i=e.$implicit;d(3),u("innerHTML",i.folderName,b)}}var E=class n{constructor(){this.#e=r(j);this.#i=r(x);this.mobileService=r($);this.router=r(P);this.route=r(L);this.i18nService=r(k);this.channelIdContentCodeService=r(B);this.subs=new m;this.loadingService=r(O);this.first=s(0);this.rows=s(10);this.totalRecords=s(0);this.items=s([]);this.channelId=void 0}#e;#i;ngOnInit(){this.route.params.subscribe(e=>{let i=e.channelId;this.channelId=i,i&&(this.changeLanguage(i),this.loadVirtualFolders())})}changeLanguage(e){this.subs.unsubscribe(),this.subs=new m;let i=this.i18nService.language$.subscribe(()=>{let t=this.channelIdContentCodeService.getChannelIdContentCode(e);t&&this.#i.getMatchedChannelByContentCode(t).subscribe({next:o=>{o.id!==e&&this.router.navigateByUrl(`/virtual-folder/list/${o.id}`)}})});this.subs.add(i)}loadVirtualFolders(){this.channelId&&(this.loadingService.show(),this.#e.getList({channelId:this.channelId,skipCount:this.first(),maxResultCount:this.rows()}).subscribe({next:e=>{this.items.set(e.items||[]),this.totalRecords.set(e.totalCount||0),this.loadingService.hide()},error:e=>{console.error("\u83B7\u53D6\u865A\u62DF\u6587\u4EF6\u5939\u5217\u8868\u5931\u8D25:",e),this.loadingService.hide()}}))}onPageChange(e){this.first.set(e.first),this.rows.set(e.rows),this.loadVirtualFolders()}navigateToFolderDetail(e){e&&this.router.navigateByUrl(`/virtual-folder/folder-detail/${e}`)}ngOnDestroy(){this.subs.unsubscribe()}static{this.\u0275fac=function(i){return new(i||n)}}static{this.\u0275cmp=S({type:n,selectors:[["app-virtual-folder-list"]],decls:8,vars:9,consts:[[1,"p-6"],[1,"grid","grid-cols-2","md:grid-cols-3","lg:grid-cols-5","gap-6","mb-8"],[1,"cursor-pointer"],[1,"pagination-container"],["styleClass","custom-paginator",3,"onPageChange","first","rows","totalRecords","showPageLinks","showCurrentPageReport"],[1,"cursor-pointer",3,"click"],[1,"flex","justify-center","mb-4"],["src","assets/images/folder.png","alt",""],[1,"flex","justify-center",3,"innerHTML"]],template:function(i,t){i&1&&(a(0,"div",0)(1,"div",1),_(2,N,4,1,"div",2,H),l(),a(4,"div",3)(5,"p-paginator",4),g(6,"async"),g(7,"async"),f("onPageChange",function(c){return t.onPageChange(c)}),l()()()),i&2&&(d(2),y(t.items()),d(3),u("first",t.first())("rows",t.rows())("totalRecords",t.totalRecords())("showPageLinks",!h(6,5,t.mobileService.isMobile))("showCurrentPageReport",h(7,7,t.mobileService.isMobile)))},dependencies:[R,V,M,I,D,T],styles:["[_nghost-%COMP%]{flex:1}"]})}};export{E as VirtualFolderListComponent};
