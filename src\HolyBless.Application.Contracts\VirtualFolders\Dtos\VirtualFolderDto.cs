using Volo.Abp.Application.Dtos;

namespace HolyBless.VirtualFolders.Dtos
{
    public class VirtualFolderDto : AuditedEntityDto<int>
    {
        public int? ParentFolderId { get; set; }
        public string FolderName { get; set; } = default!;
        public string? LanguageCode { get; set; }
        public string? SpokenLangCode { get; set; }
        public int Views { get; set; } = 0;
        public int Weight { get; set; } = 0;
        public int? ChannelId { get; set; }
    }
}