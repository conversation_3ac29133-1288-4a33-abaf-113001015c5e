using HolyBless.Albums.Dtos;
using HolyBless.Buckets;
using HolyBless.Entities.Albums;
using HolyBless.Entities.Buckets;
using HolyBless.Entities.Channels;
using HolyBless.Permissions;
using HolyBless.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.Albums
{
    [Authorize(HolyBlessPermissions.Albums.Default)]
    public class AlbumAppService : ReadOnlyAlbumAppService, IAlbumAppService
    {
        public AlbumAppService(
            IRepository<Album, int> albumRepository,
            IRepository<AlbumToFile> albumToFileRepository,
            IRepository<BucketFile, int> bucketFileRepository,
            IRepository<Channel, int> channelRepository,
            IRequestContextService requestContextService,
            ICachedFileUrlAppService cachedFileUrlAppService
            )
            : base(albumRepository, albumToFileRepository, bucketFileRepository, channelRepository, requestContextService, cachedFileUrlAppService)
        {
        }

        [Authorize(HolyBlessPermissions.Albums.Create)]
        public async Task<AlbumDto> CreateAsync(CreateUpdateAlbumDto input)
        {
            var album = ObjectMapper.Map<CreateUpdateAlbumDto, Album>(input);
            album = await _albumRepository.InsertAsync(album, autoSave: true);
            return ObjectMapper.Map<Album, AlbumDto>(album);
        }

        [Authorize(HolyBlessPermissions.Albums.Edit)]
        public async Task<AlbumDto> UpdateAsync(int id, CreateUpdateAlbumDto input)
        {
            var album = await _albumRepository.GetAsync(id);
            ObjectMapper.Map(input, album);
            album = await _albumRepository.UpdateAsync(album, autoSave: true);
            return ObjectMapper.Map<Album, AlbumDto>(album);
        }

        [Authorize(HolyBlessPermissions.Albums.Delete)]
        public async Task DeleteAsync(int id)
        {
            await _albumRepository.DeleteAsync(id);
        }

        [Authorize(HolyBlessPermissions.Albums.Edit)]
        public async Task<List<AlbumFileDto>> AddFilesToAlbumAsync(int albumId, List<CreateUpdateAlbumToFileDto> files)
        {
            // Verify album exists
            _ = await _albumRepository.GetAsync(albumId);

            var albumToFiles = new List<AlbumToFile>();

            foreach (var fileDto in files)
            {
                // Check if file already exists in album
                var existingAlbumFile = await _albumToFileRepository.FirstOrDefaultAsync(
                    af => af.AlbumId == albumId && af.FileId == fileDto.FileId);

                if (existingAlbumFile == null)
                {
                    // Verify bucket file exists
                    _ = await _bucketFileRepository.GetAsync(fileDto.FileId);

                    var albumToFile = new AlbumToFile
                    {
                        AlbumId = albumId,
                        FileId = fileDto.FileId,
                        Title = fileDto.Title,
                        Weight = fileDto.Weight
                    };

                    albumToFiles.Add(await _albumToFileRepository.InsertAsync(albumToFile));
                }
            }

            return ObjectMapper.Map<List<AlbumToFile>, List<AlbumFileDto>>(albumToFiles);
        }

        [Authorize(HolyBlessPermissions.Albums.Edit)]
        public async Task RemoveFileFromAlbumAsync(int albumId, int fileId)
        {
            var albumToFile = await _albumToFileRepository.FirstOrDefaultAsync(
                af => af.AlbumId == albumId && af.FileId == fileId);

            if (albumToFile != null)
            {
                await _albumToFileRepository.DeleteAsync(albumToFile);
            }
        }

        [Authorize(HolyBlessPermissions.Albums.Edit)]
        public async Task<AlbumFileDto> UpdateAlbumFileAsync(int albumId, int fileId, CreateUpdateAlbumToFileDto input)
        {
            var albumToFile = await _albumToFileRepository.FirstOrDefaultAsync(
                af => af.AlbumId == albumId && af.FileId == fileId);

            if (albumToFile == null)
            {
                throw new EntityNotFoundException(typeof(AlbumToFile), $"AlbumId: {albumId}, FileId: {fileId}");
            }

            ObjectMapper.Map(input, albumToFile);
            albumToFile = await _albumToFileRepository.UpdateAsync(albumToFile, autoSave: true);

            return ObjectMapper.Map<AlbumToFile, AlbumFileDto>(albumToFile);
        }

        [Authorize(HolyBlessPermissions.Albums.Edit)]
        public async Task ReorderAlbumFilesAsync(int albumId, List<int> fileIds)
        {
            var albumFiles = await _albumToFileRepository.GetListAsync(
                af => af.AlbumId == albumId && fileIds.Contains(af.FileId));

            for (int i = 0; i < fileIds.Count; i++)
            {
                var albumFile = albumFiles.FirstOrDefault(af => af.FileId == fileIds[i]);
                if (albumFile != null)
                {
                    albumFile.Weight = i;
                    await _albumToFileRepository.UpdateAsync(albumFile);
                }
            }
        }

        /// <summary>
        /// Usage Admin page: Link an album to a channel.
        /// </summary>
        /// <param name="channelId"></param>
        /// <param name="albumId"></param>
        /// <returns></returns>
        //[Authorize(HolyBlessPermissions.Albums.Default)]
        public async Task LinkToChannel(int channelId, int albumId)
        {
            var album = await _albumRepository.GetAsync(albumId);
            album.ChannelId = channelId;
            await _albumRepository.UpdateAsync(album, true);
        }

        /// <summary>
        /// Usage Admin page: Link multiple albums to a channel.
        /// </summary>
        /// <param name="channelId"></param>
        /// <param name="albumIds"></param>
        /// <returns></returns>
        //[Authorize(HolyBlessPermissions.Albums.Default)]
        public async Task LinkToChannel(int channelId, List<int> albumIds)
        {
            var albums = await _albumRepository.GetListAsync(x => albumIds.Contains(x.Id));
            foreach (var album in albums)
            {
                album.ChannelId = channelId;
            }
            await _albumRepository.UpdateManyAsync(albums, autoSave: true);
        }

        /// <summary>
        /// Admin page: Unlink an album from a channel (Set ChannelId to null)
        /// </summary>
        /// <param name="albumId"></param>
        /// <returns></returns>
        public async Task UnlinkFromChannel(int albumId)
        {
            var album = await _albumRepository.GetAsync(albumId);
            album.ChannelId = null;
            await _albumRepository.UpdateAsync(album, true);
        }

        /// <summary>
        /// Admin page: Unlink multiple albums from their channels (Set ChannelId to null)
        /// </summary>
        /// <param name="albumIds"></param>
        /// <returns></returns>
        public async Task UnlinkFromChannel(List<int> albumIds)
        {
            var albums = await _albumRepository.GetListAsync(x => albumIds.Contains(x.Id));
            foreach (var album in albums)
            {
                album.ChannelId = null;
            }
            await _albumRepository.UpdateManyAsync(albums, autoSave: true);
        }

        /// <summary>
        /// [Admin]
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        /// <exception cref="EntityNotFoundException"></exception>
        public async Task<AlbumDto> GetAsync(int id)
        {
            var queryable = await _albumRepository.GetQueryableAsync();
            var album = await queryable
                .FirstOrDefaultAsync(x => x.Id == id);

            if (album == null)
            {
                throw new EntityNotFoundException(typeof(Album), id);
            }

            var rt = ObjectMapper.Map<Album, AlbumDto>(album);
            await FillThumbnailUrl(rt);
            return rt;
        }

        /// <summary>
        /// [Admin] Get all albums, optionally filtered by language code.
        /// </summary>
        /// <param name="languageCode">Optional language code to filter albums. If null, returns albums from all languages.</param>
        /// <returns>List of all albums matching the language criteria</returns>
        public async Task<List<AlbumDto>> GetAllAlbumsAsync(string? languageCode = null)
        {
            var queryable = await _albumRepository.GetQueryableAsync();
            var query = queryable
                .WhereIf(!string.IsNullOrEmpty(languageCode), x => x.LanguageCode != null && x.LanguageCode.ToLower() == languageCode!.ToLower())
                .OrderBy(x => x.Weight)
                ;

            var albums = await AsyncExecuter.ToListAsync(query);
            return ObjectMapper.Map<List<Album>, List<AlbumDto>>(albums);
        }

        /// <summary>
        /// [Admin] Get all albums for a specific channel.
        /// </summary>
        /// <param name="channelId">The channel ID to filter albums.</param>
        /// <returns>List of albums belonging to the channel.</returns>
        public async Task<List<AlbumDto>> GetAlbumsByChannelAsync(int channelId)
        {
            var queryable = await _albumRepository.GetQueryableAsync();
            var query = queryable
                .Where(x => x.ChannelId == channelId)
                .OrderBy(x => x.Weight);

            var albums = await AsyncExecuter.ToListAsync(query);
            return ObjectMapper.Map<List<Album>, List<AlbumDto>>(albums);
        }
    }
}