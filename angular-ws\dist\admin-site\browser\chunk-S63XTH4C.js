import{a as se}from"./chunk-DRIYSUPZ.js";import{a as oe}from"./chunk-KDWN5JK3.js";import{a as ie,b as le}from"./chunk-4ZKPBQ3G.js";import{a as I}from"./chunk-NOX4GDMW.js";import{a as Z,b as te}from"./chunk-IWTIDBZ7.js";import{a as ae}from"./chunk-RCAKOILJ.js";import{a as re}from"./chunk-DZLXCZWK.js";import{a as ee}from"./chunk-SPDIY6EA.js";import{h as Y}from"./chunk-JBJGPYJN.js";import"./chunk-E6VTCYRR.js";import{a as J,b as K,c as ne}from"./chunk-W4NOLTEH.js";import{A as q,B as G,C as Q,D as X,p as O,q as z,r as N,s as H,t as R,u as j,y as U}from"./chunk-NSHV5XN7.js";import{C as A,E as F,F as L,H as P,gb as $}from"./chunk-GXYB24H2.js";import{$a as p,$b as B,Bb as x,Ca as m,Fb as a,Gb as d,Lb as w,Qb as y,Rb as f,Zb as g,a as T,b as E,bc as C,cc as u,dc as _,fc as v,ga as b,jb as k,na as r,oa as s,pb as S,rc as W,wb as c,zb as V}from"./chunk-AKB6GSCA.js";var M=(n=>(n[n.Unspefied=0]="Unspefied",n[n.Collection=1]="Collection",n[n.Ebook=2]="Ebook",n[n.VirtualDisk=3]="VirtualDisk",n[n.PodCast=4]="PodCast",n))(M||{}),D=$(M);var pe=()=>({width:"30rem"}),me=()=>({width:"25rem"});function ce(h,l){if(h&1){let t=w();a(0,"div",14)(1,"p-checkbox",15),_("ngModelChange",function(i){r(t);let n=f();return u(n.selectSub,i)||(n.selectSub=i),s(i)}),d(),a(2,"label",16),g(3),d()()}if(h&2){let t=l.$implicit,e=f();p(),c("inputId",t.id+"")("value",t.id),C("ngModel",e.selectSub),p(),c("for",t.id+""),p(),B(" ",t.name||t.title," ")}}function he(h,l){if(h&1){let t=w();a(0,"div",10)(1,"div",17)(2,"label"),g(3,"Title"),d(),a(4,"input",18),_("ngModelChange",function(i){r(t);let n=f();return u(n.selectChannel().name,i)||(n.selectChannel().name=i),s(i)}),d()(),a(5,"div",17)(6,"label"),g(7,"Content code"),d(),a(8,"input",18),_("ngModelChange",function(i){r(t);let n=f();return u(n.selectChannel().contentCode,i)||(n.selectChannel().contentCode=i),s(i)}),d()(),a(9,"div",17)(10,"label"),g(11,"Channel source"),d(),a(12,"p-select",19),_("ngModelChange",function(i){r(t);let n=f();return u(n.selectChannel().channelSource,i)||(n.selectChannel().channelSource=i),s(i)}),d()()()}if(h&2){let t=f();p(4),C("ngModel",t.selectChannel().name),p(4),C("ngModel",t.selectChannel().contentCode),p(4),c("options",t.channelSourceOptions),C("ngModel",t.selectChannel().channelSource)}}var de=class h{constructor(){this.#e=b(ee);this.#t=b(oe);this.#n=b(ae);this.#i=b(se);this.#l=b(re);this.langOptions=[{label:"zh-Hans"},{label:"zh-Hant"},{label:"en"}];this.lang=m("zh-Hans");this.cols=[{field:"id",header:"ID"},{field:"name",header:"Title"},{field:"contentCode",header:"Content Code"},{field:"channelSource",header:"Channel Source",type:"select",selectData:D}];this.data=m([]);this.drawerVisible=m(!1);this.openChannel=m(null);this.subData=m([]);this.selectSub=m([]);this.channelSourceOptions=D;this.selectChannel=m(null);this.mode=m("create")}#e;#t;#n;#i;#l;ngOnInit(){this.loadData()}loadData(){this.#e.getAllChannels(this.lang()).subscribe({next:l=>{this.data.set(l)}})}handleDrop({channelId:l,toParentId:t,beforeId:e}){this.#e.moveChannel(l,t,e).subscribe({next:i=>{this.loadData()}})}handleEdit(l){this.selectChannel.set(ne(l)),this.mode.set("edit")}handleCreate(){this.selectChannel.set({}),this.mode.set("create")}handleAdd(l){this.drawerVisible.set(!0),this.openChannel.set(l);let t=null;l.id===1?t=this.#t.getFirstByChannelId(l.id):l.id===2?t=this.#n.getEBooksByChannelId(l.id):(l.id,3),t&&t.subscribe({next:e=>{e&&(Array.isArray(e)?this.subData.set(e):this.subData.set([e]))}})}handleRemove(){let l=null,t=this.openChannel();t.id===1?l=this.#t.unlinkFromChannelByCollectionIds(this.selectSub()):t.id===2?l=this.#n.unlinkFromChannelByEBookIds(this.selectSub()):t.id===3?l=this.#i.unlinkFromChannelByVirtualFolderIds(this.selectSub()):l=this.#l.unlinkFromChannelByAlbumIds(this.selectSub()),l.subscribe({next:e=>{this.drawerVisible.set(!1),this.selectSub.set([]),this.loadData()}})}updateSelectChannel(){this.mode()==="edit"?this.#e.update(this.selectChannel().id,this.selectChannel()).subscribe({next:l=>{this.loadData(),this.selectChannel.set(null)}}):this.#e.create(E(T({},this.selectChannel()),{parentChannelId:null,languageCode:"zh-Hans",weight:1})).subscribe({next:l=>{this.loadData(),this.selectChannel.set(null)}})}handleDelete(l){this.#e.delete(l.id).subscribe({next:t=>{this.loadData()}})}static{this.\u0275fac=function(t){return new(t||h)}}static{this.\u0275cmp=k({type:h,selectors:[["app-channel"]],decls:17,vars:18,consts:[["item",""],[1,"p-4"],[1,"flex","justify-between"],["optionLabel","label","optionValue","label",3,"ngModelChange","onChange","options","ngModel"],["label","Create",3,"onClick"],[3,"onDrop","onEdit","onDelete","onAdd","data","cols"],["position","right",3,"visibleChange","visible"],["icon","pi pi-trash","label","Remove","severity","danger",3,"onClick","text"],["styleClass","h-[calc(100vh-10rem)]",3,"items","itemSize"],[3,"visibleChange","header","modal","visible"],[1,"form"],[1,"w-full","grid","grid-cols-2","gap-2","mt-4"],["styleClass","w-full","severity","secondary"],["styleClass","w-full",3,"onClick"],[1,"p-2"],["name","group",3,"ngModelChange","inputId","value","ngModel"],[1,"ml-2",3,"for"],[1,"form-item"],["type","text","pInputText","",3,"ngModelChange","ngModel"],["optionLabel","label","optionValue","value","placeholder","Select a channel source","appendTo","body",3,"ngModelChange","options","ngModel"]],template:function(t,e){if(t&1){let i=w();a(0,"div",1)(1,"div",2)(2,"p-selectbutton",3),_("ngModelChange",function(o){return r(i),u(e.lang,o)||(e.lang=o),s(o)}),y("onChange",function(){return r(i),s(e.loadData())}),d(),a(3,"p-button",4),y("onClick",function(){return r(i),s(e.handleCreate())}),d()(),a(4,"c-tree-table",5),y("onDrop",function(o){return r(i),s(e.handleDrop(o))})("onEdit",function(o){return r(i),s(e.handleEdit(o))})("onDelete",function(o){return r(i),s(e.handleDelete(o))})("onAdd",function(o){return r(i),s(e.handleAdd(o))}),d()(),a(5,"p-drawer",6),_("visibleChange",function(o){return r(i),u(e.drawerVisible,o)||(e.drawerVisible=o),s(o)}),a(6,"p-button",7),y("onClick",function(){return r(i),s(e.handleRemove())}),d(),a(7,"p-virtualscroller",8),S(8,ce,4,5,"ng-template",null,0,W),d()(),a(10,"p-dialog",9),_("visibleChange",function(o){return r(i),u(e.selectChannel,o)||(e.selectChannel=o),s(o)}),S(11,he,13,4,"div",10),a(12,"div",11)(13,"p-button",12),g(14,"Cancel"),d(),a(15,"p-button",13),y("onClick",function(){return r(i),s(e.updateSelectChannel())}),g(16," Confirm "),d()()()}t&2&&(p(2),c("options",e.langOptions),C("ngModel",e.lang),p(2),c("data",e.data)("cols",e.cols),p(),V(v(16,pe)),C("visible",e.drawerVisible),p(),c("text",!0),p(),c("items",e.subData())("itemSize",40),p(3),V(v(17,me)),c("header",e.mode()==="edit"?"Edit Channel":"Create Channel")("modal",!0),C("visible",e.selectChannel),p(),x(e.selectChannel()?11:-1))},dependencies:[Y,U,z,O,Z,I,K,J,P,A,F,L,j,R,H,N,G,q,te,le,ie,X,Q],encapsulation:2})}};export{de as ChannelComponent};
