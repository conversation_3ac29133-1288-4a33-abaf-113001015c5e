import{p as m}from"./chunk-7BEB76UR.js";import{_ as r,da as i}from"./chunk-6TTFYGC3.js";import{a}from"./chunk-QWWW7GFA.js";var s=class o{constructor(e){this.restService=e;this.apiName="Default";this.getAlbumFiles=(e,t)=>this.restService.request({method:"GET",url:`/api/app/read-only-album/album-files/${e}`},a({apiName:this.apiName},t));this.getList=(e,t)=>this.restService.request({method:"GET",url:"/api/app/read-only-album",params:{channelId:e.channelId,albumType:e.albumType,channelContentCode:e.channelContentCode,sorting:e.sorting,skipCount:e.skipCount,maxResultCount:e.maxResultCount}},a({apiName:this.apiName},t))}static{this.\u0275fac=function(t){return new(t||o)(i(m))}}static{this.\u0275prov=r({token:o,factory:o.\u0275fac,providedIn:"root"})}};export{s as a};
