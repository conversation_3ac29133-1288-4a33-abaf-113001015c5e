﻿using System.Collections;
using System.Collections.Generic;
using HolyBless.Entities.Articles;
using HolyBless.Entities.Channels;
using HolyBless.Enums;
using HolyBless.Lookups;
using Volo.Abp.Domain.Entities.Auditing;

namespace HolyBless.Entities.VirtualFolders
{
    public class VirtualDiskFolder : LanguageAuditedAggregateRoot<int>
    {
        public int? ParentFolderId { get; set; }
        public VirtualDiskFolder? ParentFolder { get; set; }
        public string FolderName { get; set; } = default!;
        public string? SpokenLangCode { get; set; }
        public int Views { get; set; } = 0;     //Visit times
        public int Weight { get; set; } = 0;     //Decide display order
        public int? ChannelId { get; set; }     //Under which channel
        public Channel? Channel { get; set; }
        public ICollection<VirtualDiskFolder>? Children { get; set; }
        public ICollection<FolderToFile> FolderToBucketFiles { get; set; } = new List<FolderToFile>();
    }
}