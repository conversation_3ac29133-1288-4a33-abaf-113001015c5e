using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HolyBless.Entities.Articles;
using Volo.Abp;
using Volo.Abp.Domain.Entities;

namespace HolyBless.Entities.Collections
{
    public class CollectionToArticle : Entity, ISoftDelete
    {
        public int CollectionId { get; set; }
        public Collection Collection { get; set; } = default!;
        public int ArticleId { get; set; }
        public Article Article { get; set; } = default!;
        public int Weight { get; set; } = 0;
        public bool IsDeleted { get; set; }

        public override object?[] GetKeys()
        {
            return [CollectionId, ArticleId];
        }
    }
}