using HolyBless.Buckets;
using HolyBless.Channels.Dtos;
using HolyBless.Entities.Albums;
using HolyBless.Entities.Books;
using HolyBless.Entities.Channels;
using HolyBless.Entities.Collections;
using HolyBless.Entities.VirtualFolders;
using HolyBless.Permissions;
using HolyBless.Services;
using HolyBless.TreeJsonSnapshots;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.Channels
{
    [Authorize(HolyBlessPermissions.Channels.Default)]
    public class ChannelAppService : ReadOnlyChannelAppService, IChannelAppService
    {
        private readonly IRepository<Collection, int> _collectionRepository;
        private readonly IRepository<EBook, int> _eBookRepository;
        private readonly IRepository<Album, int> _albumRepository;
        private readonly IRepository<VirtualDiskFolder, int> _virtualDiskFolderRepository;

        public ChannelAppService(IRepository<Channel, int> repository,
            IRepository<Collection, int> collectionRepository,
            IRepository<EBook, int> eBookRepository,
            IRepository<Album, int> albumRepository,
            IRepository<VirtualDiskFolder, int> virtualDiskFolderRepository,
            ITreeJsonSnapshotAppService treeSnapshotAppService,
            IRequestContextService requestContextService,
            ICachedFileUrlAppService cachedFileUrlAppService
            )
            : base(repository, treeSnapshotAppService, requestContextService, cachedFileUrlAppService)
        {
            _collectionRepository = collectionRepository;
            _eBookRepository = eBookRepository;
            _albumRepository = albumRepository;
            _virtualDiskFolderRepository = virtualDiskFolderRepository;
        }

        [Authorize(HolyBlessPermissions.Channels.Create)]
        public async Task<ChannelDto> CreateAsync(CreateUpdateChannelDto input)
        {
            var channel = ObjectMapper.Map<CreateUpdateChannelDto, Channel>(input);
            
            // Ensure ContentCode is set if not provided
            if (string.IsNullOrWhiteSpace(channel.ContentCode))
            {
                channel.ContentCode = "";
            }
            
            channel = await _repository.InsertAsync(channel, autoSave: true);
            return ObjectMapper.Map<Channel, ChannelDto>(channel);
        }

        [Authorize(HolyBlessPermissions.Channels.Edit)]
        public async Task<ChannelDto> UpdateAsync(int id, CreateUpdateChannelDto input)
        {
            var channel = await _repository.GetAsync(id);
            ObjectMapper.Map(input, channel);
            channel = await _repository.UpdateAsync(channel, true);
            return ObjectMapper.Map<Channel, ChannelDto>(channel);
        }

        /// <summary>
        /// [Admin] Delete a channel by its ID. Recursively handles all descendant channels and their foreign key relationships based on forceDelete flag.
        /// </summary>
        /// <param name="id">The ID of the channel to delete</param>
        /// <param name="forceDelete">
        /// If true (default): Recursively collects all descendant channel IDs from ChannelTreeCache, then uses bulk updates to set ChannelId to null
        /// for all related Collections, EBooks, Albums, and VirtualDiskFolders across the entire channel hierarchy before deletion.
        /// If false: Throws exceptions if any foreign key relationships exist on this channel or any descendant channels, preventing deletion to maintain data integrity.
        /// </param>
        /// <returns>A task representing the asynchronous delete operation</returns>
        /// <exception cref="UserFriendlyException">
        /// Thrown when forceDelete is false and foreign key relationships exist on this channel or any descendant channels.
        /// </exception>
        [Authorize(HolyBlessPermissions.Channels.Delete)]
        public async Task DeleteAsync(int id, bool forceDelete = true)
        {
            var channel = await _repository.GetAsync(id, includeDetails: true);

            // Get all descendant channel IDs from ChannelTreeCache
            var allChannelIds = await GetAllDescendantChannelIdsAsync(id, channel.LanguageCode);

            if (forceDelete)
            {
                // Force delete: Use bulk updates to handle foreign key relationships for all channels in hierarchy
                await BulkUpdateForeignKeyReferencesAsync(allChannelIds);

                // Delete all channels in the hierarchy (children first, then parent)
                var channelsToDelete = allChannelIds.Where(cid => cid != id).ToList();
                channelsToDelete.Add(id); // Add parent channel last

                foreach (var channelId in channelsToDelete)
                {
                    await _repository.DeleteAsync(channelId, autoSave: true);
                }
            }
            else
            {
                // Normal delete: Check for foreign key relationships on this channel and all descendants
                await ValidateNoForeignKeyRelationshipsAsync(allChannelIds);

                // If validation passes, delete all channels in hierarchy
                var channelsToDelete = allChannelIds.Where(cid => cid != id).ToList();
                channelsToDelete.Add(id); // Add parent channel last

                foreach (var channelId in channelsToDelete)
                {
                    await _repository.DeleteAsync(channelId, autoSave: true);
                }
            }
            await _treeSnapshotAppService.ClearCacheAsync(HolyBless.Enums.TreeType.Channel, null);
        }

        /// <summary>
        /// Get all descendant channel IDs recursively from ChannelTreeCache
        /// </summary>
        /// <param name="channelId">The root channel ID</param>
        /// <param name="languageCode">Language code for the channel tree</param>
        /// <returns>List of all channel IDs including the root and all descendants</returns>
        private async Task<List<int>> GetAllDescendantChannelIdsAsync(int channelId, string? languageCode)
        {
            if (string.IsNullOrEmpty(languageCode))
            {
                return new List<int> { channelId };
            }

            var tree = await GetChannelTreeAsync(languageCode);
            var allChannels = tree.Flatten<ChannelTreeDto>(new List<ChannelTreeDto>());

            var targetChannel = allChannels.FirstOrDefault(c => c.Id == channelId);
            if (targetChannel == null)
            {
                return new List<int> { channelId };
            }

            var result = new List<int> { channelId };
            CollectDescendantIds(targetChannel, result);
            return result;
        }

        /// <summary>
        /// Recursively collect all descendant channel IDs
        /// </summary>
        private void CollectDescendantIds(ChannelTreeDto channel, List<int> result)
        {
            foreach (var child in channel.Children)
            {
                result.Add(child.Id);
                CollectDescendantIds(child, result);
            }
        }

        /// <summary>
        /// Bulk update foreign key references to null for all specified channel IDs
        /// </summary>
        /// <param name="channelIds">List of channel IDs to update FK references for</param>
        private async Task BulkUpdateForeignKeyReferencesAsync(List<int> channelIds)
        {
            if (!channelIds.Any()) return;

            // Bulk update Collections
            var collectionsQueryable = await _collectionRepository.GetQueryableAsync();
            var collectionsToUpdate = await AsyncExecuter.ToListAsync(
                collectionsQueryable.Where(c => channelIds.Contains(c.ChannelId ?? 0))
            );
            if (collectionsToUpdate.Any())
            {
                foreach (var collection in collectionsToUpdate)
                {
                    collection.ChannelId = null;
                }
                await _collectionRepository.UpdateManyAsync(collectionsToUpdate, autoSave: false);
            }

            // Bulk update EBooks
            var eBooksQueryable = await _eBookRepository.GetQueryableAsync();
            var eBooksToUpdate = await AsyncExecuter.ToListAsync(
                eBooksQueryable.Where(e => channelIds.Contains(e.ChannelId ?? 0))
            );
            if (eBooksToUpdate.Any())
            {
                foreach (var eBook in eBooksToUpdate)
                {
                    eBook.ChannelId = null;
                }
                await _eBookRepository.UpdateManyAsync(eBooksToUpdate, autoSave: false);
            }

            // Bulk update Albums
            var albumsQueryable = await _albumRepository.GetQueryableAsync();
            var albumsToUpdate = await AsyncExecuter.ToListAsync(
                albumsQueryable.Where(a => channelIds.Contains(a.ChannelId ?? 0))
            );
            if (albumsToUpdate.Any())
            {
                foreach (var album in albumsToUpdate)
                {
                    album.ChannelId = null;
                }
                await _albumRepository.UpdateManyAsync(albumsToUpdate, autoSave: false);
            }

            // Bulk update VirtualDiskFolders
            var foldersQueryable = await _virtualDiskFolderRepository.GetQueryableAsync();
            var foldersToUpdate = await AsyncExecuter.ToListAsync(
                foldersQueryable.Where(f => channelIds.Contains(f.ChannelId ?? 0))
            );
            if (foldersToUpdate.Any())
            {
                foreach (var folder in foldersToUpdate)
                {
                    folder.ChannelId = null;
                }
                await _virtualDiskFolderRepository.UpdateManyAsync(foldersToUpdate, autoSave: false);
            }

            // Save all changes at once
            if (CurrentUnitOfWork != null)
            {
                await CurrentUnitOfWork.SaveChangesAsync();
            }
        }

        /// <summary>
        /// Validate that no foreign key relationships exist for the specified channel IDs
        /// </summary>
        /// <param name="channelIds">List of channel IDs to validate</param>
        private async Task ValidateNoForeignKeyRelationshipsAsync(List<int> channelIds)
        {
            if (!channelIds.Any()) return;

            // Check Collections
            var collectionsQueryable = await _collectionRepository.GetQueryableAsync();
            var hasCollections = await AsyncExecuter.AnyAsync(
                collectionsQueryable.Where(c => channelIds.Contains(c.ChannelId ?? 0))
            );
            if (hasCollections)
            {
                throw new UserFriendlyException(L["CannotDeleteChannelWithCollections"]);
            }

            // Check EBooks
            var eBooksQueryable = await _eBookRepository.GetQueryableAsync();
            var hasEBooks = await AsyncExecuter.AnyAsync(
                eBooksQueryable.Where(e => channelIds.Contains(e.ChannelId ?? 0))
            );
            if (hasEBooks)
            {
                throw new UserFriendlyException(L["CannotDeleteChannelWithEBooks"]);
            }

            // Check Albums
            var albumsQueryable = await _albumRepository.GetQueryableAsync();
            var hasAlbums = await AsyncExecuter.AnyAsync(
                albumsQueryable.Where(a => channelIds.Contains(a.ChannelId ?? 0))
            );
            if (hasAlbums)
            {
                throw new UserFriendlyException(L["CannotDeleteChannelWithAlbums"]);
            }

            // Check VirtualDiskFolders
            var foldersQueryable = await _virtualDiskFolderRepository.GetQueryableAsync();
            var hasFolders = await AsyncExecuter.AnyAsync(
                foldersQueryable.Where(f => channelIds.Contains(f.ChannelId ?? 0))
            );
            if (hasFolders)
            {
                throw new UserFriendlyException(L["CannotDeleteChannelWithVirtualFolders"]);
            }
        }

        /// <summary>
        /// [Admin] Get a channel by its ID. This method is used to retrieve a specific channel's details.
        /// Usage: Pop up a dialog to show channel details, or when editing a channel.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ChannelDto> GetAsync(int id)
        {
            var channel = await _repository.FirstOrDefaultAsync(x => x.Id == id);
            Check.NotNull(channel, nameof(channel));
            var channelDto = ObjectMapper.Map<Channel, ChannelDto>(channel);
            return channelDto;
        }

        /// <summary>
        /// [Admin] Get a paginated list of channels based on search criteria, additional filter by lannguage code (from request header).
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<ChannelDto>> GetListAsync(ChannelSearchDto input)
        {
            var languageCode = _requestContextService!.GetLanguageCode();
            var queryable = await _repository.GetQueryableAsync();
            var query = queryable
                .WhereIf(!string.IsNullOrWhiteSpace(input.ContentCode), x => (x.ContentCode ?? "").ToLower() == input.ContentCode!.ToLower())
                .WhereIf(!string.IsNullOrWhiteSpace(languageCode), x => (x.LanguageCode ?? "").ToLower() == languageCode!.ToLower())
                .OrderBy(input.Sorting ?? "Weight")
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount);

            var channels = await AsyncExecuter.ToListAsync(query);
            var totalCount = await AsyncExecuter.CountAsync(queryable);

            return new PagedResultDto<ChannelDto>(
                totalCount,
                ObjectMapper.Map<List<Channel>, List<ChannelDto>>(channels)
            );
        }

        /// <summary>
        /// [Admin] Get all channels without pagination, filtered by language code.
        /// </summary>
        /// <param name="languageCode">Optional language code to filter channels. If null, uses all languages.</param>
        /// <returns></returns>
        public async Task<List<ChannelDto>> GetAllChannelsAsync(string? languageCode = null)
        {
            var queryable = await _repository.GetQueryableAsync();
            var query = queryable
                .WhereIf(!string.IsNullOrEmpty(languageCode), x => x.LanguageCode != null && x.LanguageCode.ToLower() == languageCode!.ToLower())
                .OrderBy(x=>x.Weight)
                ;
            var channels = await AsyncExecuter.ToListAsync(query);
            return ObjectMapper.Map<List<Channel>, List<ChannelDto>>(channels);
        }

        /// <summary>
        /// Move a channel to a new parent and reorder by weight relative to a beforeId.
        /// </summary>
        /// <param name="channelId">The channel being moved</param>
        /// <param name="toParentId">The new parent channel id (nullable for root)</param>
        /// <param name="beforeId">The channel that the moved channel should be placed before. If null, places at the end.</param>
        [Authorize(HolyBlessPermissions.Channels.Edit)]
        public async Task MoveChannelAsync(int channelId, int? toParentId, int? beforeId)
        {
            var channelEntity = await _repository.GetAsync(channelId);
            var languageCode = channelEntity.LanguageCode;
            if (string.IsNullOrEmpty(languageCode))
            {
                throw new UserFriendlyException(L["ChannelMissingLanguageCode"]);
            }

            var tree = await GetChannelTreeAsync(languageCode);
            var allChannels = tree.Flatten<ChannelTreeDto>(new List<ChannelTreeDto>());

            var channel = allChannels.FirstOrDefault(c => c.Id == channelId);
            var toParent = toParentId.HasValue ? allChannels.FirstOrDefault(c => c.Id == toParentId.Value) : null;

            if (channel == null)
            {
                // This should not happen if we found the channelEntity before.
                throw new UserFriendlyException(L["ChannelNotFound"]);
            }

            // Validation: Cannot move under channel's own child channel (deep check)
            if (toParent != null)
            {
                var descendantIds = new HashSet<int>(channel.GetDescendantIds());
                if (descendantIds.Contains(toParent.Id))
                {
                    throw new UserFriendlyException(L["CannotMoveUnderChildChannel"]);
                }
            }

            // Update ParentId logic
            channelEntity.ParentChannelId = toParentId;

            // Update Weight logic
            var queryable = await _repository.GetQueryableAsync();
            
            if (beforeId.HasValue)
            {
                // Normal case: insert before a specific channel
                var beforeEntity = await _repository.GetAsync(beforeId.Value);
                var insertWeight = beforeEntity.Weight;

                // Increase weight by +1 for siblings under the same parent of BeforeId with weight >= BeforeId's weight
                var siblingsToShift = await AsyncExecuter.ToListAsync(
                    queryable.Where(x => x.ParentChannelId == beforeEntity.ParentChannelId
                        && x.Id != channelId // Exclude the channel being moved
                        && x.Weight >= insertWeight)
                );

                foreach (var s in siblingsToShift)
                {
                    s.Weight += 1;
                    await _repository.UpdateAsync(s, true);
                }

                // Set the moved channel's weight to the original weight of the 'before' channel
                channelEntity.Weight = insertWeight;
            }
            else
            {
                // beforeId is null: move to the last position
                // Find the highest weight among siblings under the target parent
                var siblings = await AsyncExecuter.ToListAsync(
                    queryable.Where(x => x.ParentChannelId == toParentId
                        && x.Id != channelId) // Exclude the channel being moved
                );

                var maxWeight = siblings.Any() ? siblings.Max(x => x.Weight) : 0;
                channelEntity.Weight = maxWeight + 1;
            }

            await _repository.UpdateAsync(channelEntity, true);

            // Clear channel tree cache so next read rebuilds the snapshot
            await _treeSnapshotAppService.ClearCacheAsync(HolyBless.Enums.TreeType.Channel, null);
        }
    }
}