import{a as Ce}from"./chunk-RCAKOILJ.js";import{a as be,b as ge}from"./chunk-G4FA4R76.js";import{a as me}from"./chunk-SPDIY6EA.js";import{a as fe}from"./chunk-WSOPBUUS.js";import{a as oe,b as ae,c as le,d as re,e as se,f as de,g as pe,h as ce}from"./chunk-JBJGPYJN.js";import{a as ne,b as ie}from"./chunk-E6VTCYRR.js";import{a as ee,b as te,c as B}from"./chunk-W4NOLTEH.js";import{B as $,I as ue,J as _e,K as he,p as Q,q as U,r as X,s as Y,t as Z,u as K}from"./chunk-NSHV5XN7.js";import{Ba as W,C as z,E as q,F as G,H as J}from"./chunk-GXYB24H2.js";import{$a as s,$b as x,Bb as A,Ca as _,Db as T,Eb as M,Fb as a,Gb as l,Hb as f,Lb as E,Qb as w,Rb as g,Zb as u,a as P,b as F,bc as h,cc as C,dc as b,ec as H,fc as k,ga as D,hc as O,jb as L,na as p,oa as c,oc as j,pb as S,qc as N,rc as V,wb as m,zb as v}from"./chunk-AKB6GSCA.js";var Ee=()=>({"min-width":"50rem"}),ye=()=>({width:"25rem"}),ke=(d,n)=>({type:d,selectData:n}),De=(d,n)=>n.field;function Se(d,n){if(d&1&&(a(0,"th",19)(1,"div",21),u(2),f(3,"p-columnFilter",22),l()()),d&2){let e=n.$implicit;s(2),x(" ",e.header," "),s(),m("field",e.field)}}function ve(d,n){if(d&1&&(a(0,"tr"),f(1,"th",17),a(2,"th",18),f(3,"p-tableHeaderCheckbox"),l(),T(4,Se,4,2,"th",19,De),f(6,"th",20),l()),d&2){let e=n.$implicit;s(4),M(e)}}function Te(d,n){if(d&1&&(a(0,"td"),u(1),j(2,"tableDisplay"),l()),d&2){let e=n.$implicit,t=g().$implicit;s(),x(" ",N(2,1,t[e.field],O(4,ke,e.type,e.selectData))," ")}}function Me(d,n){if(d&1){let e=E();a(0,"tr",23)(1,"td"),f(2,"span",24),l(),a(3,"td"),f(4,"p-tableCheckbox",25),l(),T(5,Te,3,7,"td",null,De),a(7,"td")(8,"p-button",26),w("onClick",function(){let i=p(e).$implicit,o=g();return c(o.handleEdit(i))}),l(),a(9,"p-button",27),w("onClick",function(i){let o=p(e).$implicit,r=g();return c(r.handleDelete(i,o))}),l()()()}if(d&2){let e=n.$implicit,t=n.columns,i=n.rowIndex;m("pReorderableRow",i),s(4),m("value",e),s(),M(t),s(3),m("text",!0),s(),m("text",!0)}}function xe(d,n){if(d&1){let e=E();a(0,"div",11)(1,"div",15)(2,"label"),u(3,"Title"),l(),a(4,"input",28),b("ngModelChange",function(i){p(e);let o=g();return C(o.selectData().title,i)||(o.selectData().title=i),c(i)}),l()(),a(5,"div",15)(6,"label"),u(7,"Channel"),l(),a(8,"p-treeselect",16),b("ngModelChange",function(i){p(e);let o=g();return C(o.selectData().channel,i)||(o.selectData().channel=i),c(i)}),l()(),a(9,"div",15)(10,"label"),u(11,"Thumbnail"),l(),a(12,"input",28),b("ngModelChange",function(i){p(e);let o=g();return C(o.selectData().thumbnailFileId,i)||(o.selectData().thumbnailFileId=i),c(i)}),l()(),a(13,"div",15)(14,"label"),u(15,"Description"),l(),a(16,"input",28),b("ngModelChange",function(i){p(e);let o=g();return C(o.selectData().description,i)||(o.selectData().description=i),c(i)}),l()(),a(17,"div",15)(18,"label"),u(19,"Delivery date"),l(),a(20,"p-datepicker",29),b("ngModelChange",function(i){p(e);let o=g();return C(o.selectData().deliveryDate,i)||(o.selectData().deliveryDate=i),c(i)}),l()()()}if(d&2){let e=g();s(4),h("ngModel",e.selectData().title),s(4),h("ngModel",e.selectData().channel),m("options",e.channelTree()),s(4),h("ngModel",e.selectData().thumbnailFileId),s(4),h("ngModel",e.selectData().description),s(4),h("ngModel",e.selectData().deliveryDate)}}var we=class d{constructor(){this.#e=D(Ce);this.confirmationService=D(W);this.#t=D(me);this.langOptions=[{label:"zh-Hans"},{label:"zh-Hant"},{label:"en"}];this.lang=_("zh-Hans");this.columns=[{field:"id",header:"ID"},{field:"title",header:"Title"},{field:"channelId",header:"Channel"},{field:"description",header:"Description"},{field:"deliveryDate",header:"Delivery Date",type:"date"}];this.first=_(0);this.rows=_(10);this.totalRecords=_(0);this.data=_([]);this.selectedRows=_([]);this.channelTree=_([]);this.channelMap=new Map;this.addToChannelVisible=_(!1);this.selectChannel=_(null);this.contentCategoryOptions=fe;this.selectData=_(null);this.mode=_("create")}#e;#t;ngOnInit(){this.loadData(),this.loadChannelData()}loadData(){this.#e.getAllEBooks(this.lang()).subscribe({next:n=>{this.data.set(n||[])}})}loadChannelData(){this.#t.getAllChannels(this.lang()).subscribe({next:n=>{this.channelTree.set(this.buildTreeData(n,void 0,void 0,void 0,this.channelMap))}})}buildTreeData(n,e="parentChannelId",t="id",i="name",o=new Map){let r=[];return n.forEach(y=>{o.set(y[t],F(P({},y),{key:y[t],label:y[i],children:[]}))}),n.forEach(y=>{let R=o.get(y[t]);if(!y[e])r.push(R);else{let I=o.get(y[e]);I&&I.children.push(R)}}),r}onPageChange(n){this.first.set(n.first),this.loadData()}handleEdit(n){let e=B(n);e.channelId&&(e.channel=this.channelMap.get(e.channelId)),e.deliveryDate&&(e.deliveryDate=new Date(e.deliveryDate)),this.selectData.set(e),this.mode.set("edit")}handleCreate(){this.selectData.set({}),this.mode.set("create")}updateSelectData(){let n=B(this.selectData());n.channel&&(n.channelId=n.channel.id,delete n.channel),this.mode()==="edit"?this.#e.update(n.id,n).subscribe({next:e=>{this.loadData(),this.selectData.set(null)}}):this.#e.create(n).subscribe({next:e=>{this.loadData(),this.selectData.set(null)}})}handleDelete(n,e){this.confirmationService.confirm({target:n.currentTarget,message:"Do you want to delete this record?",icon:"pi pi-info-circle",rejectButtonProps:{label:"Cancel",severity:"secondary",outlined:!0},acceptButtonProps:{label:"Delete",severity:"danger"},accept:()=>{this.#e.delete(e.id).subscribe({next:t=>{this.loadData()}})}})}handleAddToChannel(){this.addToChannelVisible.set(!0)}changeChannel(){let n=this.selectedRows().map(e=>e.id);this.#e.linkToChannelByChannelIdAndEBookIds(this.selectChannel().id,n).subscribe({next:e=>{this.loadData(),this.selectChannel.set(null),this.selectedRows.set([]),this.addToChannelVisible.set(!1)}})}static{this.\u0275fac=function(e){return new(e||d)}}static{this.\u0275cmp=L({type:d,selectors:[["app-ebook"]],features:[H([W])],decls:31,vars:26,consts:[["header",""],["body",""],[1,"p-4"],[1,"flex","justify-between"],["optionLabel","label","optionValue","label",3,"ngModelChange","onChange","options","ngModel"],[1,"flex","gap-2"],["label","Create",3,"onClick","text"],["severity","danger","label","Delete",3,"text"],["label","Add to channel",3,"onClick","text"],[3,"selectionChange","value","columns","reorderableColumns","tableStyle","selection"],[3,"visibleChange","header","modal","visible"],[1,"form"],[1,"w-full","grid","grid-cols-2","gap-2","mt-4"],["styleClass","w-full","severity","secondary"],["styleClass","w-full",3,"onClick"],[1,"form-item"],["containerStyleClass","w-full","appendTo","body",3,"ngModelChange","ngModel","options"],[2,"width","3rem"],[2,"width","4rem"],["pReorderableColumn",""],[2,"width","8rem"],[1,"flex","items-center"],["type","text","display","menu",3,"field"],[3,"pReorderableRow"],["pReorderableRowHandle","",1,"pi","pi-bars"],[3,"value"],["styleClass","!rounded-full","icon","pi pi-pencil",3,"onClick","text"],["styleClass","!rounded-full","icon","pi pi-trash","severity","danger",3,"onClick","text"],["type","text","pInputText","",3,"ngModelChange","ngModel"],["styleClass","w-full","dateFormat","yy-mm-dd",3,"ngModelChange","ngModel"]],template:function(e,t){if(e&1){let i=E();a(0,"div",2)(1,"div",3)(2,"p-selectbutton",4),b("ngModelChange",function(r){return p(i),C(t.lang,r)||(t.lang=r),c(r)}),w("onChange",function(){return p(i),c(t.loadData())}),l(),a(3,"div",5)(4,"p-button",6),w("onClick",function(){return p(i),c(t.handleCreate())}),l(),f(5,"p-button",7),a(6,"p-button",8),w("onClick",function(){return p(i),c(t.handleAddToChannel())}),l()()(),a(7,"p-table",9),b("selectionChange",function(r){return p(i),C(t.selectedRows,r)||(t.selectedRows=r),c(r)}),S(8,ve,7,0,"ng-template",null,0,V)(10,Me,10,4,"ng-template",null,1,V),l()(),f(12,"p-confirmpopup"),a(13,"p-dialog",10),b("visibleChange",function(r){return p(i),C(t.selectData,r)||(t.selectData=r),c(r)}),S(14,xe,21,6,"div",11),a(15,"div",12)(16,"p-button",13),u(17,"Cancel"),l(),a(18,"p-button",14),w("onClick",function(){return p(i),c(t.updateSelectData())}),u(19," Confirm "),l()()(),a(20,"p-dialog",10),b("visibleChange",function(r){return p(i),C(t.addToChannelVisible,r)||(t.addToChannelVisible=r),c(r)}),a(21,"div",11)(22,"div",15)(23,"label"),u(24,"Channel"),l(),a(25,"p-treeselect",16),b("ngModelChange",function(r){return p(i),C(t.selectChannel,r)||(t.selectChannel=r),c(r)}),l()()(),a(26,"div",12)(27,"p-button",13),u(28,"Cancel"),l(),a(29,"p-button",14),w("onClick",function(){return p(i),c(t.changeChannel())}),u(30," Confirm "),l()()()}e&2&&(s(2),m("options",t.langOptions),h("ngModel",t.lang),s(2),m("text",!0),s(),m("text",!0),s(),m("text",!0),s(),m("value",t.data())("columns",t.columns)("reorderableColumns",!0)("tableStyle",k(23,Ee)),h("selection",t.selectedRows),s(6),v(k(24,ye)),m("header",t.mode()==="edit"?"Edit":"Create")("modal",!0),h("visible",t.selectData),s(),A(t.selectData()?14:-1),s(6),v(k(25,ye)),m("header","Add to channel")("modal",!0),h("visible",t.addToChannelVisible),s(5),h("ngModel",t.selectChannel),m("options",t.channelTree()))},dependencies:[U,Q,te,ee,J,z,q,G,K,Z,Y,X,$,ie,ne,_e,ue,he,ce,oe,ae,le,re,se,de,pe,ge,be],encapsulation:2})}};export{we as EbookComponent};
