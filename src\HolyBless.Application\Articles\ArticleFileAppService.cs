using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using HolyBless.Articles.Dtos;
using HolyBless.Entities.Articles;
using HolyBless.Entities.Buckets;
using HolyBless.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.ObjectMapping;

namespace HolyBless.Articles
{
    [Authorize(HolyBlessPermissions.ArticleFiles.Default)]
    public class ArticleFileAppService : HolyBlessAppService, IArticleFileAppService
    {
        private readonly IRepository<ArticleFile, int> _repository;
        private readonly IRepository<Article, int> _articleRepository;
        private readonly IRepository<BucketFile, int> _bucketFileRepository;

        public ArticleFileAppService(
            IRepository<ArticleFile, int> repository,
            IRepository<Article, int> articleRepository,
            IRepository<BucketFile, int> bucketFileRepository)
        {
            _repository = repository;
            _articleRepository = articleRepository;
            _bucketFileRepository = bucketFileRepository;
        }

        [Authorize(HolyBlessPermissions.ArticleFiles.Edit)]
        public async Task<ArticleFileDto> SetPrimaryAsync(int id)
        {
            var articleFile = await _repository.GetAsync(id);
            articleFile.IsPrimary = true;
            await _repository.UpdateAsync(articleFile);
            return ObjectMapper.Map<ArticleFile, ArticleFileDto>(articleFile);
        }

        [Authorize(HolyBlessPermissions.ArticleFiles.Create)]
        public async Task<ArticleFileDto> CreateAsync(CreateUpdateArticleFileDto input)
        {
            await ValidateArticleAndFileExistAsync(input.ArticleId, input.FileId);
            var query = await _repository.GetQueryableAsync();
            var exists = await query.AnyAsync(af => af.ArticleId == input.ArticleId && af.FileId == input.FileId);
            if (exists)
            {
                throw new UserFriendlyException(L["FileAlreadyAttachedToArticle"]);
            }
            var articleFile = ObjectMapper.Map<CreateUpdateArticleFileDto, ArticleFile>(input);

            articleFile = await _repository.InsertAsync(articleFile);
            return ObjectMapper.Map<ArticleFile, ArticleFileDto>(articleFile);
        }

        [Authorize(HolyBlessPermissions.ArticleFiles.Edit)]
        public async Task<ArticleFileDto> UpdateAsync(int id, CreateUpdateArticleFileDto input)
        {
            var articleFile = await _repository.GetAsync(id);
            if (articleFile.ArticleId != input.ArticleId || articleFile.FileId != input.FileId)
            {
                await ValidateArticleAndFileExistAsync(input.ArticleId, input.FileId);
                var query = await _repository.GetQueryableAsync();
                var exists = await query.AnyAsync(af => af.Id != id && af.ArticleId == input.ArticleId && af.FileId == input.FileId);
                if (exists)
                {
                    throw new UserFriendlyException(L["FileAlreadyAttachedToArticle"]);
                }
            }
            ObjectMapper.Map<CreateUpdateArticleFileDto, ArticleFile>(input, articleFile);

            articleFile = await _repository.UpdateAsync(articleFile);
            return ObjectMapper.Map<ArticleFile, ArticleFileDto>(articleFile);
        }

        private async Task ValidateArticleAndFileExistAsync(int articleId, int fileId)
        {
            var article = await _articleRepository.FindAsync(articleId);
            if (article == null)
            {
                throw new EntityNotFoundException(typeof(Article), articleId);
            }
            var bucketFile = await _bucketFileRepository.FindAsync(fileId);
            if (bucketFile == null)
            {
                throw new EntityNotFoundException(typeof(BucketFile), fileId);
            }
        }

        public async Task<ArticleFileDto> GetAsync(int id)
        {
            var query = await _repository.GetQueryableAsync();
            var articleFile = await query
                .Include(af => af.Article)
                .Include(af => af.BucketFile)
                .FirstOrDefaultAsync(af => af.Id == id);

            if (articleFile == null)
            {
                throw new EntityNotFoundException(typeof(ArticleFile), id);
            }

            return ObjectMapper.Map<ArticleFile, ArticleFileDto>(articleFile);
        }

        public async Task<PagedResultDto<ArticleFileDto>> GetListAsync(PagedAndSortedResultRequestDto input)
        {
            var query = await _repository.GetQueryableAsync();
            query = query
                .Include(af => af.Article)
                .Include(af => af.BucketFile)
                .OrderBy(input.Sorting ?? "CreationTime desc");

            var totalCount = await query.CountAsync();
            query = query.Skip(input.SkipCount).Take(input.MaxResultCount);
            var items = await query.ToListAsync();

            return new PagedResultDto<ArticleFileDto>(
                totalCount,
                ObjectMapper.Map<List<ArticleFile>, List<ArticleFileDto>>(items)
            );
        }

        public async Task<List<ArticleFileDto>> GetByArticleIdAsync(int articleId)
        {
            var query = await _repository.GetQueryableAsync();
            var articleFiles = await query
                .Include(af => af.BucketFile)
                .Where(af => af.ArticleId == articleId)
                .OrderByDescending(af => af.IsPrimary)
                .ThenBy(af => af.CreationTime)
                .ToListAsync();
            return ObjectMapper.Map<List<ArticleFile>, List<ArticleFileDto>>(articleFiles);
        }

        public async Task<List<ArticleFileDto>> GetPrimaryFilesAsync(int articleId)
        {
            var query = await _repository.GetQueryableAsync();
            var primaryFiles = await query
                .Include(af => af.BucketFile)
                .Where(af => af.ArticleId == articleId && af.IsPrimary)
                .OrderBy(af => af.CreationTime)
                .ToListAsync();
            return ObjectMapper.Map<List<ArticleFile>, List<ArticleFileDto>>(primaryFiles);
        }

        [Authorize(HolyBlessPermissions.ArticleFiles.Delete)]
        public async Task DeleteAsync(int id)
        {
            await _repository.DeleteAsync(id);
        }
    }
}