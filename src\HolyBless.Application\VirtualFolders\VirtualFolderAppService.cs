﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HolyBless.Buckets;
using HolyBless.Entities.VirtualFolders;
using HolyBless.Enums;
using HolyBless.Permissions;
using HolyBless.Services;
using HolyBless.TreeJsonSnapshots;
using HolyBless.VirtualFolders.Dtos;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Volo.Abp;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.VirtualFolders
{
    public class VirtualFolderAppService : ReadOnlyVirtualFolderAppService, IVirtualFolderAppService
    {
        public VirtualFolderAppService(
             IRepository<VirtualDiskFolder, int> virtualFolderRepository,
             IRepository<FolderToFile> folderToBucketFileRepository,
             ITreeJsonSnapshotAppService treeSnapshotAppService,
             IRequestContextService requestContextService,
             ICachedFileUrlAppService cachedFileUrlAppService
            )
            : base(virtualFolderRepository, folderToBucketFileRepository, treeSnapshotAppService, requestContextService, cachedFileUrlAppService)
        {
        }

        [Authorize(HolyBlessPermissions.VirtualFolders.Create)]
        public async Task<VirtualFolderDto> CreateAsync(CreateUpdateVirtualFolderDto input)
        {
            var virtualFolder = ObjectMapper.Map<CreateUpdateVirtualFolderDto, VirtualDiskFolder>(input);
            virtualFolder = await _virtualFolderRepository.InsertAsync(virtualFolder, autoSave: true);
            return ObjectMapper.Map<VirtualDiskFolder, VirtualFolderDto>(virtualFolder);
        }

        [Authorize(HolyBlessPermissions.VirtualFolders.Edit)]
        public async Task<VirtualFolderDto> UpdateAsync(int id, CreateUpdateVirtualFolderDto input)
        {
            var virtualFolder = await _virtualFolderRepository.GetAsync(id);
            ObjectMapper.Map(input, virtualFolder);
            virtualFolder = await _virtualFolderRepository.UpdateAsync(virtualFolder, true);
            return ObjectMapper.Map<VirtualDiskFolder, VirtualFolderDto>(virtualFolder);
        }

        [Authorize(HolyBlessPermissions.VirtualFolders.Delete)]
        public async Task DeleteAsync(int id)
        {
            var virtualFolder = await _virtualFolderRepository.GetAsync(id, includeDetails: true);
            if (virtualFolder.Children != null && virtualFolder.Children.Any())
            {
                throw new UserFriendlyException(L["CannotDeleteVirtualFolderWithChildren"]);
            }
            if (virtualFolder.FolderToBucketFiles != null && virtualFolder.FolderToBucketFiles.Any())
            {
                throw new UserFriendlyException(L["CannotDeleteVirtualFolderWithFiles"]);
            }
            await _virtualFolderRepository.DeleteAsync(id, true);
        }

        /// <summary>
        /// [Admin] Get a virtual folder by its ID. This method is used to retrieve a specific virtual folder's details.
        /// Usage: Pop up a dialog to show virtual folder details, or when editing a virtual folder.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<VirtualFolderDto> GetAsync(int id)
        {
            var virtualFolder = await _virtualFolderRepository.FirstOrDefaultAsync(x => x.Id == id);
            Check.NotNull(virtualFolder, nameof(virtualFolder));
            var virtualFolderDto = ObjectMapper.Map<VirtualDiskFolder, VirtualFolderDto>(virtualFolder);
            return virtualFolderDto;
        }

        public async Task<string> GetFolderTreeJson(int rootFolderId)
        {
            // Use TreeJsonSnapshotAppService which caches snapshots in-memory
            var json = await _treeSnapshotAppService.GetTreeJsonAsync(TreeType.VirtualDiskFolder, rootFolderId);
            return json ?? string.Empty;
        }

        /// <summary>
        /// [Admin] Get all virtual folders without pagination, filtered by language code.
        /// </summary>
        /// <param name="languageCode">Optional language code to filter virtual folders. If null, uses all languages.</param>
        /// <returns></returns>
        public async Task<List<VirtualFolderDto>> GetAllVirtualFoldersAsync(string? languageCode = null)
        {
            var queryable = await _virtualFolderRepository.GetQueryableAsync();
            var query = queryable
                .WhereIf(!string.IsNullOrEmpty(languageCode), x => x.LanguageCode != null && x.LanguageCode.ToLower() == languageCode!.ToLower())
                .OrderBy(x => x.Weight);
            var virtualFolders = await AsyncExecuter.ToListAsync(query);
            return ObjectMapper.Map<List<VirtualDiskFolder>, List<VirtualFolderDto>>(virtualFolders);
        }

        /// <summary>
        /// Usage Admin page: Link a virtual folder to a channel.
        /// </summary>
        /// <param name="channelId"></param>
        /// <param name="virtualFolderId"></param>
        /// <returns></returns>
        //[Authorize(HolyBlessPermissions.VirtualFolders.Default)]
        public async Task LinkToChannel(int channelId, int virtualFolderId)
        {
            var virtualFolder = await _virtualFolderRepository.GetAsync(virtualFolderId);
            virtualFolder.ChannelId = channelId;
            await _virtualFolderRepository.UpdateAsync(virtualFolder, true);
        }

        /// <summary>
        /// Usage Admin page: Link multiple virtual folders to a channel.
        /// </summary>
        /// <param name="channelId"></param>
        /// <param name="virtualFolderIds"></param>
        /// <returns></returns>
        //[Authorize(HolyBlessPermissions.VirtualFolders.Default)]
        public async Task LinkToChannel(int channelId, List<int> virtualFolderIds)
        {
            var virtualFolders = await _virtualFolderRepository.GetListAsync(x => virtualFolderIds.Contains(x.Id));
            foreach (var virtualFolder in virtualFolders)
            {
                virtualFolder.ChannelId = channelId;
            }
            await _virtualFolderRepository.UpdateManyAsync(virtualFolders, autoSave: true);
        }

        /// <summary>
        /// Admin page: Unlink a virtual folder from a channel (Set ChannelId to null)
        /// </summary>
        /// <param name="virtualFolderId"></param>
        /// <returns></returns>
        public async Task UnlinkFromChannel(int virtualFolderId)
        {
            var virtualFolder = await _virtualFolderRepository.GetAsync(virtualFolderId);
            virtualFolder.ChannelId = null;
            await _virtualFolderRepository.UpdateAsync(virtualFolder, true);
        }

        /// <summary>
        /// Admin page: Unlink multiple virtual folders from their channels (Set ChannelId to null)
        /// </summary>
        /// <param name="virtualFolderIds"></param>
        /// <returns></returns>
        public async Task UnlinkFromChannel(List<int> virtualFolderIds)
        {
            var virtualFolders = await _virtualFolderRepository.GetListAsync(x => virtualFolderIds.Contains(x.Id));
            foreach (var virtualFolder in virtualFolders)
            {
                virtualFolder.ChannelId = null;
            }
            await _virtualFolderRepository.UpdateManyAsync(virtualFolders, autoSave: true);
        }

        /// <summary>
        /// Move a virtual folder to a new parent and reorder by weight relative to a beforeId.
        /// </summary>
        /// <param name="folderId">The folder being moved</param>
        /// <param name="toParentId">The new parent folder id (nullable for root)</param>
        /// <param name="beforeId">The folder that the moved folder should be placed before. If null, places at the end.</param>
        //[Authorize(HolyBlessPermissions.VirtualFolders.Edit)]
        public async Task MoveVirtualFolderAsync(int folderId, int? toParentId, int? beforeId)
        {
            var folderEntity = await _virtualFolderRepository.GetAsync(folderId);
            
            // Determine the root folder ID to get the correct tree
            var rootFolderId = folderEntity.ParentFolderId;
            while (rootFolderId.HasValue)
            {
                var parentFolder = await _virtualFolderRepository.GetAsync(rootFolderId.Value);
                if (parentFolder.ParentFolderId.HasValue)
                {
                    rootFolderId = parentFolder.ParentFolderId;
                }
                else
                {
                    break;
                }
            }
            
            // Use the folder itself as root if no parent found
            rootFolderId = rootFolderId ?? folderId;
            
            var tree = await GetVirtualFolderTreeAsync(rootFolderId.Value);
            var allFolders = tree.Flatten(new List<VirtualFolderTreeDto>());

            var folder = allFolders.FirstOrDefault(f => f.Id == folderId);
            var toParent = toParentId.HasValue ? allFolders.FirstOrDefault(f => f.Id == toParentId.Value) : null;

            if (folder == null)
            {
                throw new UserFriendlyException(L["VirtualFolderNotFound"]);
            }

            // Validation: Cannot move under folder's own child folder (deep check)
            if (toParent != null)
            {
                var descendantIds = new HashSet<int>(folder.GetDescendantIds());
                if (descendantIds.Contains(toParent.Id))
                {
                    throw new UserFriendlyException(L["CannotMoveVirtualFolderUnderChildFolder"]);
                }
            }

            // Update ParentId logic
            folderEntity.ParentFolderId = toParentId;

            // Update Weight logic
            var queryable = await _virtualFolderRepository.GetQueryableAsync();
            
            if (beforeId.HasValue)
            {
                // Normal case: insert before a specific folder
                var beforeEntity = await _virtualFolderRepository.GetAsync(beforeId.Value);
                var insertWeight = beforeEntity.Weight;

                // Increase weight by +1 for siblings under the same parent of BeforeId with weight >= BeforeId's weight
                var siblingsToShift = await AsyncExecuter.ToListAsync(
                    queryable.Where(x => x.ParentFolderId == beforeEntity.ParentFolderId
                        && x.Id != folderId // Exclude the folder being moved
                        && x.Weight >= insertWeight)
                );

                foreach (var s in siblingsToShift)
                {
                    s.Weight += 1;
                    await _virtualFolderRepository.UpdateAsync(s, true);
                }

                // Set the moved folder's weight to the original weight of the 'before' folder
                folderEntity.Weight = insertWeight;
            }
            else
            {
                // beforeId is null: move to the last position
                // Find the highest weight among siblings under the target parent
                var siblings = await AsyncExecuter.ToListAsync(
                    queryable.Where(x => x.ParentFolderId == toParentId
                        && x.Id != folderId) // Exclude the folder being moved
                );

                var maxWeight = siblings.Any() ? siblings.Max(x => x.Weight) : 0;
                folderEntity.Weight = maxWeight + 1;
            }

            await _virtualFolderRepository.UpdateAsync(folderEntity, true);

            // Clear virtual folder tree cache so next read rebuilds the snapshot
            await _treeSnapshotAppService.ClearCacheAsync(HolyBless.Enums.TreeType.VirtualDiskFolder, rootFolderId);
        }
    }
}