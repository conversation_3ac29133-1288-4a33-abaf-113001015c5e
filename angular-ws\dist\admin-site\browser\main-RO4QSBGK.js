import{$a as Ie,<PERSON> as yo,<PERSON><PERSON> as <PERSON>r,<PERSON><PERSON> as <PERSON>r,<PERSON><PERSON> as <PERSON><PERSON>,<PERSON> as <PERSON>,<PERSON> as <PERSON>,<PERSON> as Ke,<PERSON> as Hr,<PERSON> as <PERSON>e,<PERSON> as Tr,<PERSON> as zr,<PERSON>a as Pr,<PERSON> as <PERSON>r,<PERSON> as <PERSON>,<PERSON> as <PERSON>r,<PERSON> as Mr,<PERSON>a as qe,W as Te,Wa as Or,X as xo,Xa as To,Y as Be,Ya as Ur,Za as jr,_a as Nr,a as $e,ab as Wr,bb as $r,c as we,cb as Vr,d as pr,db as Kr,e as _e,eb as be,fb as Qr,h as vo,hb as Ye,i as ko,ib as Xe,j as de,jb as Bo,k as Se,kb as qr,l as hr,m as mr,ma as Rr,mb as Yr,n as U,nb as Xr,o as me,ob as Gr,p as br,pb as Jr,q as Ve,qb as Zr,r as vr,rb as Ge,s as kr,sb as Io,t as yr,tb as et,u as xr,ub as ot,v as Cr,w as ue,x as wr,y as _r,z as Sr}from"./chunk-GXYB24H2.js";import{$ as Jo,$a as h,Ab as D,Bc as mo,Ca as te,Cc as bo,Ec as gr,F as Qo,Fb as C,G as S,Ga as er,Gb as _,H as qo,Ha as or,Hb as I,Ib as je,J as ge,Jb as Ne,Ka as rr,Kb as he,Kc as Dr,L as Yo,Lb as ae,Lc as wo,N as oe,Nc as _o,P as He,Pc as So,Qb as J,R as Xo,Rb as g,S as ze,Sb as lr,Tb as dr,Ub as se,Vb as go,Wb as W,X,Xa as fo,Xb as $,Ya as Oe,Yb as Z,Z as P,Zb as ce,_ as Go,_b as le,a as ee,aa as w,b as Uo,ba as re,cb as tr,d as lo,da as Pe,e as z,eb as xe,ec as We,fa as x,fb as ir,fc as ur,g as jo,ga as T,gc as V,ha as pe,hc as Ce,jb as ie,k as uo,kb as ne,mb as nr,n as No,na as F,nb as Ue,o as q,oa as H,oc as po,p as A,pb as k,pc as ho,q as Fe,ra as N,rc as O,sb as ar,t as Wo,ta as Me,uc as fr,v as $o,va as Zo,vb as E,w as Y,wb as u,x as Vo,xa as G,y as Ko,ya as De,yb as sr,zb as cr}from"./chunk-AKB6GSCA.js";var rt=[{path:"",redirectTo:"/channel",pathMatch:"full"},{path:"channel",loadComponent:()=>import("./chunk-S63XTH4C.js").then(r=>r.ChannelComponent)},{path:"folder",loadComponent:()=>import("./chunk-36KAJZVQ.js").then(r=>r.FolderComponent)},{path:"collection",loadComponent:()=>import("./chunk-5YM7VDOU.js").then(r=>r.CollectionComponent)},{path:"bucket-file",loadComponent:()=>import("./chunk-VJ4BUBCC.js").then(r=>r.BucketFileComponent)},{path:"chapter",loadComponent:()=>import("./chunk-JD2MV3VC.js").then(r=>r.ChapterComponent)},{path:"ebook",loadComponent:()=>import("./chunk-U3QIYIII.js").then(r=>r.EbookComponent)},{path:"album",loadComponent:()=>import("./chunk-AKY47YR4.js").then(r=>r.AlbumComponent)}];var un="@",fn=(()=>{class r{doc;delegate;zone;animationType;moduleImpl;_rendererFactoryPromise=null;scheduler=null;injector=T(Me);loadingSchedulerFn=T(gn,{optional:!0});_engine;constructor(e,o,t,n,a){this.doc=e,this.delegate=o,this.zone=t,this.animationType=n,this.moduleImpl=a}ngOnDestroy(){this._engine?.flush()}loadImpl(){let e=()=>this.moduleImpl??import("./chunk-22MOQ7FK.js").then(t=>t),o;return this.loadingSchedulerFn?o=this.loadingSchedulerFn(e):o=e(),o.catch(t=>{throw new Go(5300,!1)}).then(({\u0275createEngine:t,\u0275AnimationRendererFactory:n})=>{this._engine=t(this.animationType,this.doc);let a=new n(this.delegate,this._engine,this.zone);return this.delegate=a,a})}createRenderer(e,o){let t=this.delegate.createRenderer(e,o);if(t.\u0275type===0)return t;typeof t.throwOnSyntheticProps=="boolean"&&(t.throwOnSyntheticProps=!1);let n=new Ro(t);return o?.data?.animation&&!this._rendererFactoryPromise&&(this._rendererFactoryPromise=this.loadImpl()),this._rendererFactoryPromise?.then(a=>{let s=a.createRenderer(e,o);n.use(s),this.scheduler??=this.injector.get(Zo,null,{optional:!0}),this.scheduler?.notify(10)}).catch(a=>{n.use(t)}),n}begin(){this.delegate.begin?.()}end(){this.delegate.end?.()}whenRenderingDone(){return this.delegate.whenRenderingDone?.()??Promise.resolve()}componentReplaced(e){this._engine?.flush(),this.delegate.componentReplaced?.(e)}static \u0275fac=function(o){ir()};static \u0275prov=w({token:r,factory:r.\u0275fac})}return r})(),Ro=class{delegate;replay=[];\u0275type=1;constructor(i){this.delegate=i}use(i){if(this.delegate=i,this.replay!==null){for(let e of this.replay)e(i);this.replay=null}}get data(){return this.delegate.data}destroy(){this.replay=null,this.delegate.destroy()}createElement(i,e){return this.delegate.createElement(i,e)}createComment(i){return this.delegate.createComment(i)}createText(i){return this.delegate.createText(i)}get destroyNode(){return this.delegate.destroyNode}appendChild(i,e){this.delegate.appendChild(i,e)}insertBefore(i,e,o,t){this.delegate.insertBefore(i,e,o,t)}removeChild(i,e,o){this.delegate.removeChild(i,e,o)}selectRootElement(i,e){return this.delegate.selectRootElement(i,e)}parentNode(i){return this.delegate.parentNode(i)}nextSibling(i){return this.delegate.nextSibling(i)}setAttribute(i,e,o,t){this.delegate.setAttribute(i,e,o,t)}removeAttribute(i,e,o){this.delegate.removeAttribute(i,e,o)}addClass(i,e){this.delegate.addClass(i,e)}removeClass(i,e){this.delegate.removeClass(i,e)}setStyle(i,e,o,t){this.delegate.setStyle(i,e,o,t)}removeStyle(i,e,o){this.delegate.removeStyle(i,e,o)}setProperty(i,e,o){this.shouldReplay(e)&&this.replay.push(t=>t.setProperty(i,e,o)),this.delegate.setProperty(i,e,o)}setValue(i,e){this.delegate.setValue(i,e)}listen(i,e,o,t){return this.shouldReplay(e)&&this.replay.push(n=>n.listen(i,e,o,t)),this.delegate.listen(i,e,o,t)}shouldReplay(i){return this.replay!==null&&i.startsWith(un)}},gn=new Pe("");function tt(r="animations"){return rr("NgAsyncAnimations"),pe([{provide:tr,useFactory:(i,e,o)=>new fn(i,e,o,r),deps:[$e,hr,De]},{provide:or,useValue:r==="noop"?"NoopAnimations":"BrowserAnimations"}])}var Je=class{validateSignature(i){return Promise.resolve(null)}validateAtHash(i){return Promise.resolve(!0)}},Ze=class{};var Re=class{},pn=(()=>{class r extends Re{now(){return Date.now()}new(){return new Date}static{this.\u0275fac=(()=>{let e;return function(t){return(e||(e=N(r)))(t||r)}})()}static{this.\u0275prov=w({token:r,factory:r.\u0275fac})}}return r})();var eo=class{},ye=class{},hn=(()=>{class r{constructor(){this.data=new Map}getItem(e){return this.data.get(e)}removeItem(e){this.data.delete(e)}setItem(e,o){this.data.set(e,o)}static{this.\u0275fac=function(o){return new(o||r)}}static{this.\u0275prov=w({token:r,factory:r.\u0275fac})}}return r})();var Ae=class{constructor(i){this.type=i}},R=class extends Ae{constructor(i,e=null){super(i),this.info=e}},L=class extends Ae{constructor(i,e=null){super(i),this.info=e}},y=class extends Ae{constructor(i,e,o=null){super(i),this.reason=e,this.params=o}};function it(r){let i=r.replace(/-/g,"+").replace(/_/g,"/");return decodeURIComponent(atob(i).split("").map(function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)}).join(""))}function nt(r){return btoa(r).replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}var ke=class{constructor(i){this.clientId="",this.redirectUri="",this.postLogoutRedirectUri="",this.redirectUriAsPostLogoutRedirectUriFallback=!0,this.loginUrl="",this.scope="openid profile",this.resource="",this.rngUrl="",this.oidc=!0,this.requestAccessToken=!0,this.options=null,this.issuer="",this.logoutUrl="",this.clearHashAfterLogin=!0,this.tokenEndpoint=null,this.revocationEndpoint=null,this.customTokenParameters=[],this.userinfoEndpoint=null,this.responseType="",this.showDebugInformation=!1,this.silentRefreshRedirectUri="",this.silentRefreshMessagePrefix="",this.silentRefreshShowIFrame=!1,this.siletRefreshTimeout=1e3*20,this.silentRefreshTimeout=1e3*20,this.dummyClientSecret="",this.requireHttps="remoteOnly",this.strictDiscoveryDocumentValidation=!0,this.jwks=null,this.customQueryParams=null,this.silentRefreshIFrameName="angular-oauth-oidc-silent-refresh-iframe",this.timeoutFactor=.75,this.sessionChecksEnabled=!1,this.sessionCheckIntervall=3*1e3,this.sessionCheckIFrameUrl=null,this.sessionCheckIFrameName="angular-oauth-oidc-check-session-iframe",this.disableAtHashCheck=!1,this.skipSubjectCheck=!1,this.useIdTokenHintForSilentRefresh=!1,this.skipIssuerCheck=!1,this.nonceStateSeparator=";",this.useHttpBasicAuth=!1,this.decreaseExpirationBySec=0,this.waitForTokenInMsec=0,this.disablePKCE=!1,this.preserveRequestedRoute=!1,this.disableIdTokenTimer=!1,this.checkOrigin=!1,this.openUri=e=>{location.href=e},i&&Object.assign(this,i)}},fe=class{encodeKey(i){return encodeURIComponent(i)}encodeValue(i){return encodeURIComponent(i)}decodeKey(i){return decodeURIComponent(i)}decodeValue(i){return decodeURIComponent(i)}},oo=class{};var at=(()=>{class r{getHashFragmentParams(e){let o=e||window.location.hash;if(o=decodeURIComponent(o),o.indexOf("#")!==0)return{};let t=o.indexOf("?");return t>-1?o=o.substr(t+1):o=o.substr(1),this.parseQueryString(o)}parseQueryString(e){let o={},t,n,a,s,l,d;if(e===null)return o;let v=e.split("&");for(let c=0;c<v.length;c++)t=v[c],n=t.indexOf("="),n===-1?(a=t,s=null):(a=t.substr(0,n),s=t.substr(n+1)),l=decodeURIComponent(a),d=decodeURIComponent(s),l.substr(0,1)==="/"&&(l=l.substr(1)),o[l]=d;return o}static{this.\u0275fac=function(o){return new(o||r)}}static{this.\u0275prov=w({token:r,factory:r.\u0275fac})}}return r})(),st=32,mn=64,bn=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]);function Ao(r,i,e,o,t){let n,a,s,l,d,v,c,p,f,m,B,Q,j;for(;t>=64;){for(n=i[0],a=i[1],s=i[2],l=i[3],d=i[4],v=i[5],c=i[6],p=i[7],m=0;m<16;m++)B=o+m*4,r[m]=(e[B]&255)<<24|(e[B+1]&255)<<16|(e[B+2]&255)<<8|e[B+3]&255;for(m=16;m<64;m++)f=r[m-2],Q=(f>>>17|f<<15)^(f>>>19|f<<13)^f>>>10,f=r[m-15],j=(f>>>7|f<<25)^(f>>>18|f<<14)^f>>>3,r[m]=(Q+r[m-7]|0)+(j+r[m-16]|0);for(m=0;m<64;m++)Q=(((d>>>6|d<<26)^(d>>>11|d<<21)^(d>>>25|d<<7))+(d&v^~d&c)|0)+(p+(bn[m]+r[m]|0)|0)|0,j=((n>>>2|n<<30)^(n>>>13|n<<19)^(n>>>22|n<<10))+(n&a^n&s^a&s)|0,p=c,c=v,v=d,d=l+Q|0,l=s,s=a,a=n,n=Q+j|0;i[0]+=n,i[1]+=a,i[2]+=s,i[3]+=l,i[4]+=d,i[5]+=v,i[6]+=c,i[7]+=p,o+=64,t-=64}return o}var Eo=class{constructor(){this.digestLength=st,this.blockSize=mn,this.state=new Int32Array(8),this.temp=new Int32Array(64),this.buffer=new Uint8Array(128),this.bufferLength=0,this.bytesHashed=0,this.finished=!1,this.reset()}reset(){return this.state[0]=1779033703,this.state[1]=3144134277,this.state[2]=1013904242,this.state[3]=2773480762,this.state[4]=1359893119,this.state[5]=2600822924,this.state[6]=528734635,this.state[7]=1541459225,this.bufferLength=0,this.bytesHashed=0,this.finished=!1,this}clean(){for(let i=0;i<this.buffer.length;i++)this.buffer[i]=0;for(let i=0;i<this.temp.length;i++)this.temp[i]=0;this.reset()}update(i,e=i.length){if(this.finished)throw new Error("SHA256: can't update because hash was finished.");let o=0;if(this.bytesHashed+=e,this.bufferLength>0){for(;this.bufferLength<64&&e>0;)this.buffer[this.bufferLength++]=i[o++],e--;this.bufferLength===64&&(Ao(this.temp,this.state,this.buffer,0,64),this.bufferLength=0)}for(e>=64&&(o=Ao(this.temp,this.state,i,o,e),e%=64);e>0;)this.buffer[this.bufferLength++]=i[o++],e--;return this}finish(i){if(!this.finished){let e=this.bytesHashed,o=this.bufferLength,t=e/536870912|0,n=e<<3,a=e%64<56?64:128;this.buffer[o]=128;for(let s=o+1;s<a-8;s++)this.buffer[s]=0;this.buffer[a-8]=t>>>24&255,this.buffer[a-7]=t>>>16&255,this.buffer[a-6]=t>>>8&255,this.buffer[a-5]=t>>>0&255,this.buffer[a-4]=n>>>24&255,this.buffer[a-3]=n>>>16&255,this.buffer[a-2]=n>>>8&255,this.buffer[a-1]=n>>>0&255,Ao(this.temp,this.state,this.buffer,0,a),this.finished=!0}for(let e=0;e<8;e++)i[e*4+0]=this.state[e]>>>24&255,i[e*4+1]=this.state[e]>>>16&255,i[e*4+2]=this.state[e]>>>8&255,i[e*4+3]=this.state[e]>>>0&255;return this}digest(){let i=new Uint8Array(this.digestLength);return this.finish(i),i}_saveState(i){for(let e=0;e<this.state.length;e++)i[e]=this.state[e]}_restoreState(i,e){for(let o=0;o<this.state.length;o++)this.state[o]=i[o];this.bytesHashed=e,this.finished=!1,this.bufferLength=0}};function vn(r){let i=new Eo().update(r),e=i.digest();return i.clean(),e}var ns=new Uint8Array(st);var ro=class{};function kn(r){if(typeof r!="string")throw new TypeError("expected string");let i=r,e=new Uint8Array(i.length);for(let o=0;o<i.length;o++)e[o]=i.charCodeAt(o);return e}function yn(r){let i=[];for(let e=0;e<r.length;e++)i.push(String.fromCharCode(r[e]));return i.join("")}var xn=(()=>{class r{calcHash(e,o){return z(this,null,function*(){return yn(vn(kn(e)))})}toHashString2(e){let o="";for(let t of e)o+=String.fromCharCode(t);return o}toHashString(e){let o=new Uint8Array(e),t="";for(let n of o)t+=String.fromCharCode(n);return t}static{this.\u0275fac=function(o){return new(o||r)}}static{this.\u0275prov=w({token:r,factory:r.\u0275fac})}}return r})(),M=(()=>{class r extends ke{constructor(e,o,t,n,a,s,l,d,v,c){super(),this.ngZone=e,this.http=o,this.config=a,this.urlHelper=s,this.logger=l,this.crypto=d,this.dateTimeService=c,this.discoveryDocumentLoaded=!1,this.state="",this.eventsSubject=new uo,this.discoveryDocumentLoadedSubject=new uo,this.grantTypesSupported=[],this.inImplicitFlow=!1,this.saveNoncesInLocalStorage=!1,this.debug("angular-oauth2-oidc v10"),this.document=v,a||(a={}),this.discoveryDocumentLoaded$=this.discoveryDocumentLoadedSubject.asObservable(),this.events=this.eventsSubject.asObservable(),n&&(this.tokenValidationHandler=n),a&&this.configure(a);try{t?this.setStorage(t):typeof sessionStorage<"u"&&this.setStorage(sessionStorage)}catch(p){console.error("No OAuthStorage provided and cannot access default (sessionStorage).Consider providing a custom OAuthStorage implementation in your module.",p)}if(this.checkLocalStorageAccessable()){let p=window?.navigator?.userAgent;(p?.includes("MSIE ")||p?.includes("Trident"))&&(this.saveNoncesInLocalStorage=!0)}this.setupRefreshTimer()}checkLocalStorageAccessable(){if(typeof window>"u")return!1;let e="test";try{return typeof window.localStorage>"u"?!1:(localStorage.setItem(e,e),localStorage.removeItem(e),!0)}catch{return!1}}configure(e){Object.assign(this,new ke,e),this.config=Object.assign({},new ke,e),this.sessionChecksEnabled&&this.setupSessionCheck(),this.configChanged()}configChanged(){this.setupRefreshTimer()}restartSessionChecksIfStillLoggedIn(){this.hasValidIdToken()&&this.initSessionCheck()}restartRefreshTimerIfStillLoggedIn(){this.setupExpirationTimers()}setupSessionCheck(){this.events.pipe(S(e=>e.type==="token_received")).subscribe(()=>{this.initSessionCheck()})}setupAutomaticSilentRefresh(e={},o,t=!0){let n=!0;this.clearAutomaticRefreshTimer(),this.automaticRefreshSubscription=this.events.pipe(P(a=>{a.type==="token_received"?n=!0:a.type==="logout"&&(n=!1)}),S(a=>a.type==="token_expires"&&(o==null||o==="any"||a.info===o)),Yo(1e3)).subscribe(()=>{n&&this.refreshInternal(e,t).catch(()=>{this.debug("Automatic silent refresh did not work")})}),this.restartRefreshTimerIfStillLoggedIn()}refreshInternal(e,o){return!this.useSilentRefresh&&this.responseType==="code"?this.refreshToken():this.silentRefresh(e,o)}loadDiscoveryDocumentAndTryLogin(e=null){return this.loadDiscoveryDocument().then(()=>this.tryLogin(e))}loadDiscoveryDocumentAndLogin(e=null){return e=e||{},this.loadDiscoveryDocumentAndTryLogin(e).then(()=>{if(!this.hasValidIdToken()||!this.hasValidAccessToken()){let o=typeof e.state=="string"?e.state:"";return this.initLoginFlow(o),!1}else return!0})}debug(...e){this.showDebugInformation&&this.logger.debug(...e)}validateUrlFromDiscoveryDocument(e){let o=[],t=this.validateUrlForHttps(e),n=this.validateUrlAgainstIssuer(e);return t||o.push("https for all urls required. Also for urls received by discovery."),n||o.push("Every url in discovery document has to start with the issuer url.Also see property strictDiscoveryDocumentValidation."),o}validateUrlForHttps(e){if(!e)return!0;let o=e.toLowerCase();return this.requireHttps===!1||(o.match(/^http:\/\/localhost($|[:/])/)||o.match(/^http:\/\/localhost($|[:/])/))&&this.requireHttps==="remoteOnly"?!0:o.startsWith("https://")}assertUrlNotNullAndCorrectProtocol(e,o){if(!e)throw new Error(`'${o}' should not be null`);if(!this.validateUrlForHttps(e))throw new Error(`'${o}' must use HTTPS (with TLS), or config value for property 'requireHttps' must be set to 'false' and allow HTTP (without TLS).`)}validateUrlAgainstIssuer(e){return!this.strictDiscoveryDocumentValidation||!e?!0:e.toLowerCase().startsWith(this.issuer.toLowerCase())}setupRefreshTimer(){if(typeof window>"u"){this.debug("timer not supported on this plattform");return}(this.hasValidIdToken()||this.hasValidAccessToken())&&(this.clearAccessTokenTimer(),this.clearIdTokenTimer(),this.setupExpirationTimers()),this.tokenReceivedSubscription&&this.tokenReceivedSubscription.unsubscribe(),this.tokenReceivedSubscription=this.events.pipe(S(e=>e.type==="token_received")).subscribe(()=>{this.clearAccessTokenTimer(),this.clearIdTokenTimer(),this.setupExpirationTimers()})}setupExpirationTimers(){this.hasValidAccessToken()&&this.setupAccessTokenTimer(),!this.disableIdTokenTimer&&this.hasValidIdToken()&&this.setupIdTokenTimer()}setupAccessTokenTimer(){let e=this.getAccessTokenExpiration(),o=this.getAccessTokenStoredAt(),t=this.calcTimeout(o,e);this.ngZone.runOutsideAngular(()=>{this.accessTokenTimeoutSubscription=A(new L("token_expires","access_token")).pipe(He(t)).subscribe(n=>{this.ngZone.run(()=>{this.eventsSubject.next(n)})})})}setupIdTokenTimer(){let e=this.getIdTokenExpiration(),o=this.getIdTokenStoredAt(),t=this.calcTimeout(o,e);this.ngZone.runOutsideAngular(()=>{this.idTokenTimeoutSubscription=A(new L("token_expires","id_token")).pipe(He(t)).subscribe(n=>{this.ngZone.run(()=>{this.eventsSubject.next(n)})})})}stopAutomaticRefresh(){this.clearAccessTokenTimer(),this.clearIdTokenTimer(),this.clearAutomaticRefreshTimer()}clearAccessTokenTimer(){this.accessTokenTimeoutSubscription&&this.accessTokenTimeoutSubscription.unsubscribe()}clearIdTokenTimer(){this.idTokenTimeoutSubscription&&this.idTokenTimeoutSubscription.unsubscribe()}clearAutomaticRefreshTimer(){this.automaticRefreshSubscription&&this.automaticRefreshSubscription.unsubscribe()}calcTimeout(e,o){let t=this.dateTimeService.now(),n=(o-e)*this.timeoutFactor-(t-e),a=Math.max(0,n),s=2147483647;return a>s?s:a}setStorage(e){this._storage=e,this.configChanged()}loadDiscoveryDocument(e=null){return new Promise((o,t)=>{if(e||(e=this.issuer||"",e.endsWith("/")||(e+="/"),e+=".well-known/openid-configuration"),!this.validateUrlForHttps(e)){t("issuer  must use HTTPS (with TLS), or config value for property 'requireHttps' must be set to 'false' and allow HTTP (without TLS).");return}this.http.get(e).subscribe(n=>{if(!this.validateDiscoveryDocument(n)){this.eventsSubject.next(new y("discovery_document_validation_error",null)),t("discovery_document_validation_error");return}this.loginUrl=n.authorization_endpoint,this.logoutUrl=n.end_session_endpoint||this.logoutUrl,this.grantTypesSupported=n.grant_types_supported,this.issuer=n.issuer,this.tokenEndpoint=n.token_endpoint,this.userinfoEndpoint=n.userinfo_endpoint||this.userinfoEndpoint,this.jwksUri=n.jwks_uri,this.sessionCheckIFrameUrl=n.check_session_iframe||this.sessionCheckIFrameUrl,this.discoveryDocumentLoaded=!0,this.discoveryDocumentLoadedSubject.next(n),this.revocationEndpoint=n.revocation_endpoint||this.revocationEndpoint,this.sessionChecksEnabled&&this.restartSessionChecksIfStillLoggedIn(),this.loadJwks().then(a=>{let s={discoveryDocument:n,jwks:a},l=new R("discovery_document_loaded",s);this.eventsSubject.next(l),o(l)}).catch(a=>{this.eventsSubject.next(new y("discovery_document_load_error",a)),t(a)})},n=>{this.logger.error("error loading discovery document",n),this.eventsSubject.next(new y("discovery_document_load_error",n)),t(n)})})}loadJwks(){return new Promise((e,o)=>{this.jwksUri?this.http.get(this.jwksUri).subscribe(t=>{this.jwks=t,e(t)},t=>{this.logger.error("error loading jwks",t),this.eventsSubject.next(new y("jwks_load_error",t)),o(t)}):e(null)})}validateDiscoveryDocument(e){let o;return!this.skipIssuerCheck&&e.issuer!==this.issuer?(this.logger.error("invalid issuer in discovery document","expected: "+this.issuer,"current: "+e.issuer),!1):(o=this.validateUrlFromDiscoveryDocument(e.authorization_endpoint),o.length>0?(this.logger.error("error validating authorization_endpoint in discovery document",o),!1):(o=this.validateUrlFromDiscoveryDocument(e.end_session_endpoint),o.length>0?(this.logger.error("error validating end_session_endpoint in discovery document",o),!1):(o=this.validateUrlFromDiscoveryDocument(e.token_endpoint),o.length>0&&this.logger.error("error validating token_endpoint in discovery document",o),o=this.validateUrlFromDiscoveryDocument(e.revocation_endpoint),o.length>0&&this.logger.error("error validating revocation_endpoint in discovery document",o),o=this.validateUrlFromDiscoveryDocument(e.userinfo_endpoint),o.length>0?(this.logger.error("error validating userinfo_endpoint in discovery document",o),!1):(o=this.validateUrlFromDiscoveryDocument(e.jwks_uri),o.length>0?(this.logger.error("error validating jwks_uri in discovery document",o),!1):(this.sessionChecksEnabled&&!e.check_session_iframe&&this.logger.warn("sessionChecksEnabled is activated but discovery document does not contain a check_session_iframe field"),!0)))))}fetchTokenUsingPasswordFlowAndLoadUserProfile(e,o,t=new U){return this.fetchTokenUsingPasswordFlow(e,o,t).then(()=>this.loadUserProfile())}loadUserProfile(){if(!this.hasValidAccessToken())throw new Error("Can not load User Profile without access_token");if(!this.validateUrlForHttps(this.userinfoEndpoint))throw new Error("userinfoEndpoint must use HTTPS (with TLS), or config value for property 'requireHttps' must be set to 'false' and allow HTTP (without TLS).");return new Promise((e,o)=>{let t=new U().set("Authorization","Bearer "+this.getAccessToken());this.http.get(this.userinfoEndpoint,{headers:t,observe:"response",responseType:"text"}).subscribe(n=>{if(this.debug("userinfo received",JSON.stringify(n)),n.headers.get("content-type").startsWith("application/json")){let a=JSON.parse(n.body),s=this.getIdentityClaims()||{};if(!this.skipSubjectCheck&&this.oidc&&(!s.sub||a.sub!==s.sub)){o(`if property oidc is true, the received user-id (sub) has to be the user-id of the user that has logged in with oidc.
if you are not using oidc but just oauth2 password flow set oidc to false`);return}a=Object.assign({},s,a),this._storage.setItem("id_token_claims_obj",JSON.stringify(a)),this.eventsSubject.next(new R("user_profile_loaded")),e({info:a})}else this.debug("userinfo is not JSON, treating it as JWE/JWS"),this.eventsSubject.next(new R("user_profile_loaded")),e(JSON.parse(n.body))},n=>{this.logger.error("error loading user info",n),this.eventsSubject.next(new y("user_profile_load_error",n)),o(n)})})}fetchTokenUsingPasswordFlow(e,o,t=new U){let n={username:e,password:o};return this.fetchTokenUsingGrant("password",n,t)}fetchTokenUsingGrant(e,o,t=new U){this.assertUrlNotNullAndCorrectProtocol(this.tokenEndpoint,"tokenEndpoint");let n=new me({encoder:new fe}).set("grant_type",e).set("scope",this.scope);if(this.useHttpBasicAuth){let a=btoa(`${this.clientId}:${this.dummyClientSecret}`);t=t.set("Authorization","Basic "+a)}if(this.useHttpBasicAuth||(n=n.set("client_id",this.clientId)),!this.useHttpBasicAuth&&this.dummyClientSecret&&(n=n.set("client_secret",this.dummyClientSecret)),this.customQueryParams)for(let a of Object.getOwnPropertyNames(this.customQueryParams))n=n.set(a,this.customQueryParams[a]);for(let a of Object.keys(o))n=n.set(a,o[a]);return t=t.set("Content-Type","application/x-www-form-urlencoded"),new Promise((a,s)=>{this.http.post(this.tokenEndpoint,n,{headers:t}).subscribe(l=>{this.debug("tokenResponse",l),this.storeAccessTokenResponse(l.access_token,l.refresh_token,l.expires_in||this.fallbackAccessTokenExpirationTimeInSec,l.scope,this.extractRecognizedCustomParameters(l)),this.oidc&&l.id_token&&this.processIdToken(l.id_token,l.access_token).then(d=>{this.storeIdToken(d),a(l)}),this.eventsSubject.next(new R("token_received")),a(l)},l=>{this.logger.error("Error performing ${grantType} flow",l),this.eventsSubject.next(new y("token_error",l)),s(l)})})}refreshToken(){return this.assertUrlNotNullAndCorrectProtocol(this.tokenEndpoint,"tokenEndpoint"),new Promise((e,o)=>{let t=new me({encoder:new fe}).set("grant_type","refresh_token").set("scope",this.scope).set("refresh_token",this._storage.getItem("refresh_token")),n=new U().set("Content-Type","application/x-www-form-urlencoded");if(this.useHttpBasicAuth){let a=btoa(`${this.clientId}:${this.dummyClientSecret}`);n=n.set("Authorization","Basic "+a)}if(this.useHttpBasicAuth||(t=t.set("client_id",this.clientId)),!this.useHttpBasicAuth&&this.dummyClientSecret&&(t=t.set("client_secret",this.dummyClientSecret)),this.customQueryParams)for(let a of Object.getOwnPropertyNames(this.customQueryParams))t=t.set(a,this.customQueryParams[a]);this.http.post(this.tokenEndpoint,t,{headers:n}).pipe(X(a=>this.oidc&&a.id_token?q(this.processIdToken(a.id_token,a.access_token,!0)).pipe(P(s=>this.storeIdToken(s)),Y(()=>a)):A(a))).subscribe(a=>{this.debug("refresh tokenResponse",a),this.storeAccessTokenResponse(a.access_token,a.refresh_token,a.expires_in||this.fallbackAccessTokenExpirationTimeInSec,a.scope,this.extractRecognizedCustomParameters(a)),this.eventsSubject.next(new R("token_received")),this.eventsSubject.next(new R("token_refreshed")),e(a)},a=>{this.logger.error("Error refreshing token",a),this.eventsSubject.next(new y("token_refresh_error",a)),o(a)})})}removeSilentRefreshEventListener(){this.silentRefreshPostMessageEventListener&&(window.removeEventListener("message",this.silentRefreshPostMessageEventListener),this.silentRefreshPostMessageEventListener=null)}setupSilentRefreshEventListener(){this.removeSilentRefreshEventListener(),this.silentRefreshPostMessageEventListener=e=>{let o=this.processMessageEventMessage(e);this.checkOrigin&&e.origin!==location.origin&&console.error("wrong origin requested silent refresh!"),this.tryLogin({customHashFragment:o,preventClearHashAfterLogin:!0,customRedirectUri:this.silentRefreshRedirectUri||this.redirectUri}).catch(t=>this.debug("tryLogin during silent refresh failed",t))},window.addEventListener("message",this.silentRefreshPostMessageEventListener)}silentRefresh(e={},o=!0){let t=this.getIdentityClaims()||{};if(this.useIdTokenHintForSilentRefresh&&this.hasValidIdToken()&&(e.id_token_hint=this.getIdToken()),!this.validateUrlForHttps(this.loginUrl))throw new Error("loginUrl  must use HTTPS (with TLS), or config value for property 'requireHttps' must be set to 'false' and allow HTTP (without TLS).");if(typeof this.document>"u")throw new Error("silent refresh is not supported on this platform");let n=this.document.getElementById(this.silentRefreshIFrameName);n&&this.document.body.removeChild(n),this.silentRefreshSubject=t.sub;let a=this.document.createElement("iframe");a.id=this.silentRefreshIFrameName,this.setupSilentRefreshEventListener();let s=this.silentRefreshRedirectUri||this.redirectUri;this.createLoginUrl(null,null,s,o,e).then(c=>{a.setAttribute("src",c),this.silentRefreshShowIFrame||(a.style.display="none"),this.document.body.appendChild(a)});let l=this.events.pipe(S(c=>c instanceof y),ze()),d=this.events.pipe(S(c=>c.type==="token_received"),ze()),v=A(new y("silent_refresh_timeout",null)).pipe(He(this.silentRefreshTimeout));return qo([l,d,v]).pipe(Y(c=>{if(c instanceof y)throw c.type==="silent_refresh_timeout"?this.eventsSubject.next(c):(c=new y("silent_refresh_error",c),this.eventsSubject.next(c)),c;return c.type==="token_received"&&(c=new R("silently_refreshed"),this.eventsSubject.next(c)),c})).toPromise()}initImplicitFlowInPopup(e){return this.initLoginFlowInPopup(e)}initLoginFlowInPopup(e){return e=e||{},this.createLoginUrl(null,null,this.silentRefreshRedirectUri,!1,{display:"popup"}).then(o=>new Promise((t,n)=>{let s=null;e.windowRef?e.windowRef&&!e.windowRef.closed&&(s=e.windowRef,s.location.href=o):s=window.open(o,"ngx-oauth2-oidc-login",this.calculatePopupFeatures(e));let l,d=m=>{this.tryLogin({customHashFragment:m,preventClearHashAfterLogin:!0,customRedirectUri:this.silentRefreshRedirectUri}).then(()=>{c(),t(!0)},B=>{c(),n(B)})},v=()=>{(!s||s.closed)&&(c(),n(new y("popup_closed",{})))};s?l=window.setInterval(v,500):n(new y("popup_blocked",{}));let c=()=>{window.clearInterval(l),window.removeEventListener("storage",f),window.removeEventListener("message",p),s!==null&&s.close(),s=null},p=m=>{let B=this.processMessageEventMessage(m);B&&B!==null?(window.removeEventListener("storage",f),d(B)):console.log("false event firing")},f=m=>{m.key==="auth_hash"&&(window.removeEventListener("message",p),d(m.newValue))};window.addEventListener("message",p),window.addEventListener("storage",f)}))}calculatePopupFeatures(e){let o=e.height||470,t=e.width||500,n=window.screenLeft+(window.outerWidth-t)/2,a=window.screenTop+(window.outerHeight-o)/2;return`location=no,toolbar=no,width=${t},height=${o},top=${a},left=${n}`}processMessageEventMessage(e){let o="#";if(this.silentRefreshMessagePrefix&&(o+=this.silentRefreshMessagePrefix),!e||!e.data||typeof e.data!="string")return;let t=e.data;if(t.startsWith(o))return"#"+t.substr(o.length)}canPerformSessionCheck(){return this.sessionChecksEnabled?this.sessionCheckIFrameUrl?this.getSessionState()?!(typeof this.document>"u"):(console.warn("sessionChecksEnabled is activated but there is no session_state"),!1):(console.warn("sessionChecksEnabled is activated but there is no sessionCheckIFrameUrl"),!1):!1}setupSessionCheckEventListener(){this.removeSessionCheckEventListener(),this.sessionCheckEventListener=e=>{let o=e.origin.toLowerCase(),t=this.issuer.toLowerCase();if(this.debug("sessionCheckEventListener"),!t.startsWith(o)){this.debug("sessionCheckEventListener","wrong origin",o,"expected",t,"event",e);return}switch(e.data){case"unchanged":this.ngZone.run(()=>{this.handleSessionUnchanged()});break;case"changed":this.ngZone.run(()=>{this.handleSessionChange()});break;case"error":this.ngZone.run(()=>{this.handleSessionError()});break}this.debug("got info from session check inframe",e)},this.ngZone.runOutsideAngular(()=>{window.addEventListener("message",this.sessionCheckEventListener)})}handleSessionUnchanged(){this.debug("session check","session unchanged"),this.eventsSubject.next(new L("session_unchanged"))}handleSessionChange(){this.eventsSubject.next(new L("session_changed")),this.stopSessionCheckTimer(),!this.useSilentRefresh&&this.responseType==="code"?this.refreshToken().then(()=>{this.debug("token refresh after session change worked")}).catch(()=>{this.debug("token refresh did not work after session changed"),this.eventsSubject.next(new L("session_terminated")),this.logOut(!0)}):this.silentRefreshRedirectUri?(this.silentRefresh().catch(()=>this.debug("silent refresh failed after session changed")),this.waitForSilentRefreshAfterSessionChange()):(this.eventsSubject.next(new L("session_terminated")),this.logOut(!0))}waitForSilentRefreshAfterSessionChange(){this.events.pipe(S(e=>e.type==="silently_refreshed"||e.type==="silent_refresh_timeout"||e.type==="silent_refresh_error"),ze()).subscribe(e=>{e.type!=="silently_refreshed"&&(this.debug("silent refresh did not work after session changed"),this.eventsSubject.next(new L("session_terminated")),this.logOut(!0))})}handleSessionError(){this.stopSessionCheckTimer(),this.eventsSubject.next(new L("session_error"))}removeSessionCheckEventListener(){this.sessionCheckEventListener&&(window.removeEventListener("message",this.sessionCheckEventListener),this.sessionCheckEventListener=null)}initSessionCheck(){if(!this.canPerformSessionCheck())return;let e=this.document.getElementById(this.sessionCheckIFrameName);e&&this.document.body.removeChild(e);let o=this.document.createElement("iframe");o.id=this.sessionCheckIFrameName,this.setupSessionCheckEventListener();let t=this.sessionCheckIFrameUrl;o.setAttribute("src",t),o.style.display="none",this.document.body.appendChild(o),this.startSessionCheckTimer()}startSessionCheckTimer(){this.stopSessionCheckTimer(),this.ngZone.runOutsideAngular(()=>{this.sessionCheckTimer=setInterval(this.checkSession.bind(this),this.sessionCheckIntervall)})}stopSessionCheckTimer(){this.sessionCheckTimer&&(clearInterval(this.sessionCheckTimer),this.sessionCheckTimer=null)}checkSession(){let e=this.document.getElementById(this.sessionCheckIFrameName);e||this.logger.warn("checkSession did not find iframe",this.sessionCheckIFrameName);let o=this.getSessionState();o||this.stopSessionCheckTimer();let t=this.clientId+" "+o;e.contentWindow.postMessage(t,this.issuer)}createLoginUrl(){return z(this,arguments,function*(e="",o="",t="",n=!1,a={}){let s=this,l;t?l=t:l=this.redirectUri;let d=yield this.createAndSaveNonce();if(e?e=d+this.config.nonceStateSeparator+encodeURIComponent(e):e=d,!this.requestAccessToken&&!this.oidc)throw new Error("Either requestAccessToken or oidc or both must be true");this.config.responseType?this.responseType=this.config.responseType:this.oidc&&this.requestAccessToken?this.responseType="id_token token":this.oidc&&!this.requestAccessToken?this.responseType="id_token":this.responseType="token";let v=s.loginUrl.indexOf("?")>-1?"&":"?",c=s.scope;this.oidc&&!c.match(/(^|\s)openid($|\s)/)&&(c="openid "+c);let p=s.loginUrl+v+"response_type="+encodeURIComponent(s.responseType)+"&client_id="+encodeURIComponent(s.clientId)+"&state="+encodeURIComponent(e)+"&redirect_uri="+encodeURIComponent(l)+"&scope="+encodeURIComponent(c);if(this.responseType.includes("code")&&!this.disablePKCE){let[f,m]=yield this.createChallangeVerifierPairForPKCE();this.saveNoncesInLocalStorage&&typeof window.localStorage<"u"?localStorage.setItem("PKCE_verifier",m):this._storage.setItem("PKCE_verifier",m),p+="&code_challenge="+f,p+="&code_challenge_method=S256"}o&&(p+="&login_hint="+encodeURIComponent(o)),s.resource&&(p+="&resource="+encodeURIComponent(s.resource)),s.oidc&&(p+="&nonce="+encodeURIComponent(d)),n&&(p+="&prompt=none");for(let f of Object.keys(a))p+="&"+encodeURIComponent(f)+"="+encodeURIComponent(a[f]);if(this.customQueryParams)for(let f of Object.getOwnPropertyNames(this.customQueryParams))p+="&"+f+"="+encodeURIComponent(this.customQueryParams[f]);return p})}initImplicitFlowInternal(e="",o=""){if(this.inImplicitFlow)return;if(this.inImplicitFlow=!0,!this.validateUrlForHttps(this.loginUrl))throw new Error("loginUrl  must use HTTPS (with TLS), or config value for property 'requireHttps' must be set to 'false' and allow HTTP (without TLS).");let t={},n=null;typeof o=="string"?n=o:typeof o=="object"&&(t=o),this.createLoginUrl(e,n,null,!1,t).then(this.config.openUri).catch(a=>{console.error("Error in initImplicitFlow",a),this.inImplicitFlow=!1})}initImplicitFlow(e="",o=""){this.loginUrl!==""?this.initImplicitFlowInternal(e,o):this.events.pipe(S(t=>t.type==="discovery_document_loaded")).subscribe(()=>this.initImplicitFlowInternal(e,o))}resetImplicitFlow(){this.inImplicitFlow=!1}callOnTokenReceivedIfExists(e){let o=this;if(e.onTokenReceived){let t={idClaims:o.getIdentityClaims(),idToken:o.getIdToken(),accessToken:o.getAccessToken(),state:o.state};e.onTokenReceived(t)}}storeAccessTokenResponse(e,o,t,n,a){if(this._storage.setItem("access_token",e),n&&!Array.isArray(n)?this._storage.setItem("granted_scopes",JSON.stringify(n.split(" "))):n&&Array.isArray(n)&&this._storage.setItem("granted_scopes",JSON.stringify(n)),this._storage.setItem("access_token_stored_at",""+this.dateTimeService.now()),t){let s=t*1e3,d=this.dateTimeService.new().getTime()+s;this._storage.setItem("expires_at",""+d)}o&&this._storage.setItem("refresh_token",o),a&&a.forEach((s,l)=>{this._storage.setItem(l,s)})}tryLogin(e=null){return this.config.responseType==="code"?this.tryLoginCodeFlow(e).then(()=>!0):this.tryLoginImplicitFlow(e)}parseQueryString(e){return!e||e.length===0?{}:(e.charAt(0)==="?"&&(e=e.substr(1)),this.urlHelper.parseQueryString(e))}tryLoginCodeFlow(e=null){return z(this,null,function*(){e=e||{};let o=e.customHashFragment?e.customHashFragment.substring(1):window.location.search,t=this.getCodePartsFromUrl(o),n=t.code,a=t.state,s=t.session_state;if(!e.preventClearHashAfterLogin){let v=location.origin+location.pathname+location.search.replace(/code=[^&$]*/,"").replace(/scope=[^&$]*/,"").replace(/state=[^&$]*/,"").replace(/session_state=[^&$]*/,"").replace(/^\?&/,"?").replace(/&$/,"").replace(/^\?$/,"").replace(/&+/g,"&").replace(/\?&/,"?").replace(/\?$/,"")+location.hash;history.replaceState(null,window.name,v)}let[l,d]=this.parseState(a);if(this.state=d,t.error){this.debug("error trying to login"),this.handleLoginError(e,t);let v=new y("code_error",{},t);return this.eventsSubject.next(v),Promise.reject(v)}if(!e.disableNonceCheck){if(!l)return this.saveRequestedRoute(),Promise.resolve();if(!e.disableOAuth2StateCheck&&!this.validateNonce(l)){let c=new y("invalid_nonce_in_state",null);return this.eventsSubject.next(c),Promise.reject(c)}}return this.storeSessionState(s),n&&(yield this.getTokenFromCode(n,e),this.restoreRequestedRoute()),Promise.resolve()})}saveRequestedRoute(){this.config.preserveRequestedRoute&&this._storage.setItem("requested_route",window.location.pathname+window.location.search)}restoreRequestedRoute(){let e=this._storage.getItem("requested_route");e&&history.replaceState(null,"",window.location.origin+e)}getCodePartsFromUrl(e){return!e||e.length===0?this.urlHelper.getHashFragmentParams():(e.charAt(0)==="?"&&(e=e.substr(1)),this.urlHelper.parseQueryString(e))}getTokenFromCode(e,o){let t=new me({encoder:new fe}).set("grant_type","authorization_code").set("code",e).set("redirect_uri",o.customRedirectUri||this.redirectUri);if(!this.disablePKCE){let n;this.saveNoncesInLocalStorage&&typeof window.localStorage<"u"?n=localStorage.getItem("PKCE_verifier"):n=this._storage.getItem("PKCE_verifier"),n?t=t.set("code_verifier",n):console.warn("No PKCE verifier found in oauth storage!")}return this.fetchAndProcessToken(t,o)}fetchAndProcessToken(e,o){o=o||{},this.assertUrlNotNullAndCorrectProtocol(this.tokenEndpoint,"tokenEndpoint");let t=new U().set("Content-Type","application/x-www-form-urlencoded");if(this.useHttpBasicAuth){let n=btoa(`${this.clientId}:${this.dummyClientSecret}`);t=t.set("Authorization","Basic "+n)}return this.useHttpBasicAuth||(e=e.set("client_id",this.clientId)),!this.useHttpBasicAuth&&this.dummyClientSecret&&(e=e.set("client_secret",this.dummyClientSecret)),new Promise((n,a)=>{if(this.customQueryParams)for(let s of Object.getOwnPropertyNames(this.customQueryParams))e=e.set(s,this.customQueryParams[s]);this.http.post(this.tokenEndpoint,e,{headers:t}).subscribe(s=>{this.debug("refresh tokenResponse",s),this.storeAccessTokenResponse(s.access_token,s.refresh_token,s.expires_in||this.fallbackAccessTokenExpirationTimeInSec,s.scope,this.extractRecognizedCustomParameters(s)),this.oidc&&s.id_token?this.processIdToken(s.id_token,s.access_token,o.disableNonceCheck).then(l=>{this.storeIdToken(l),this.eventsSubject.next(new R("token_received")),this.eventsSubject.next(new R("token_refreshed")),n(s)}).catch(l=>{this.eventsSubject.next(new y("token_validation_error",l)),console.error("Error validating tokens"),console.error(l),a(l)}):(this.eventsSubject.next(new R("token_received")),this.eventsSubject.next(new R("token_refreshed")),n(s))},s=>{console.error("Error getting token",s),this.eventsSubject.next(new y("token_refresh_error",s)),a(s)})})}tryLoginImplicitFlow(e=null){e=e||{};let o;e.customHashFragment?o=this.urlHelper.getHashFragmentParams(e.customHashFragment):o=this.urlHelper.getHashFragmentParams(),this.debug("parsed url",o);let t=o.state,[n,a]=this.parseState(t);if(this.state=a,o.error){this.debug("error trying to login"),this.handleLoginError(e,o);let c=new y("token_error",{},o);return this.eventsSubject.next(c),Promise.reject(c)}let s=o.access_token,l=o.id_token,d=o.session_state,v=o.scope;if(!this.requestAccessToken&&!this.oidc)return Promise.reject("Either requestAccessToken or oidc (or both) must be true.");if(this.requestAccessToken&&!s||this.requestAccessToken&&!e.disableOAuth2StateCheck&&!t||this.oidc&&!l)return Promise.resolve(!1);if(this.sessionChecksEnabled&&!d&&this.logger.warn("session checks (Session Status Change Notification) were activated in the configuration but the id_token does not contain a session_state claim"),this.requestAccessToken&&!e.disableNonceCheck&&!this.validateNonce(n)){let p=new y("invalid_nonce_in_state",null);return this.eventsSubject.next(p),Promise.reject(p)}return this.requestAccessToken&&this.storeAccessTokenResponse(s,null,o.expires_in||this.fallbackAccessTokenExpirationTimeInSec,v),this.oidc?this.processIdToken(l,s,e.disableNonceCheck).then(c=>e.validationHandler?e.validationHandler({accessToken:s,idClaims:c.idTokenClaims,idToken:c.idToken,state:t}).then(()=>c):c).then(c=>(this.storeIdToken(c),this.storeSessionState(d),this.clearHashAfterLogin&&!e.preventClearHashAfterLogin&&this.clearLocationHash(),this.eventsSubject.next(new R("token_received")),this.callOnTokenReceivedIfExists(e),this.inImplicitFlow=!1,!0)).catch(c=>(this.eventsSubject.next(new y("token_validation_error",c)),this.logger.error("Error validating tokens"),this.logger.error(c),Promise.reject(c))):(this.eventsSubject.next(new R("token_received")),this.clearHashAfterLogin&&!e.preventClearHashAfterLogin&&this.clearLocationHash(),this.callOnTokenReceivedIfExists(e),Promise.resolve(!0))}parseState(e){let o=e,t="";if(e){let n=e.indexOf(this.config.nonceStateSeparator);n>-1&&(o=e.substr(0,n),t=e.substr(n+this.config.nonceStateSeparator.length))}return[o,t]}validateNonce(e){let o;return this.saveNoncesInLocalStorage&&typeof window.localStorage<"u"?o=localStorage.getItem("nonce"):o=this._storage.getItem("nonce"),o!==e?(console.error("Validating access_token failed, wrong state/nonce.",o,e),!1):!0}storeIdToken(e){this._storage.setItem("id_token",e.idToken),this._storage.setItem("id_token_claims_obj",e.idTokenClaimsJson),this._storage.setItem("id_token_expires_at",""+e.idTokenExpiresAt),this._storage.setItem("id_token_stored_at",""+this.dateTimeService.now())}storeSessionState(e){this._storage.setItem("session_state",e)}getSessionState(){return this._storage.getItem("session_state")}handleLoginError(e,o){e.onLoginError&&e.onLoginError(o),this.clearHashAfterLogin&&!e.preventClearHashAfterLogin&&this.clearLocationHash()}getClockSkewInMsec(e=6e5){return!this.clockSkewInSec&&this.clockSkewInSec!==0?e:this.clockSkewInSec*1e3}processIdToken(e,o,t=!1){let n=e.split("."),a=this.padBase64(n[0]),s=it(a),l=JSON.parse(s),d=this.padBase64(n[1]),v=it(d),c=JSON.parse(v),p;if(this.saveNoncesInLocalStorage&&typeof window.localStorage<"u"?p=localStorage.getItem("nonce"):p=this._storage.getItem("nonce"),Array.isArray(c.aud)){if(c.aud.every(b=>b!==this.clientId)){let b="Wrong audience: "+c.aud.join(",");return this.logger.warn(b),Promise.reject(b)}}else if(c.aud!==this.clientId){let b="Wrong audience: "+c.aud;return this.logger.warn(b),Promise.reject(b)}if(!c.sub){let b="No sub claim in id_token";return this.logger.warn(b),Promise.reject(b)}if(this.sessionChecksEnabled&&this.silentRefreshSubject&&this.silentRefreshSubject!==c.sub){let b=`After refreshing, we got an id_token for another user (sub). Expected sub: ${this.silentRefreshSubject}, received sub: ${c.sub}`;return this.logger.warn(b),Promise.reject(b)}if(!c.iat){let b="No iat claim in id_token";return this.logger.warn(b),Promise.reject(b)}if(!this.skipIssuerCheck&&c.iss!==this.issuer){let b="Wrong issuer: "+c.iss;return this.logger.warn(b),Promise.reject(b)}if(!t&&c.nonce!==p){let b="Wrong nonce: "+c.nonce;return this.logger.warn(b),Promise.reject(b)}if(Object.prototype.hasOwnProperty.call(this,"responseType")&&(this.responseType==="code"||this.responseType==="id_token")&&(this.disableAtHashCheck=!0),!this.disableAtHashCheck&&this.requestAccessToken&&!c.at_hash){let b="An at_hash is needed!";return this.logger.warn(b),Promise.reject(b)}let f=this.dateTimeService.now(),m=c.iat*1e3,B=c.exp*1e3,Q=this.getClockSkewInMsec();if(m-Q>=f||B+Q-this.decreaseExpirationBySec<=f){let b="Token has expired";return console.error(b),console.error({now:f,issuedAtMSec:m,expiresAtMSec:B}),Promise.reject(b)}let j={accessToken:o,idToken:e,jwks:this.jwks,idTokenClaims:c,idTokenHeader:l,loadKeys:()=>this.loadJwks()};return this.disableAtHashCheck?this.checkSignature(j).then(()=>({idToken:e,idTokenClaims:c,idTokenClaimsJson:v,idTokenHeader:l,idTokenHeaderJson:s,idTokenExpiresAt:B})):this.checkAtHash(j).then(b=>{if(!this.disableAtHashCheck&&this.requestAccessToken&&!b){let Le="Wrong at_hash";return this.logger.warn(Le),Promise.reject(Le)}return this.checkSignature(j).then(()=>{let Le=!this.disableAtHashCheck,Do={idToken:e,idTokenClaims:c,idTokenClaimsJson:v,idTokenHeader:l,idTokenHeaderJson:s,idTokenExpiresAt:B};return Le?this.checkAtHash(j).then(dn=>{if(this.requestAccessToken&&!dn){let Oo="Wrong at_hash";return this.logger.warn(Oo),Promise.reject(Oo)}else return Do}):Do})})}getIdentityClaims(){let e=this._storage.getItem("id_token_claims_obj");return e?JSON.parse(e):null}getGrantedScopes(){let e=this._storage.getItem("granted_scopes");return e?JSON.parse(e):null}getIdToken(){return this._storage?this._storage.getItem("id_token"):null}padBase64(e){for(;e.length%4!==0;)e+="=";return e}getAccessToken(){return this._storage?this._storage.getItem("access_token"):null}getRefreshToken(){return this._storage?this._storage.getItem("refresh_token"):null}getAccessTokenExpiration(){return this._storage.getItem("expires_at")?parseInt(this._storage.getItem("expires_at"),10):null}getAccessTokenStoredAt(){return parseInt(this._storage.getItem("access_token_stored_at"),10)}getIdTokenStoredAt(){return parseInt(this._storage.getItem("id_token_stored_at"),10)}getIdTokenExpiration(){return this._storage.getItem("id_token_expires_at")?parseInt(this._storage.getItem("id_token_expires_at"),10):null}hasValidAccessToken(){if(this.getAccessToken()){let e=this._storage.getItem("expires_at"),o=this.dateTimeService.new();return!(e&&parseInt(e,10)-this.decreaseExpirationBySec<o.getTime()-this.getClockSkewInMsec())}return!1}hasValidIdToken(){if(this.getIdToken()){let e=this._storage.getItem("id_token_expires_at"),o=this.dateTimeService.new();return!(e&&parseInt(e,10)-this.decreaseExpirationBySec<o.getTime()-this.getClockSkewInMsec())}return!1}getCustomTokenResponseProperty(e){return this._storage&&this.config.customTokenParameters&&this.config.customTokenParameters.indexOf(e)>=0&&this._storage.getItem(e)!==null?JSON.parse(this._storage.getItem(e)):null}authorizationHeader(){return"Bearer "+this.getAccessToken()}logOut(e={},o=""){let t=!1;typeof e=="boolean"&&(t=e,e={});let n=this.getIdToken();if(this._storage.removeItem("access_token"),this._storage.removeItem("id_token"),this._storage.removeItem("refresh_token"),this.saveNoncesInLocalStorage?(localStorage.removeItem("nonce"),localStorage.removeItem("PKCE_verifier")):(this._storage.removeItem("nonce"),this._storage.removeItem("PKCE_verifier")),this._storage.removeItem("expires_at"),this._storage.removeItem("id_token_claims_obj"),this._storage.removeItem("id_token_expires_at"),this._storage.removeItem("id_token_stored_at"),this._storage.removeItem("access_token_stored_at"),this._storage.removeItem("granted_scopes"),this._storage.removeItem("session_state"),this.config.customTokenParameters&&this.config.customTokenParameters.forEach(s=>this._storage.removeItem(s)),this.silentRefreshSubject=null,this.eventsSubject.next(new L("logout")),!this.logoutUrl||t)return;let a;if(!this.validateUrlForHttps(this.logoutUrl))throw new Error("logoutUrl  must use HTTPS (with TLS), or config value for property 'requireHttps' must be set to 'false' and allow HTTP (without TLS).");if(this.logoutUrl.indexOf("{{")>-1)a=this.logoutUrl.replace(/\{\{id_token\}\}/,encodeURIComponent(n)).replace(/\{\{client_id\}\}/,encodeURIComponent(this.clientId));else{let s=new me({encoder:new fe});n&&(s=s.set("id_token_hint",n));let l=this.postLogoutRedirectUri||this.redirectUriAsPostLogoutRedirectUriFallback&&this.redirectUri||"";l&&(s=s.set("post_logout_redirect_uri",l),o&&(s=s.set("state",o)));for(let d in e)s=s.set(d,e[d]);a=this.logoutUrl+(this.logoutUrl.indexOf("?")>-1?"&":"?")+s.toString()}this.config.openUri(a)}createAndSaveNonce(){let e=this;return this.createNonce().then(function(o){return e.saveNoncesInLocalStorage&&typeof window.localStorage<"u"?localStorage.setItem("nonce",o):e._storage.setItem("nonce",o),o})}ngOnDestroy(){this.clearAccessTokenTimer(),this.clearIdTokenTimer(),this.removeSilentRefreshEventListener();let e=this.document.getElementById(this.silentRefreshIFrameName);e&&e.remove(),this.stopSessionCheckTimer(),this.removeSessionCheckEventListener();let o=this.document.getElementById(this.sessionCheckIFrameName);o&&o.remove()}createNonce(){return new Promise(e=>{if(this.rngUrl)throw new Error("createNonce with rng-web-api has not been implemented so far");let o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",t=45,n="",a=typeof self>"u"?null:self.crypto||self.msCrypto;if(a){let s=new Uint8Array(t);a.getRandomValues(s),s.map||(s.map=Array.prototype.map),s=s.map(l=>o.charCodeAt(l%o.length)),n=String.fromCharCode.apply(null,s)}else for(;0<t--;)n+=o[Math.random()*o.length|0];e(nt(n))})}checkAtHash(e){return z(this,null,function*(){return this.tokenValidationHandler?this.tokenValidationHandler.validateAtHash(e):(this.logger.warn("No tokenValidationHandler configured. Cannot check at_hash."),!0)})}checkSignature(e){return this.tokenValidationHandler?this.tokenValidationHandler.validateSignature(e):(this.logger.warn("No tokenValidationHandler configured. Cannot check signature."),Promise.resolve(null))}initLoginFlow(e="",o={}){return this.responseType==="code"?this.initCodeFlow(e,o):this.initImplicitFlow(e,o)}initCodeFlow(e="",o={}){this.loginUrl!==""?this.initCodeFlowInternal(e,o):this.events.pipe(S(t=>t.type==="discovery_document_loaded")).subscribe(()=>this.initCodeFlowInternal(e,o))}initCodeFlowInternal(e="",o={}){if(!this.validateUrlForHttps(this.loginUrl))throw new Error("loginUrl  must use HTTPS (with TLS), or config value for property 'requireHttps' must be set to 'false' and allow HTTP (without TLS).");let t={},n=null;typeof o=="string"?n=o:typeof o=="object"&&(t=o),this.createLoginUrl(e,n,null,!1,t).then(this.config.openUri).catch(a=>{console.error("Error in initAuthorizationCodeFlow"),console.error(a)})}createChallangeVerifierPairForPKCE(){return z(this,null,function*(){if(!this.crypto)throw new Error("PKCE support for code flow needs a CryptoHander. Did you import the OAuthModule using forRoot() ?");let e=yield this.createNonce(),o=yield this.crypto.calcHash(e,"sha-256");return[nt(o),e]})}extractRecognizedCustomParameters(e){let o=new Map;return this.config.customTokenParameters&&this.config.customTokenParameters.forEach(t=>{e[t]&&o.set(t,JSON.stringify(e[t]))}),o}revokeTokenAndLogout(e={},o=!1){let t=this.revocationEndpoint,n=this.getAccessToken(),a=this.getRefreshToken();if(!n)return Promise.resolve();let s=new me({encoder:new fe}),l=new U().set("Content-Type","application/x-www-form-urlencoded");if(this.useHttpBasicAuth){let d=btoa(`${this.clientId}:${this.dummyClientSecret}`);l=l.set("Authorization","Basic "+d)}if(this.useHttpBasicAuth||(s=s.set("client_id",this.clientId)),!this.useHttpBasicAuth&&this.dummyClientSecret&&(s=s.set("client_secret",this.dummyClientSecret)),this.customQueryParams)for(let d of Object.getOwnPropertyNames(this.customQueryParams))s=s.set(d,this.customQueryParams[d]);return new Promise((d,v)=>{let c,p;if(n){let f=s.set("token",n).set("token_type_hint","access_token");c=this.http.post(t,f,{headers:l})}else c=A(null);if(a){let f=s.set("token",a).set("token_type_hint","refresh_token");p=this.http.post(t,f,{headers:l})}else p=A(null);o&&(c=c.pipe(ge(f=>f.status===0?A(null):Fe(f))),p=p.pipe(ge(f=>f.status===0?A(null):Fe(f)))),Vo([c,p]).subscribe(f=>{this.logOut(e),d(f),this.logger.info("Token successfully revoked")},f=>{this.logger.error("Error revoking token",f),this.eventsSubject.next(new y("token_revoke_error",f)),v(f)})})}clearLocationHash(){location.hash!=""&&(location.hash="")}static{this.\u0275fac=function(o){return new(o||r)(x(De),x(br),x(ye,8),x(oo,8),x(ke,8),x(at),x(eo),x(ro,8),x($e),x(Re))}}static{this.\u0275prov=w({token:r,factory:r.\u0275fac})}}return r})(),to=class{},Lo=class{handleError(i){return Fe(i)}},Cn=(()=>{class r{constructor(e,o,t){this.oAuthService=e,this.errorHandler=o,this.moduleConfig=t}checkUrl(e){return this.moduleConfig.resourceServer.customUrlValidation?this.moduleConfig.resourceServer.customUrlValidation(e):this.moduleConfig.resourceServer.allowedUrls?!!this.moduleConfig.resourceServer.allowedUrls.find(o=>e.toLowerCase().startsWith(o.toLowerCase())):!0}intercept(e,o){let t=e.url.toLowerCase();return!this.moduleConfig||!this.moduleConfig.resourceServer||!this.checkUrl(t)?o.handle(e):this.moduleConfig.resourceServer.sendAccessToken?Qo(A(this.oAuthService.getAccessToken()).pipe(S(a=>!!a)),this.oAuthService.events.pipe(S(a=>a.type==="token_received"),$o(this.oAuthService.waitForTokenInMsec||0),ge(()=>A(null)),Y(()=>this.oAuthService.getAccessToken()))).pipe(oe(1),Ko(a=>{if(a){let s="Bearer "+a,l=e.headers.set("Authorization",s);e=e.clone({headers:l})}return o.handle(e).pipe(ge(s=>this.errorHandler.handleError(s)))})):o.handle(e).pipe(ge(a=>this.errorHandler.handleError(a)))}static{this.\u0275fac=function(o){return new(o||r)(x(M),x(to),x(Ze,8))}}static{this.\u0275prov=w({token:r,factory:r.\u0275fac})}}return r})();function wn(){return console}function _n(){return typeof sessionStorage<"u"?sessionStorage:new hn}function Sn(r=null,i=Je){return pe([M,at,{provide:eo,useFactory:wn},{provide:ye,useFactory:_n},{provide:oo,useClass:i},{provide:ro,useClass:xn},{provide:to,useClass:Lo},{provide:Ze,useValue:r},{provide:Ve,useClass:Cn,multi:!0},{provide:Re,useClass:pn}])}var ct=(()=>{class r{static forRoot(e=null,o=Je){return{ngModule:r,providers:[Sn(e,o)]}}static{this.\u0275fac=function(o){return new(o||r)}}static{this.\u0275mod=ne({type:r})}static{this.\u0275inj=re({imports:[de]})}}return r})();var as=new Pe("AUTH_CONFIG");var Bn={provide:Gr,useFactory:()=>{let r=T(be);return()=>{let i=r.getEnvironment();if(!i.oAuthConfig){console.warn("The oAuthConfig env is missing on environment.ts");return}let{issuer:e}=i.oAuthConfig,o=e.endsWith("/")?e:`${e}/`;window.open(`${o}Account/Manage?returnUrl=${window.location.href}`,"_self")}}},In=(()=>{class r{constructor(){this.oAuthService=T(M),this.authService=T(Ie)}canActivate(e,o){if(this.oAuthService.hasValidAccessToken())return!0;let n={returnUrl:o.url};return this.authService.navigateToLogin(n),!1}static{this.\u0275fac=function(o){return new(o||r)}}static{this.\u0275prov=w({token:r,factory:r.\u0275fac,providedIn:"root"})}}return r})(),Rn=(r,i)=>{let e=T(M),o=T(Ie);if(e.hasValidAccessToken())return!0;let n={returnUrl:i.url};return o.navigateToLogin(n),!1},An=(()=>{class r{constructor(e,o,t){this.oAuthService=e,this.environmentService=o,this.options=t,this.listenToSetEnvironment()}listenToSetEnvironment(){this.environmentService.createOnUpdateStream(e=>e).pipe(Y(e=>e.oAuthConfig),S(e=>!Ur(e,this.options.environment.oAuthConfig))).subscribe(e=>{this.oAuthService.configure(e)})}static{this.\u0275fac=function(o){return new(o||r)(x(M),x(be),x(Vr))}}static{this.\u0275prov=w({token:r,factory:r.\u0275fac,providedIn:"root"})}}return r})(),En=(()=>{class r{constructor(e,o,t,n){this.oAuthService=e,this.sessionState=o,this.httpWaitService=t,this.tenantKey=n}intercept(e,o){this.httpWaitService.addRequest(e);let n=e.context?.get(qr)?e:e.clone({setHeaders:this.getAdditionalHeaders(e.headers)});return o.handle(n).pipe(Xo(()=>this.httpWaitService.deleteRequest(e)))}getAdditionalHeaders(e){let o={},t=this.oAuthService.getAccessToken();!e?.has("Authorization")&&t&&(o.Authorization=`Bearer ${t}`);let n=this.sessionState.getLanguage();!e?.has("Accept-Language")&&n&&(o["Accept-Language"]=n);let a=this.sessionState.getTenant();return!e?.has(this.tenantKey)&&a?.id&&(o[this.tenantKey]=a.id),o["X-Requested-With"]="XMLHttpRequest",o}static{this.\u0275fac=function(o){return new(o||r)(x(M),x(Xe),x(Xr),x(Bo))}}static{this.\u0275prov=w({token:r,factory:r.\u0275fac,providedIn:"root"})}}return r})(),Fo=localStorage;function Ee(r=Fo){["access_token","id_token","refresh_token","nonce","PKCE_verifier","expires_at","id_token_claims_obj","id_token_expires_at","id_token_stored_at","access_token_stored_at","granted_scopes","session_state"].forEach(e=>r.removeItem(e))}var dt=(()=>{class r{constructor(){this.#e="remember_me",this.localStorageService=T(Ye)}#e;set(e){this.localStorageService.setItem(this.#e,JSON.stringify(e))}remove(){this.localStorageService.removeItem(this.#e)}get(){return!!JSON.parse(this.localStorageService.getItem(this.#e)||"false")}getFromToken(e){let o=e.split(".")[1].replace(/-/g,"+").replace(/_/g,"/");try{return!!JSON.parse(atob(o))[this.#e]}catch{return!1}}static{this.\u0275fac=function(o){return new(o||r)}}static{this.\u0275prov=w({token:r,factory:r.\u0275fac,providedIn:"root"})}}return r})(),ut=function(r,i){let e=i.get(Ge),o=i.get(ue),t=i.get(dt),n=i.get(Ie);return jo(X(()=>e.refreshAppState()),P(()=>{t.set(r.rememberMe||t.get()||t.getFromToken(n.getAccessToken())),r.redirectUrl&&o.navigate([r.redirectUrl])}))};function Po(r){let i=new Date().getTime();return r<i}var Ln=function(r){let i=r.get(Ge);r.get(M).hasValidAccessToken()&&!i.getDeep("currentUser.id")&&Ee()},io=class{constructor(i){this.injector=i,this.catchError=e=>(this.httpErrorReporter.reportError(e),A(null)),this.httpErrorReporter=i.get(Qr),this.environment=i.get(be),this.configState=i.get(Ge),this.oAuthService=i.get(M),this.sessionState=i.get(Xe),this.localStorageService=i.get(Ye),this.oAuthConfig=this.environment.getEnvironment().oAuthConfig||{},this.tenantKey=i.get(Bo),this.router=i.get(ue),this.oAuthErrorFilterService=i.get(ft),this.rememberMeService=i.get(dt),this.windowService=i.get(Zr),this.listenToOauthErrors()}init(){return z(this,null,function*(){return this.oAuthConfig.clientId&&Fn(this.oAuthConfig.clientId,Fo)&&Ee(Fo),this.oAuthService.configure(this.oAuthConfig),this.oAuthService.events.pipe(S(i=>i.type==="token_refresh_error")).subscribe(()=>this.navigateToLogin()),this.navigateToPreviousUrl(),this.oAuthService.loadDiscoveryDocument().then(()=>Po(this.oAuthService.getAccessTokenExpiration())&&this.oAuthService.getRefreshToken()?this.refreshToken():Promise.resolve()).catch(this.catchError)})}navigateToPreviousUrl(){let{responseType:i}=this.oAuthConfig;i==="code"&&this.oAuthService.events.pipe(S(e=>e.type==="token_received"&&!!this.oAuthService.state),oe(1),Y(()=>{let e=decodeURIComponent(this.oAuthService.state);return e&&e!=="/"?e:"/"}),X(e=>this.configState.getOne$("currentUser").pipe(S(o=>!!o?.isAuthenticated),P(()=>this.router.navigateByUrl(e))))).subscribe()}refreshToken(){return this.oAuthService.refreshToken().catch(()=>Ee())}listenToOauthErrors(){this.oAuthService.events.pipe(S(i=>i instanceof y),P(i=>{this.oAuthErrorFilterService.run(i)||Ee()}),X(()=>this.configState.refreshAppState())).subscribe()}};function Fn(r,i){let e="abpOAuthClientId";if(!i.getItem(e))return i.setItem(e,r),!1;let o=i.getItem(e)!==r;return o&&i.setItem(e,r),o}var Ho=class r extends io{constructor(){super(...arguments),this.isInternalAuth=!1}init(){return z(this,null,function*(){return this.checkRememberMeOption(),this.listenToTokenReceived(),lo(r.prototype,this,"init").call(this).then(()=>this.oAuthService.tryLogin().catch(Kr)).then(()=>this.oAuthService.setupAutomaticSilentRefresh())})}checkRememberMeOption(){let i=this.oAuthService.getAccessToken(),e=Po(this.oAuthService.getAccessTokenExpiration()),o=this.rememberMeService.get();if(i&&!o){let t=this.rememberMeService.getFromToken(i);this.rememberMeService.set(!!t)}o=this.rememberMeService.get(),i&&e&&!o&&(this.rememberMeService.remove(),this.oAuthService.logOut())}getCultureParams(i){let e=this.sessionState.getLanguage();return ee(ee({},e&&{culture:e,"ui-culture":e}),i)}setUICulture(){let i=new URLSearchParams(window.location.search);this.configState.uiCultureFromAuthCodeFlow=i.get("ui-culture")}replaceURLParams(){let i=this.windowService.window.location,e=this.windowService.window.history,o=i.search.replace(/([?&])iss=[^&]*&?/,"$1").replace(/([?&])culture=[^&]*&?/,"$1").replace(/([?&])ui-culture=[^&]*&?/,"$1").replace(/[?&]+$/,""),t=i.origin+i.pathname+o+i.hash;e.replaceState(null,"",t)}listenToTokenReceived(){this.oAuthService.events.pipe(S(i=>i.type==="token_received"),P(()=>{this.setUICulture(),this.replaceURLParams()}),oe(1)).subscribe()}navigateToLogin(i){let e="";i?.returnUrl&&(e=i.returnUrl);let o=this.getCultureParams(i);this.oAuthService.initCodeFlow(e,o)}checkIfInternalAuth(i){return this.oAuthService.initCodeFlow("",this.getCultureParams(i)),!1}logout(i){return this.rememberMeService.remove(),i?.noRedirectToLogoutUrl?(this.router.navigate(["/"]),q(this.oAuthService.revokeTokenAndLogout(!0))):q(this.oAuthService.revokeTokenAndLogout(this.getCultureParams(i)))}login(i){return this.oAuthService.initCodeFlow("",this.getCultureParams(i)),A(null)}},zo=class r extends io{constructor(){super(...arguments),this.isInternalAuth=!0}listenToTokenExpiration(){this.oAuthService.events.pipe(S(i=>i instanceof L&&i.type==="token_expires"&&i.info==="access_token")).subscribe(()=>{this.oAuthService.getRefreshToken()?this.refreshToken():(this.oAuthService.logOut(),this.rememberMeService.remove(),this.configState.refreshAppState().subscribe())})}init(){return z(this,null,function*(){return this.checkRememberMeOption(),lo(r.prototype,this,"init").call(this).then(()=>this.listenToTokenExpiration())})}checkRememberMeOption(){let i=this.oAuthService.getAccessToken(),e=Po(this.oAuthService.getAccessTokenExpiration()),o=this.rememberMeService.get();i&&e&&!o&&(this.rememberMeService.remove(),this.oAuthService.logOut())}navigateToLogin(i){return this.injector.get(ue).navigate(["/account/login"],{queryParams:i})}checkIfInternalAuth(){return!0}login(i){let e=this.sessionState.getTenant();return q(this.oAuthService.fetchTokenUsingPasswordFlow(i.username,i.password,new U(ee({},e&&e.id&&{[this.tenantKey]:e.id})))).pipe(ut(i,this.injector))}logout(){let i=this.injector.get(ue);return q(this.oAuthService.revokeTokenAndLogout(!0)).pipe(X(()=>this.configState.refreshAppState()),P(()=>{this.rememberMeService.remove(),i.navigateByUrl("/")}))}refreshToken(){return this.oAuthService.refreshToken().catch(()=>{Ee(),this.rememberMeService.remove()})}},lt={Code(r){return new Ho(r)},Password(r){return new zo(r)}},Hn=(()=>{class r{get oidc(){return this.oAuthService.oidc}set oidc(e){this.oAuthService.oidc=e}get isInternalAuth(){return this.strategy.isInternalAuth}constructor(e){this.injector=e,this.oAuthService=this.injector.get(M)}init(){return z(this,null,function*(){let o=this.injector.get(be).getEnvironment$().pipe(Y(t=>t?.oAuthConfig),S(Boolean),P(t=>{this.strategy=t.responseType==="code"?lt.Code(this.injector):lt.Password(this.injector)}),X(()=>q(this.strategy.init())),oe(1));return yield Wo(o)})}logout(e){return this.strategy?this.strategy.logout(e):No}navigateToLogin(e){this.strategy.navigateToLogin(e)}login(e){return this.strategy.login(e)}get isAuthenticated(){return this.oAuthService.hasValidAccessToken()}loginUsingGrant(e,o,t){let{clientId:n,dummyClientSecret:a}=this.oAuthService,s=this.oAuthService.getAccessToken(),l=ee({access_token:s,grant_type:e,client_id:n},o);return a&&(l.client_secret=a),this.oAuthService.fetchTokenUsingGrant(e,l,t)}getRefreshToken(){return this.oAuthService.getRefreshToken()}getAccessToken(){return this.oAuthService.getAccessToken()}refreshToken(){return this.oAuthService.refreshToken()}getAccessTokenExpiration(){return this.oAuthService.getAccessTokenExpiration()}static{this.\u0275fac=function(o){return new(o||r)(x(Me))}}static{this.\u0275prov=w({token:r,factory:r.\u0275fac,providedIn:"root"})}}return r})(),ft=(()=>{class r extends Wr{constructor(){super(...arguments),this._filters=te([]),this.filters=this._filters.asReadonly()}get(e){return this._filters().find(({id:o})=>o===e)}add(e){this._filters.update(o=>[...o,e])}patch(e){let o=this.filters().find(({id:t})=>t===e.id);o&&Object.assign(o,e)}remove(e){this.filters().find(({id:t})=>t===e)&&this._filters.update(t=>t.filter(({id:n})=>n!==e))}run(e){return this.filters().filter(({executable:o})=>!!o).map(({execute:o})=>o(e)).some(o=>o)}static{this.\u0275fac=(()=>{let e;return function(t){return(e||(e=N(r)))(t||r)}})()}static{this.\u0275prov=w({token:r,factory:r.\u0275fac,providedIn:"root"})}}return r})();function gt(){let r=[{provide:Ie,useClass:Hn},{provide:jr,useClass:In},{provide:Nr,useValue:Rn},{provide:Io,useClass:En},{provide:Jr,useValue:ut},{provide:Yr,useValue:Ln},{provide:Ve,useExisting:Io,multi:!0},Bn,ar(()=>{T(An)}),ct.forRoot().providers,{provide:ye,useClass:Ye},{provide:$r,useExisting:ft}];return pe(r)}var ht="https://holybless-public.pages.dev/",pt={issuer:"https://admin.holyblesspan.com/",redirectUri:ht,clientId:"holybless_App",responseType:"code",scope:"offline_access holybless",requireHttps:!0},mt={production:!0,application:{baseUrl:ht,name:"holybless"},oAuthConfig:pt,apis:{default:{url:"https://admin.holyblesspan.com",rootNamespace:"Holybless"},AbpAccountPublic:{url:pt.issuer,rootNamespace:"AbpAccountPublic"}}};var no=class r{constructor(){this.supportedLanguages=[{code:"zh-Hans",label:"\u7B80\u4F53\u4E2D\u6587"},{code:"zh-Hant",label:"\u7E41\u9AD4\u4E2D\u6587"},{code:"en",label:"English"}];this.language=te(this.getStoredLanguage()||"zh-Hans")}getStoredLanguage(){return localStorage.getItem("lang")||"zh-Hans"}static{this.\u0275fac=function(e){return new(e||r)}}static{this.\u0275prov=w({token:r,factory:r.\u0275fac,providedIn:"root"})}};var bt=(r,i)=>{let e=T(no),o=r.clone({setHeaders:{"Accept-Language":e.language()}});return i(o)};var vt={root:{transitionDuration:"{transition.duration}"},panel:{borderWidth:"0 0 1px 0",borderColor:"{content.border.color}"},header:{color:"{text.muted.color}",hoverColor:"{text.color}",activeColor:"{text.color}",padding:"1.125rem",fontWeight:"600",borderRadius:"0",borderWidth:"0",borderColor:"{content.border.color}",background:"{content.background}",hoverBackground:"{content.background}",activeBackground:"{content.background}",activeHoverBackground:"{content.background}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"},toggleIcon:{color:"{text.muted.color}",hoverColor:"{text.color}",activeColor:"{text.color}",activeHoverColor:"{text.color}"},first:{topBorderRadius:"{content.border.radius}",borderWidth:"0"},last:{bottomBorderRadius:"{content.border.radius}",activeBottomBorderRadius:"0"}},content:{borderWidth:"0",borderColor:"{content.border.color}",background:"{content.background}",color:"{text.color}",padding:"0 1.125rem 1.125rem 1.125rem"}};var kt={root:{background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledHoverBackground:"{form.field.filled.hover.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",invalidPlaceholderColor:"{form.field.invalid.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}"},overlay:{background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}"},list:{padding:"{list.padding}",gap:"{list.gap}"},option:{focusBackground:"{list.option.focus.background}",selectedBackground:"{list.option.selected.background}",selectedFocusBackground:"{list.option.selected.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",selectedColor:"{list.option.selected.color}",selectedFocusColor:"{list.option.selected.focus.color}",padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}"},optionGroup:{background:"{list.option.group.background}",color:"{list.option.group.color}",fontWeight:"{list.option.group.font.weight}",padding:"{list.option.group.padding}"},dropdown:{width:"2.5rem",sm:{width:"2rem"},lg:{width:"3rem"},borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.border.color}",activeBorderColor:"{form.field.border.color}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},chip:{borderRadius:"{border.radius.sm}"},emptyMessage:{padding:"{list.option.padding}"},colorScheme:{light:{chip:{focusBackground:"{surface.200}",focusColor:"{surface.800}"},dropdown:{background:"{surface.100}",hoverBackground:"{surface.200}",activeBackground:"{surface.300}",color:"{surface.600}",hoverColor:"{surface.700}",activeColor:"{surface.800}"}},dark:{chip:{focusBackground:"{surface.700}",focusColor:"{surface.0}"},dropdown:{background:"{surface.800}",hoverBackground:"{surface.700}",activeBackground:"{surface.600}",color:"{surface.300}",hoverColor:"{surface.200}",activeColor:"{surface.100}"}}}};var yt={root:{width:"2rem",height:"2rem",fontSize:"1rem",background:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}"},icon:{size:"1rem"},group:{borderColor:"{content.background}",offset:"-0.75rem"},lg:{width:"3rem",height:"3rem",fontSize:"1.5rem",icon:{size:"1.5rem"},group:{offset:"-1rem"}},xl:{width:"4rem",height:"4rem",fontSize:"2rem",icon:{size:"2rem"},group:{offset:"-1.5rem"}}};var xt={root:{borderRadius:"{border.radius.md}",padding:"0 0.5rem",fontSize:"0.75rem",fontWeight:"700",minWidth:"1.5rem",height:"1.5rem"},dot:{size:"0.5rem"},sm:{fontSize:"0.625rem",minWidth:"1.25rem",height:"1.25rem"},lg:{fontSize:"0.875rem",minWidth:"1.75rem",height:"1.75rem"},xl:{fontSize:"1rem",minWidth:"2rem",height:"2rem"},colorScheme:{light:{primary:{background:"{primary.color}",color:"{primary.contrast.color}"},secondary:{background:"{surface.100}",color:"{surface.600}"},success:{background:"{green.500}",color:"{surface.0}"},info:{background:"{sky.500}",color:"{surface.0}"},warn:{background:"{orange.500}",color:"{surface.0}"},danger:{background:"{red.500}",color:"{surface.0}"},contrast:{background:"{surface.950}",color:"{surface.0}"}},dark:{primary:{background:"{primary.color}",color:"{primary.contrast.color}"},secondary:{background:"{surface.800}",color:"{surface.300}"},success:{background:"{green.400}",color:"{green.950}"},info:{background:"{sky.400}",color:"{sky.950}"},warn:{background:"{orange.400}",color:"{orange.950}"},danger:{background:"{red.400}",color:"{red.950}"},contrast:{background:"{surface.0}",color:"{surface.950}"}}}};var Ct={primitive:{borderRadius:{none:"0",xs:"2px",sm:"4px",md:"6px",lg:"8px",xl:"12px"},emerald:{50:"#ecfdf5",100:"#d1fae5",200:"#a7f3d0",300:"#6ee7b7",400:"#34d399",500:"#10b981",600:"#059669",700:"#047857",800:"#065f46",900:"#064e3b",950:"#022c22"},green:{50:"#f0fdf4",100:"#dcfce7",200:"#bbf7d0",300:"#86efac",400:"#4ade80",500:"#22c55e",600:"#16a34a",700:"#15803d",800:"#166534",900:"#14532d",950:"#052e16"},lime:{50:"#f7fee7",100:"#ecfccb",200:"#d9f99d",300:"#bef264",400:"#a3e635",500:"#84cc16",600:"#65a30d",700:"#4d7c0f",800:"#3f6212",900:"#365314",950:"#1a2e05"},red:{50:"#fef2f2",100:"#fee2e2",200:"#fecaca",300:"#fca5a5",400:"#f87171",500:"#ef4444",600:"#dc2626",700:"#b91c1c",800:"#991b1b",900:"#7f1d1d",950:"#450a0a"},orange:{50:"#fff7ed",100:"#ffedd5",200:"#fed7aa",300:"#fdba74",400:"#fb923c",500:"#f97316",600:"#ea580c",700:"#c2410c",800:"#9a3412",900:"#7c2d12",950:"#431407"},amber:{50:"#fffbeb",100:"#fef3c7",200:"#fde68a",300:"#fcd34d",400:"#fbbf24",500:"#f59e0b",600:"#d97706",700:"#b45309",800:"#92400e",900:"#78350f",950:"#451a03"},yellow:{50:"#fefce8",100:"#fef9c3",200:"#fef08a",300:"#fde047",400:"#facc15",500:"#eab308",600:"#ca8a04",700:"#a16207",800:"#854d0e",900:"#713f12",950:"#422006"},teal:{50:"#f0fdfa",100:"#ccfbf1",200:"#99f6e4",300:"#5eead4",400:"#2dd4bf",500:"#14b8a6",600:"#0d9488",700:"#0f766e",800:"#115e59",900:"#134e4a",950:"#042f2e"},cyan:{50:"#ecfeff",100:"#cffafe",200:"#a5f3fc",300:"#67e8f9",400:"#22d3ee",500:"#06b6d4",600:"#0891b2",700:"#0e7490",800:"#155e75",900:"#164e63",950:"#083344"},sky:{50:"#f0f9ff",100:"#e0f2fe",200:"#bae6fd",300:"#7dd3fc",400:"#38bdf8",500:"#0ea5e9",600:"#0284c7",700:"#0369a1",800:"#075985",900:"#0c4a6e",950:"#082f49"},blue:{50:"#eff6ff",100:"#dbeafe",200:"#bfdbfe",300:"#93c5fd",400:"#60a5fa",500:"#3b82f6",600:"#2563eb",700:"#1d4ed8",800:"#1e40af",900:"#1e3a8a",950:"#172554"},indigo:{50:"#eef2ff",100:"#e0e7ff",200:"#c7d2fe",300:"#a5b4fc",400:"#818cf8",500:"#6366f1",600:"#4f46e5",700:"#4338ca",800:"#3730a3",900:"#312e81",950:"#1e1b4b"},violet:{50:"#f5f3ff",100:"#ede9fe",200:"#ddd6fe",300:"#c4b5fd",400:"#a78bfa",500:"#8b5cf6",600:"#7c3aed",700:"#6d28d9",800:"#5b21b6",900:"#4c1d95",950:"#2e1065"},purple:{50:"#faf5ff",100:"#f3e8ff",200:"#e9d5ff",300:"#d8b4fe",400:"#c084fc",500:"#a855f7",600:"#9333ea",700:"#7e22ce",800:"#6b21a8",900:"#581c87",950:"#3b0764"},fuchsia:{50:"#fdf4ff",100:"#fae8ff",200:"#f5d0fe",300:"#f0abfc",400:"#e879f9",500:"#d946ef",600:"#c026d3",700:"#a21caf",800:"#86198f",900:"#701a75",950:"#4a044e"},pink:{50:"#fdf2f8",100:"#fce7f3",200:"#fbcfe8",300:"#f9a8d4",400:"#f472b6",500:"#ec4899",600:"#db2777",700:"#be185d",800:"#9d174d",900:"#831843",950:"#500724"},rose:{50:"#fff1f2",100:"#ffe4e6",200:"#fecdd3",300:"#fda4af",400:"#fb7185",500:"#f43f5e",600:"#e11d48",700:"#be123c",800:"#9f1239",900:"#881337",950:"#4c0519"},slate:{50:"#f8fafc",100:"#f1f5f9",200:"#e2e8f0",300:"#cbd5e1",400:"#94a3b8",500:"#64748b",600:"#475569",700:"#334155",800:"#1e293b",900:"#0f172a",950:"#020617"},gray:{50:"#f9fafb",100:"#f3f4f6",200:"#e5e7eb",300:"#d1d5db",400:"#9ca3af",500:"#6b7280",600:"#4b5563",700:"#374151",800:"#1f2937",900:"#111827",950:"#030712"},zinc:{50:"#fafafa",100:"#f4f4f5",200:"#e4e4e7",300:"#d4d4d8",400:"#a1a1aa",500:"#71717a",600:"#52525b",700:"#3f3f46",800:"#27272a",900:"#18181b",950:"#09090b"},neutral:{50:"#fafafa",100:"#f5f5f5",200:"#e5e5e5",300:"#d4d4d4",400:"#a3a3a3",500:"#737373",600:"#525252",700:"#404040",800:"#262626",900:"#171717",950:"#0a0a0a"},stone:{50:"#fafaf9",100:"#f5f5f4",200:"#e7e5e4",300:"#d6d3d1",400:"#a8a29e",500:"#78716c",600:"#57534e",700:"#44403c",800:"#292524",900:"#1c1917",950:"#0c0a09"}},semantic:{transitionDuration:"0.2s",focusRing:{width:"1px",style:"solid",color:"{primary.color}",offset:"2px",shadow:"none"},disabledOpacity:"0.6",iconSize:"1rem",anchorGutter:"2px",primary:{50:"{emerald.50}",100:"{emerald.100}",200:"{emerald.200}",300:"{emerald.300}",400:"{emerald.400}",500:"{emerald.500}",600:"{emerald.600}",700:"{emerald.700}",800:"{emerald.800}",900:"{emerald.900}",950:"{emerald.950}"},formField:{paddingX:"0.75rem",paddingY:"0.5rem",sm:{fontSize:"0.875rem",paddingX:"0.625rem",paddingY:"0.375rem"},lg:{fontSize:"1.125rem",paddingX:"0.875rem",paddingY:"0.625rem"},borderRadius:"{border.radius.md}",focusRing:{width:"0",style:"none",color:"transparent",offset:"0",shadow:"none"},transitionDuration:"{transition.duration}"},list:{padding:"0.25rem 0.25rem",gap:"2px",header:{padding:"0.5rem 1rem 0.25rem 1rem"},option:{padding:"0.5rem 0.75rem",borderRadius:"{border.radius.sm}"},optionGroup:{padding:"0.5rem 0.75rem",fontWeight:"600"}},content:{borderRadius:"{border.radius.md}"},mask:{transitionDuration:"0.15s"},navigation:{list:{padding:"0.25rem 0.25rem",gap:"2px"},item:{padding:"0.5rem 0.75rem",borderRadius:"{border.radius.sm}",gap:"0.5rem"},submenuLabel:{padding:"0.5rem 0.75rem",fontWeight:"600"},submenuIcon:{size:"0.875rem"}},overlay:{select:{borderRadius:"{border.radius.md}",shadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)"},popover:{borderRadius:"{border.radius.md}",padding:"0.75rem",shadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)"},modal:{borderRadius:"{border.radius.xl}",padding:"1.25rem",shadow:"0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)"},navigation:{shadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)"}},colorScheme:{light:{surface:{0:"#ffffff",50:"{slate.50}",100:"{slate.100}",200:"{slate.200}",300:"{slate.300}",400:"{slate.400}",500:"{slate.500}",600:"{slate.600}",700:"{slate.700}",800:"{slate.800}",900:"{slate.900}",950:"{slate.950}"},primary:{color:"{primary.500}",contrastColor:"#ffffff",hoverColor:"{primary.600}",activeColor:"{primary.700}"},highlight:{background:"{primary.50}",focusBackground:"{primary.100}",color:"{primary.700}",focusColor:"{primary.800}"},mask:{background:"rgba(0,0,0,0.4)",color:"{surface.200}"},formField:{background:"{surface.0}",disabledBackground:"{surface.200}",filledBackground:"{surface.50}",filledHoverBackground:"{surface.50}",filledFocusBackground:"{surface.50}",borderColor:"{surface.300}",hoverBorderColor:"{surface.400}",focusBorderColor:"{primary.color}",invalidBorderColor:"{red.400}",color:"{surface.700}",disabledColor:"{surface.500}",placeholderColor:"{surface.500}",invalidPlaceholderColor:"{red.600}",floatLabelColor:"{surface.500}",floatLabelFocusColor:"{primary.600}",floatLabelActiveColor:"{surface.500}",floatLabelInvalidColor:"{form.field.invalid.placeholder.color}",iconColor:"{surface.400}",shadow:"0 0 #0000, 0 0 #0000, 0 1px 2px 0 rgba(18, 18, 23, 0.05)"},text:{color:"{surface.700}",hoverColor:"{surface.800}",mutedColor:"{surface.500}",hoverMutedColor:"{surface.600}"},content:{background:"{surface.0}",hoverBackground:"{surface.100}",borderColor:"{surface.200}",color:"{text.color}",hoverColor:"{text.hover.color}"},overlay:{select:{background:"{surface.0}",borderColor:"{surface.200}",color:"{text.color}"},popover:{background:"{surface.0}",borderColor:"{surface.200}",color:"{text.color}"},modal:{background:"{surface.0}",borderColor:"{surface.200}",color:"{text.color}"}},list:{option:{focusBackground:"{surface.100}",selectedBackground:"{highlight.background}",selectedFocusBackground:"{highlight.focus.background}",color:"{text.color}",focusColor:"{text.hover.color}",selectedColor:"{highlight.color}",selectedFocusColor:"{highlight.focus.color}",icon:{color:"{surface.400}",focusColor:"{surface.500}"}},optionGroup:{background:"transparent",color:"{text.muted.color}"}},navigation:{item:{focusBackground:"{surface.100}",activeBackground:"{surface.100}",color:"{text.color}",focusColor:"{text.hover.color}",activeColor:"{text.hover.color}",icon:{color:"{surface.400}",focusColor:"{surface.500}",activeColor:"{surface.500}"}},submenuLabel:{background:"transparent",color:"{text.muted.color}"},submenuIcon:{color:"{surface.400}",focusColor:"{surface.500}",activeColor:"{surface.500}"}}},dark:{surface:{0:"#ffffff",50:"{zinc.50}",100:"{zinc.100}",200:"{zinc.200}",300:"{zinc.300}",400:"{zinc.400}",500:"{zinc.500}",600:"{zinc.600}",700:"{zinc.700}",800:"{zinc.800}",900:"{zinc.900}",950:"{zinc.950}"},primary:{color:"{primary.400}",contrastColor:"{surface.900}",hoverColor:"{primary.300}",activeColor:"{primary.200}"},highlight:{background:"color-mix(in srgb, {primary.400}, transparent 84%)",focusBackground:"color-mix(in srgb, {primary.400}, transparent 76%)",color:"rgba(255,255,255,.87)",focusColor:"rgba(255,255,255,.87)"},mask:{background:"rgba(0,0,0,0.6)",color:"{surface.200}"},formField:{background:"{surface.950}",disabledBackground:"{surface.700}",filledBackground:"{surface.800}",filledHoverBackground:"{surface.800}",filledFocusBackground:"{surface.800}",borderColor:"{surface.600}",hoverBorderColor:"{surface.500}",focusBorderColor:"{primary.color}",invalidBorderColor:"{red.300}",color:"{surface.0}",disabledColor:"{surface.400}",placeholderColor:"{surface.400}",invalidPlaceholderColor:"{red.400}",floatLabelColor:"{surface.400}",floatLabelFocusColor:"{primary.color}",floatLabelActiveColor:"{surface.400}",floatLabelInvalidColor:"{form.field.invalid.placeholder.color}",iconColor:"{surface.400}",shadow:"0 0 #0000, 0 0 #0000, 0 1px 2px 0 rgba(18, 18, 23, 0.05)"},text:{color:"{surface.0}",hoverColor:"{surface.0}",mutedColor:"{surface.400}",hoverMutedColor:"{surface.300}"},content:{background:"{surface.900}",hoverBackground:"{surface.800}",borderColor:"{surface.700}",color:"{text.color}",hoverColor:"{text.hover.color}"},overlay:{select:{background:"{surface.900}",borderColor:"{surface.700}",color:"{text.color}"},popover:{background:"{surface.900}",borderColor:"{surface.700}",color:"{text.color}"},modal:{background:"{surface.900}",borderColor:"{surface.700}",color:"{text.color}"}},list:{option:{focusBackground:"{surface.800}",selectedBackground:"{highlight.background}",selectedFocusBackground:"{highlight.focus.background}",color:"{text.color}",focusColor:"{text.hover.color}",selectedColor:"{highlight.color}",selectedFocusColor:"{highlight.focus.color}",icon:{color:"{surface.500}",focusColor:"{surface.400}"}},optionGroup:{background:"transparent",color:"{text.muted.color}"}},navigation:{item:{focusBackground:"{surface.800}",activeBackground:"{surface.800}",color:"{text.color}",focusColor:"{text.hover.color}",activeColor:"{text.hover.color}",icon:{color:"{surface.500}",focusColor:"{surface.400}",activeColor:"{surface.400}"}},submenuLabel:{background:"transparent",color:"{text.muted.color}"},submenuIcon:{color:"{surface.500}",focusColor:"{surface.400}",activeColor:"{surface.400}"}}}}}};var wt={root:{borderRadius:"{content.border.radius}"}};var _t={root:{padding:"1rem",background:"{content.background}",gap:"0.5rem",transitionDuration:"{transition.duration}"},item:{color:"{text.muted.color}",hoverColor:"{text.color}",borderRadius:"{content.border.radius}",gap:"{navigation.item.gap}",icon:{color:"{navigation.item.icon.color}",hoverColor:"{navigation.item.icon.focus.color}"},focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},separator:{color:"{navigation.item.icon.color}"}};var St={root:{borderRadius:"{form.field.border.radius}",roundedBorderRadius:"2rem",gap:"0.5rem",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",iconOnlyWidth:"2.5rem",sm:{fontSize:"{form.field.sm.font.size}",paddingX:"{form.field.sm.padding.x}",paddingY:"{form.field.sm.padding.y}",iconOnlyWidth:"2rem"},lg:{fontSize:"{form.field.lg.font.size}",paddingX:"{form.field.lg.padding.x}",paddingY:"{form.field.lg.padding.y}",iconOnlyWidth:"3rem"},label:{fontWeight:"500"},raisedShadow:"0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12)",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",offset:"{focus.ring.offset}"},badgeSize:"1rem",transitionDuration:"{form.field.transition.duration}"},colorScheme:{light:{root:{primary:{background:"{primary.color}",hoverBackground:"{primary.hover.color}",activeBackground:"{primary.active.color}",borderColor:"{primary.color}",hoverBorderColor:"{primary.hover.color}",activeBorderColor:"{primary.active.color}",color:"{primary.contrast.color}",hoverColor:"{primary.contrast.color}",activeColor:"{primary.contrast.color}",focusRing:{color:"{primary.color}",shadow:"none"}},secondary:{background:"{surface.100}",hoverBackground:"{surface.200}",activeBackground:"{surface.300}",borderColor:"{surface.100}",hoverBorderColor:"{surface.200}",activeBorderColor:"{surface.300}",color:"{surface.600}",hoverColor:"{surface.700}",activeColor:"{surface.800}",focusRing:{color:"{surface.600}",shadow:"none"}},info:{background:"{sky.500}",hoverBackground:"{sky.600}",activeBackground:"{sky.700}",borderColor:"{sky.500}",hoverBorderColor:"{sky.600}",activeBorderColor:"{sky.700}",color:"#ffffff",hoverColor:"#ffffff",activeColor:"#ffffff",focusRing:{color:"{sky.500}",shadow:"none"}},success:{background:"{green.500}",hoverBackground:"{green.600}",activeBackground:"{green.700}",borderColor:"{green.500}",hoverBorderColor:"{green.600}",activeBorderColor:"{green.700}",color:"#ffffff",hoverColor:"#ffffff",activeColor:"#ffffff",focusRing:{color:"{green.500}",shadow:"none"}},warn:{background:"{orange.500}",hoverBackground:"{orange.600}",activeBackground:"{orange.700}",borderColor:"{orange.500}",hoverBorderColor:"{orange.600}",activeBorderColor:"{orange.700}",color:"#ffffff",hoverColor:"#ffffff",activeColor:"#ffffff",focusRing:{color:"{orange.500}",shadow:"none"}},help:{background:"{purple.500}",hoverBackground:"{purple.600}",activeBackground:"{purple.700}",borderColor:"{purple.500}",hoverBorderColor:"{purple.600}",activeBorderColor:"{purple.700}",color:"#ffffff",hoverColor:"#ffffff",activeColor:"#ffffff",focusRing:{color:"{purple.500}",shadow:"none"}},danger:{background:"{red.500}",hoverBackground:"{red.600}",activeBackground:"{red.700}",borderColor:"{red.500}",hoverBorderColor:"{red.600}",activeBorderColor:"{red.700}",color:"#ffffff",hoverColor:"#ffffff",activeColor:"#ffffff",focusRing:{color:"{red.500}",shadow:"none"}},contrast:{background:"{surface.950}",hoverBackground:"{surface.900}",activeBackground:"{surface.800}",borderColor:"{surface.950}",hoverBorderColor:"{surface.900}",activeBorderColor:"{surface.800}",color:"{surface.0}",hoverColor:"{surface.0}",activeColor:"{surface.0}",focusRing:{color:"{surface.950}",shadow:"none"}}},outlined:{primary:{hoverBackground:"{primary.50}",activeBackground:"{primary.100}",borderColor:"{primary.200}",color:"{primary.color}"},secondary:{hoverBackground:"{surface.50}",activeBackground:"{surface.100}",borderColor:"{surface.200}",color:"{surface.500}"},success:{hoverBackground:"{green.50}",activeBackground:"{green.100}",borderColor:"{green.200}",color:"{green.500}"},info:{hoverBackground:"{sky.50}",activeBackground:"{sky.100}",borderColor:"{sky.200}",color:"{sky.500}"},warn:{hoverBackground:"{orange.50}",activeBackground:"{orange.100}",borderColor:"{orange.200}",color:"{orange.500}"},help:{hoverBackground:"{purple.50}",activeBackground:"{purple.100}",borderColor:"{purple.200}",color:"{purple.500}"},danger:{hoverBackground:"{red.50}",activeBackground:"{red.100}",borderColor:"{red.200}",color:"{red.500}"},contrast:{hoverBackground:"{surface.50}",activeBackground:"{surface.100}",borderColor:"{surface.700}",color:"{surface.950}"},plain:{hoverBackground:"{surface.50}",activeBackground:"{surface.100}",borderColor:"{surface.200}",color:"{surface.700}"}},text:{primary:{hoverBackground:"{primary.50}",activeBackground:"{primary.100}",color:"{primary.color}"},secondary:{hoverBackground:"{surface.50}",activeBackground:"{surface.100}",color:"{surface.500}"},success:{hoverBackground:"{green.50}",activeBackground:"{green.100}",color:"{green.500}"},info:{hoverBackground:"{sky.50}",activeBackground:"{sky.100}",color:"{sky.500}"},warn:{hoverBackground:"{orange.50}",activeBackground:"{orange.100}",color:"{orange.500}"},help:{hoverBackground:"{purple.50}",activeBackground:"{purple.100}",color:"{purple.500}"},danger:{hoverBackground:"{red.50}",activeBackground:"{red.100}",color:"{red.500}"},contrast:{hoverBackground:"{surface.50}",activeBackground:"{surface.100}",color:"{surface.950}"},plain:{hoverBackground:"{surface.50}",activeBackground:"{surface.100}",color:"{surface.700}"}},link:{color:"{primary.color}",hoverColor:"{primary.color}",activeColor:"{primary.color}"}},dark:{root:{primary:{background:"{primary.color}",hoverBackground:"{primary.hover.color}",activeBackground:"{primary.active.color}",borderColor:"{primary.color}",hoverBorderColor:"{primary.hover.color}",activeBorderColor:"{primary.active.color}",color:"{primary.contrast.color}",hoverColor:"{primary.contrast.color}",activeColor:"{primary.contrast.color}",focusRing:{color:"{primary.color}",shadow:"none"}},secondary:{background:"{surface.800}",hoverBackground:"{surface.700}",activeBackground:"{surface.600}",borderColor:"{surface.800}",hoverBorderColor:"{surface.700}",activeBorderColor:"{surface.600}",color:"{surface.300}",hoverColor:"{surface.200}",activeColor:"{surface.100}",focusRing:{color:"{surface.300}",shadow:"none"}},info:{background:"{sky.400}",hoverBackground:"{sky.300}",activeBackground:"{sky.200}",borderColor:"{sky.400}",hoverBorderColor:"{sky.300}",activeBorderColor:"{sky.200}",color:"{sky.950}",hoverColor:"{sky.950}",activeColor:"{sky.950}",focusRing:{color:"{sky.400}",shadow:"none"}},success:{background:"{green.400}",hoverBackground:"{green.300}",activeBackground:"{green.200}",borderColor:"{green.400}",hoverBorderColor:"{green.300}",activeBorderColor:"{green.200}",color:"{green.950}",hoverColor:"{green.950}",activeColor:"{green.950}",focusRing:{color:"{green.400}",shadow:"none"}},warn:{background:"{orange.400}",hoverBackground:"{orange.300}",activeBackground:"{orange.200}",borderColor:"{orange.400}",hoverBorderColor:"{orange.300}",activeBorderColor:"{orange.200}",color:"{orange.950}",hoverColor:"{orange.950}",activeColor:"{orange.950}",focusRing:{color:"{orange.400}",shadow:"none"}},help:{background:"{purple.400}",hoverBackground:"{purple.300}",activeBackground:"{purple.200}",borderColor:"{purple.400}",hoverBorderColor:"{purple.300}",activeBorderColor:"{purple.200}",color:"{purple.950}",hoverColor:"{purple.950}",activeColor:"{purple.950}",focusRing:{color:"{purple.400}",shadow:"none"}},danger:{background:"{red.400}",hoverBackground:"{red.300}",activeBackground:"{red.200}",borderColor:"{red.400}",hoverBorderColor:"{red.300}",activeBorderColor:"{red.200}",color:"{red.950}",hoverColor:"{red.950}",activeColor:"{red.950}",focusRing:{color:"{red.400}",shadow:"none"}},contrast:{background:"{surface.0}",hoverBackground:"{surface.100}",activeBackground:"{surface.200}",borderColor:"{surface.0}",hoverBorderColor:"{surface.100}",activeBorderColor:"{surface.200}",color:"{surface.950}",hoverColor:"{surface.950}",activeColor:"{surface.950}",focusRing:{color:"{surface.0}",shadow:"none"}}},outlined:{primary:{hoverBackground:"color-mix(in srgb, {primary.color}, transparent 96%)",activeBackground:"color-mix(in srgb, {primary.color}, transparent 84%)",borderColor:"{primary.700}",color:"{primary.color}"},secondary:{hoverBackground:"rgba(255,255,255,0.04)",activeBackground:"rgba(255,255,255,0.16)",borderColor:"{surface.700}",color:"{surface.400}"},success:{hoverBackground:"color-mix(in srgb, {green.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {green.400}, transparent 84%)",borderColor:"{green.700}",color:"{green.400}"},info:{hoverBackground:"color-mix(in srgb, {sky.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {sky.400}, transparent 84%)",borderColor:"{sky.700}",color:"{sky.400}"},warn:{hoverBackground:"color-mix(in srgb, {orange.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {orange.400}, transparent 84%)",borderColor:"{orange.700}",color:"{orange.400}"},help:{hoverBackground:"color-mix(in srgb, {purple.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {purple.400}, transparent 84%)",borderColor:"{purple.700}",color:"{purple.400}"},danger:{hoverBackground:"color-mix(in srgb, {red.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {red.400}, transparent 84%)",borderColor:"{red.700}",color:"{red.400}"},contrast:{hoverBackground:"{surface.800}",activeBackground:"{surface.700}",borderColor:"{surface.500}",color:"{surface.0}"},plain:{hoverBackground:"{surface.800}",activeBackground:"{surface.700}",borderColor:"{surface.600}",color:"{surface.0}"}},text:{primary:{hoverBackground:"color-mix(in srgb, {primary.color}, transparent 96%)",activeBackground:"color-mix(in srgb, {primary.color}, transparent 84%)",color:"{primary.color}"},secondary:{hoverBackground:"{surface.800}",activeBackground:"{surface.700}",color:"{surface.400}"},success:{hoverBackground:"color-mix(in srgb, {green.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {green.400}, transparent 84%)",color:"{green.400}"},info:{hoverBackground:"color-mix(in srgb, {sky.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {sky.400}, transparent 84%)",color:"{sky.400}"},warn:{hoverBackground:"color-mix(in srgb, {orange.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {orange.400}, transparent 84%)",color:"{orange.400}"},help:{hoverBackground:"color-mix(in srgb, {purple.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {purple.400}, transparent 84%)",color:"{purple.400}"},danger:{hoverBackground:"color-mix(in srgb, {red.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {red.400}, transparent 84%)",color:"{red.400}"},contrast:{hoverBackground:"{surface.800}",activeBackground:"{surface.700}",color:"{surface.0}"},plain:{hoverBackground:"{surface.800}",activeBackground:"{surface.700}",color:"{surface.0}"}},link:{color:"{primary.color}",hoverColor:"{primary.color}",activeColor:"{primary.color}"}}}};var Tt={root:{background:"{content.background}",borderRadius:"{border.radius.xl}",color:"{content.color}",shadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1)"},body:{padding:"1.25rem",gap:"0.5rem"},caption:{gap:"0.5rem"},title:{fontSize:"1.25rem",fontWeight:"500"},subtitle:{color:"{text.muted.color}"}};var Bt={root:{transitionDuration:"{transition.duration}"},content:{gap:"0.25rem"},indicatorList:{padding:"1rem",gap:"0.5rem"},indicator:{width:"2rem",height:"0.5rem",borderRadius:"{content.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},colorScheme:{light:{indicator:{background:"{surface.200}",hoverBackground:"{surface.300}",activeBackground:"{primary.color}"}},dark:{indicator:{background:"{surface.700}",hoverBackground:"{surface.600}",activeBackground:"{primary.color}"}}}};var It={root:{background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledHoverBackground:"{form.field.filled.hover.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",invalidPlaceholderColor:"{form.field.invalid.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}",sm:{fontSize:"{form.field.sm.font.size}",paddingX:"{form.field.sm.padding.x}",paddingY:"{form.field.sm.padding.y}"},lg:{fontSize:"{form.field.lg.font.size}",paddingX:"{form.field.lg.padding.x}",paddingY:"{form.field.lg.padding.y}"}},dropdown:{width:"2.5rem",color:"{form.field.icon.color}"},overlay:{background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}"},list:{padding:"{list.padding}",gap:"{list.gap}",mobileIndent:"1rem"},option:{focusBackground:"{list.option.focus.background}",selectedBackground:"{list.option.selected.background}",selectedFocusBackground:"{list.option.selected.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",selectedColor:"{list.option.selected.color}",selectedFocusColor:"{list.option.selected.focus.color}",padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}",icon:{color:"{list.option.icon.color}",focusColor:"{list.option.icon.focus.color}",size:"0.875rem"}},clearIcon:{color:"{form.field.icon.color}"}};var Rt={root:{borderRadius:"{border.radius.sm}",width:"1.25rem",height:"1.25rem",background:"{form.field.background}",checkedBackground:"{primary.color}",checkedHoverBackground:"{primary.hover.color}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.border.color}",checkedBorderColor:"{primary.color}",checkedHoverBorderColor:"{primary.hover.color}",checkedFocusBorderColor:"{primary.color}",checkedDisabledBorderColor:"{form.field.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",shadow:"{form.field.shadow}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}",sm:{width:"1rem",height:"1rem"},lg:{width:"1.5rem",height:"1.5rem"}},icon:{size:"0.875rem",color:"{form.field.color}",checkedColor:"{primary.contrast.color}",checkedHoverColor:"{primary.contrast.color}",disabledColor:"{form.field.disabled.color}",sm:{size:"0.75rem"},lg:{size:"1rem"}}};var At={root:{borderRadius:"16px",paddingX:"0.75rem",paddingY:"0.5rem",gap:"0.5rem",transitionDuration:"{transition.duration}"},image:{width:"2rem",height:"2rem"},icon:{size:"1rem"},removeIcon:{size:"1rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"}},colorScheme:{light:{root:{background:"{surface.100}",color:"{surface.800}"},icon:{color:"{surface.800}"},removeIcon:{color:"{surface.800}"}},dark:{root:{background:"{surface.800}",color:"{surface.0}"},icon:{color:"{surface.0}"},removeIcon:{color:"{surface.0}"}}}};var Et={root:{transitionDuration:"{transition.duration}"},preview:{width:"1.5rem",height:"1.5rem",borderRadius:"{form.field.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},panel:{shadow:"{overlay.popover.shadow}",borderRadius:"{overlay.popover.borderRadius}"},colorScheme:{light:{panel:{background:"{surface.800}",borderColor:"{surface.900}"},handle:{color:"{surface.0}"}},dark:{panel:{background:"{surface.900}",borderColor:"{surface.700}"},handle:{color:"{surface.0}"}}}};var Lt={icon:{size:"2rem",color:"{overlay.modal.color}"},content:{gap:"1rem"}};var Ft={root:{background:"{overlay.popover.background}",borderColor:"{overlay.popover.border.color}",color:"{overlay.popover.color}",borderRadius:"{overlay.popover.border.radius}",shadow:"{overlay.popover.shadow}",gutter:"10px",arrowOffset:"1.25rem"},content:{padding:"{overlay.popover.padding}",gap:"1rem"},icon:{size:"1.5rem",color:"{overlay.popover.color}"},footer:{gap:"0.5rem",padding:"0 {overlay.popover.padding} {overlay.popover.padding} {overlay.popover.padding}"}};var Ht={root:{background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}",shadow:"{overlay.navigation.shadow}",transitionDuration:"{transition.duration}"},list:{padding:"{navigation.list.padding}",gap:"{navigation.list.gap}"},item:{focusBackground:"{navigation.item.focus.background}",activeBackground:"{navigation.item.active.background}",color:"{navigation.item.color}",focusColor:"{navigation.item.focus.color}",activeColor:"{navigation.item.active.color}",padding:"{navigation.item.padding}",borderRadius:"{navigation.item.border.radius}",gap:"{navigation.item.gap}",icon:{color:"{navigation.item.icon.color}",focusColor:"{navigation.item.icon.focus.color}",activeColor:"{navigation.item.icon.active.color}"}},submenu:{mobileIndent:"1rem"},submenuIcon:{size:"{navigation.submenu.icon.size}",color:"{navigation.submenu.icon.color}",focusColor:"{navigation.submenu.icon.focus.color}",activeColor:"{navigation.submenu.icon.active.color}"},separator:{borderColor:"{content.border.color}"}};var zt={root:{transitionDuration:"{transition.duration}"},header:{background:"{content.background}",borderColor:"{datatable.border.color}",color:"{content.color}",borderWidth:"0 0 1px 0",padding:"0.75rem 1rem",sm:{padding:"0.375rem 0.5rem"},lg:{padding:"1rem 1.25rem"}},headerCell:{background:"{content.background}",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",borderColor:"{datatable.border.color}",color:"{content.color}",hoverColor:"{content.hover.color}",selectedColor:"{highlight.color}",gap:"0.5rem",padding:"0.75rem 1rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"},sm:{padding:"0.375rem 0.5rem"},lg:{padding:"1rem 1.25rem"}},columnTitle:{fontWeight:"600"},row:{background:"{content.background}",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",color:"{content.color}",hoverColor:"{content.hover.color}",selectedColor:"{highlight.color}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"}},bodyCell:{borderColor:"{datatable.border.color}",padding:"0.75rem 1rem",sm:{padding:"0.375rem 0.5rem"},lg:{padding:"1rem 1.25rem"}},footerCell:{background:"{content.background}",borderColor:"{datatable.border.color}",color:"{content.color}",padding:"0.75rem 1rem",sm:{padding:"0.375rem 0.5rem"},lg:{padding:"1rem 1.25rem"}},columnFooter:{fontWeight:"600"},footer:{background:"{content.background}",borderColor:"{datatable.border.color}",color:"{content.color}",borderWidth:"0 0 1px 0",padding:"0.75rem 1rem",sm:{padding:"0.375rem 0.5rem"},lg:{padding:"1rem 1.25rem"}},dropPoint:{color:"{primary.color}"},columnResizer:{width:"0.5rem"},resizeIndicator:{width:"1px",color:"{primary.color}"},sortIcon:{color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}",size:"0.875rem"},loadingIcon:{size:"2rem"},rowToggleButton:{hoverBackground:"{content.hover.background}",selectedHoverBackground:"{content.background}",color:"{text.muted.color}",hoverColor:"{text.color}",selectedHoverColor:"{primary.color}",size:"1.75rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},filter:{inlineGap:"0.5rem",overlaySelect:{background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}"},overlayPopover:{background:"{overlay.popover.background}",borderColor:"{overlay.popover.border.color}",borderRadius:"{overlay.popover.border.radius}",color:"{overlay.popover.color}",shadow:"{overlay.popover.shadow}",padding:"{overlay.popover.padding}",gap:"0.5rem"},rule:{borderColor:"{content.border.color}"},constraintList:{padding:"{list.padding}",gap:"{list.gap}"},constraint:{focusBackground:"{list.option.focus.background}",selectedBackground:"{list.option.selected.background}",selectedFocusBackground:"{list.option.selected.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",selectedColor:"{list.option.selected.color}",selectedFocusColor:"{list.option.selected.focus.color}",separator:{borderColor:"{content.border.color}"},padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}"}},paginatorTop:{borderColor:"{datatable.border.color}",borderWidth:"0 0 1px 0"},paginatorBottom:{borderColor:"{datatable.border.color}",borderWidth:"0 0 1px 0"},colorScheme:{light:{root:{borderColor:"{content.border.color}"},row:{stripedBackground:"{surface.50}"},bodyCell:{selectedBorderColor:"{primary.100}"}},dark:{root:{borderColor:"{surface.800}"},row:{stripedBackground:"{surface.950}"},bodyCell:{selectedBorderColor:"{primary.900}"}}}};var Pt={root:{borderColor:"transparent",borderWidth:"0",borderRadius:"0",padding:"0"},header:{background:"{content.background}",color:"{content.color}",borderColor:"{content.border.color}",borderWidth:"0 0 1px 0",padding:"0.75rem 1rem",borderRadius:"0"},content:{background:"{content.background}",color:"{content.color}",borderColor:"transparent",borderWidth:"0",padding:"0",borderRadius:"0"},footer:{background:"{content.background}",color:"{content.color}",borderColor:"{content.border.color}",borderWidth:"1px 0 0 0",padding:"0.75rem 1rem",borderRadius:"0"},paginatorTop:{borderColor:"{content.border.color}",borderWidth:"0 0 1px 0"},paginatorBottom:{borderColor:"{content.border.color}",borderWidth:"1px 0 0 0"}};var Mt={root:{transitionDuration:"{transition.duration}"},panel:{background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}",shadow:"{overlay.popover.shadow}",padding:"{overlay.popover.padding}"},header:{background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",padding:"0 0 0.5rem 0"},title:{gap:"0.5rem",fontWeight:"500"},dropdown:{width:"2.5rem",sm:{width:"2rem"},lg:{width:"3rem"},borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.border.color}",activeBorderColor:"{form.field.border.color}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},inputIcon:{color:"{form.field.icon.color}"},selectMonth:{hoverBackground:"{content.hover.background}",color:"{content.color}",hoverColor:"{content.hover.color}",padding:"0.25rem 0.5rem",borderRadius:"{content.border.radius}"},selectYear:{hoverBackground:"{content.hover.background}",color:"{content.color}",hoverColor:"{content.hover.color}",padding:"0.25rem 0.5rem",borderRadius:"{content.border.radius}"},group:{borderColor:"{content.border.color}",gap:"{overlay.popover.padding}"},dayView:{margin:"0.5rem 0 0 0"},weekDay:{padding:"0.25rem",fontWeight:"500",color:"{content.color}"},date:{hoverBackground:"{content.hover.background}",selectedBackground:"{primary.color}",rangeSelectedBackground:"{highlight.background}",color:"{content.color}",hoverColor:"{content.hover.color}",selectedColor:"{primary.contrast.color}",rangeSelectedColor:"{highlight.color}",width:"2rem",height:"2rem",borderRadius:"50%",padding:"0.25rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},monthView:{margin:"0.5rem 0 0 0"},month:{padding:"0.375rem",borderRadius:"{content.border.radius}"},yearView:{margin:"0.5rem 0 0 0"},year:{padding:"0.375rem",borderRadius:"{content.border.radius}"},buttonbar:{padding:"0.5rem 0 0 0",borderColor:"{content.border.color}"},timePicker:{padding:"0.5rem 0 0 0",borderColor:"{content.border.color}",gap:"0.5rem",buttonGap:"0.25rem"},colorScheme:{light:{dropdown:{background:"{surface.100}",hoverBackground:"{surface.200}",activeBackground:"{surface.300}",color:"{surface.600}",hoverColor:"{surface.700}",activeColor:"{surface.800}"},today:{background:"{surface.200}",color:"{surface.900}"}},dark:{dropdown:{background:"{surface.800}",hoverBackground:"{surface.700}",activeBackground:"{surface.600}",color:"{surface.300}",hoverColor:"{surface.200}",activeColor:"{surface.100}"},today:{background:"{surface.700}",color:"{surface.0}"}}}};var Dt={root:{background:"{overlay.modal.background}",borderColor:"{overlay.modal.border.color}",color:"{overlay.modal.color}",borderRadius:"{overlay.modal.border.radius}",shadow:"{overlay.modal.shadow}"},header:{padding:"{overlay.modal.padding}",gap:"0.5rem"},title:{fontSize:"1.25rem",fontWeight:"600"},content:{padding:"0 {overlay.modal.padding} {overlay.modal.padding} {overlay.modal.padding}"},footer:{padding:"0 {overlay.modal.padding} {overlay.modal.padding} {overlay.modal.padding}",gap:"0.5rem"}};var Ot={root:{borderColor:"{content.border.color}"},content:{background:"{content.background}",color:"{text.color}"},horizontal:{margin:"1rem 0",padding:"0 1rem",content:{padding:"0 0.5rem"}},vertical:{margin:"0 1rem",padding:"0.5rem 0",content:{padding:"0.5rem 0"}}};var Ut={root:{background:"rgba(255, 255, 255, 0.1)",borderColor:"rgba(255, 255, 255, 0.2)",padding:"0.5rem",borderRadius:"{border.radius.xl}"},item:{borderRadius:"{content.border.radius}",padding:"0.5rem",size:"3rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}}};var jt={root:{background:"{overlay.modal.background}",borderColor:"{overlay.modal.border.color}",color:"{overlay.modal.color}",shadow:"{overlay.modal.shadow}"},header:{padding:"{overlay.modal.padding}"},title:{fontSize:"1.5rem",fontWeight:"600"},content:{padding:"0 {overlay.modal.padding} {overlay.modal.padding} {overlay.modal.padding}"},footer:{padding:"{overlay.modal.padding}"}};var Nt={toolbar:{background:"{content.background}",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}"},toolbarItem:{color:"{text.muted.color}",hoverColor:"{text.color}",activeColor:"{primary.color}"},overlay:{background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}",padding:"{list.padding}"},overlayOption:{focusBackground:"{list.option.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}"},content:{background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}"}};var Wt={root:{background:"{content.background}",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}",color:"{content.color}",padding:"0 1.125rem 1.125rem 1.125rem",transitionDuration:"{transition.duration}"},legend:{background:"{content.background}",hoverBackground:"{content.hover.background}",color:"{content.color}",hoverColor:"{content.hover.color}",borderRadius:"{content.border.radius}",borderWidth:"1px",borderColor:"transparent",padding:"0.5rem 0.75rem",gap:"0.5rem",fontWeight:"600",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},toggleIcon:{color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}"},content:{padding:"0"}};var $t={root:{background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}",transitionDuration:"{transition.duration}"},header:{background:"transparent",color:"{text.color}",padding:"1.125rem",borderColor:"unset",borderWidth:"0",borderRadius:"0",gap:"0.5rem"},content:{highlightBorderColor:"{primary.color}",padding:"0 1.125rem 1.125rem 1.125rem",gap:"1rem"},file:{padding:"1rem",gap:"1rem",borderColor:"{content.border.color}",info:{gap:"0.5rem"}},fileList:{gap:"0.5rem"},progressbar:{height:"0.25rem"},basic:{gap:"0.5rem"}};var Vt={root:{color:"{form.field.float.label.color}",focusColor:"{form.field.float.label.focus.color}",activeColor:"{form.field.float.label.active.color}",invalidColor:"{form.field.float.label.invalid.color}",transitionDuration:"0.2s",positionX:"{form.field.padding.x}",positionY:"{form.field.padding.y}",fontWeight:"500",active:{fontSize:"0.75rem",fontWeight:"400"}},over:{active:{top:"-1.25rem"}},in:{input:{paddingTop:"1.5rem",paddingBottom:"{form.field.padding.y}"},active:{top:"{form.field.padding.y}"}},on:{borderRadius:"{border.radius.xs}",active:{background:"{form.field.background}",padding:"0 0.125rem"}}};var Kt={root:{borderWidth:"1px",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}",transitionDuration:"{transition.duration}"},navButton:{background:"rgba(255, 255, 255, 0.1)",hoverBackground:"rgba(255, 255, 255, 0.2)",color:"{surface.100}",hoverColor:"{surface.0}",size:"3rem",gutter:"0.5rem",prev:{borderRadius:"50%"},next:{borderRadius:"50%"},focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},navIcon:{size:"1.5rem"},thumbnailsContent:{background:"{content.background}",padding:"1rem 0.25rem"},thumbnailNavButton:{size:"2rem",borderRadius:"{content.border.radius}",gutter:"0.5rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},thumbnailNavButtonIcon:{size:"1rem"},caption:{background:"rgba(0, 0, 0, 0.5)",color:"{surface.100}",padding:"1rem"},indicatorList:{gap:"0.5rem",padding:"1rem"},indicatorButton:{width:"1rem",height:"1rem",activeBackground:"{primary.color}",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},insetIndicatorList:{background:"rgba(0, 0, 0, 0.5)"},insetIndicatorButton:{background:"rgba(255, 255, 255, 0.4)",hoverBackground:"rgba(255, 255, 255, 0.6)",activeBackground:"rgba(255, 255, 255, 0.9)"},closeButton:{size:"3rem",gutter:"0.5rem",background:"rgba(255, 255, 255, 0.1)",hoverBackground:"rgba(255, 255, 255, 0.2)",color:"{surface.50}",hoverColor:"{surface.0}",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},closeButtonIcon:{size:"1.5rem"},colorScheme:{light:{thumbnailNavButton:{hoverBackground:"{surface.100}",color:"{surface.600}",hoverColor:"{surface.700}"},indicatorButton:{background:"{surface.200}",hoverBackground:"{surface.300}"}},dark:{thumbnailNavButton:{hoverBackground:"{surface.700}",color:"{surface.400}",hoverColor:"{surface.0}"},indicatorButton:{background:"{surface.700}",hoverBackground:"{surface.600}"}}}};var Qt={icon:{color:"{form.field.icon.color}"}};var qt={root:{color:"{form.field.float.label.color}",focusColor:"{form.field.float.label.focus.color}",invalidColor:"{form.field.float.label.invalid.color}",transitionDuration:"0.2s",positionX:"{form.field.padding.x}",top:"{form.field.padding.y}",fontSize:"0.75rem",fontWeight:"400"},input:{paddingTop:"1.5rem",paddingBottom:"{form.field.padding.y}"}};var Yt={root:{transitionDuration:"{transition.duration}"},preview:{icon:{size:"1.5rem"},mask:{background:"{mask.background}",color:"{mask.color}"}},toolbar:{position:{left:"auto",right:"1rem",top:"1rem",bottom:"auto"},blur:"8px",background:"rgba(255,255,255,0.1)",borderColor:"rgba(255,255,255,0.2)",borderWidth:"1px",borderRadius:"30px",padding:".5rem",gap:"0.5rem"},action:{hoverBackground:"rgba(255,255,255,0.1)",color:"{surface.50}",hoverColor:"{surface.0}",size:"3rem",iconSize:"1.5rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}}};var Xt={handle:{size:"15px",hoverSize:"30px",background:"rgba(255,255,255,0.3)",hoverBackground:"rgba(255,255,255,0.3)",borderColor:"unset",hoverBorderColor:"unset",borderWidth:"0",borderRadius:"50%",transitionDuration:"{transition.duration}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"rgba(255,255,255,0.3)",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}}};var Gt={root:{padding:"{form.field.padding.y} {form.field.padding.x}",borderRadius:"{content.border.radius}",gap:"0.5rem"},text:{fontWeight:"500"},icon:{size:"1rem"},colorScheme:{light:{info:{background:"color-mix(in srgb, {blue.50}, transparent 5%)",borderColor:"{blue.200}",color:"{blue.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {blue.500}, transparent 96%)"},success:{background:"color-mix(in srgb, {green.50}, transparent 5%)",borderColor:"{green.200}",color:"{green.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {green.500}, transparent 96%)"},warn:{background:"color-mix(in srgb,{yellow.50}, transparent 5%)",borderColor:"{yellow.200}",color:"{yellow.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {yellow.500}, transparent 96%)"},error:{background:"color-mix(in srgb, {red.50}, transparent 5%)",borderColor:"{red.200}",color:"{red.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {red.500}, transparent 96%)"},secondary:{background:"{surface.100}",borderColor:"{surface.200}",color:"{surface.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.500}, transparent 96%)"},contrast:{background:"{surface.900}",borderColor:"{surface.950}",color:"{surface.50}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.950}, transparent 96%)"}},dark:{info:{background:"color-mix(in srgb, {blue.500}, transparent 84%)",borderColor:"color-mix(in srgb, {blue.700}, transparent 64%)",color:"{blue.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {blue.500}, transparent 96%)"},success:{background:"color-mix(in srgb, {green.500}, transparent 84%)",borderColor:"color-mix(in srgb, {green.700}, transparent 64%)",color:"{green.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {green.500}, transparent 96%)"},warn:{background:"color-mix(in srgb, {yellow.500}, transparent 84%)",borderColor:"color-mix(in srgb, {yellow.700}, transparent 64%)",color:"{yellow.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {yellow.500}, transparent 96%)"},error:{background:"color-mix(in srgb, {red.500}, transparent 84%)",borderColor:"color-mix(in srgb, {red.700}, transparent 64%)",color:"{red.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {red.500}, transparent 96%)"},secondary:{background:"{surface.800}",borderColor:"{surface.700}",color:"{surface.300}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.500}, transparent 96%)"},contrast:{background:"{surface.0}",borderColor:"{surface.100}",color:"{surface.950}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.950}, transparent 96%)"}}}};var Jt={root:{padding:"{form.field.padding.y} {form.field.padding.x}",borderRadius:"{content.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"},transitionDuration:"{transition.duration}"},display:{hoverBackground:"{content.hover.background}",hoverColor:"{content.hover.color}"}};var Zt={root:{background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}"},chip:{borderRadius:"{border.radius.sm}"},colorScheme:{light:{chip:{focusBackground:"{surface.200}",color:"{surface.800}"}},dark:{chip:{focusBackground:"{surface.700}",color:"{surface.0}"}}}};var ei={addon:{background:"{form.field.background}",borderColor:"{form.field.border.color}",color:"{form.field.icon.color}",borderRadius:"{form.field.border.radius}",padding:"0.5rem",minWidth:"2.5rem"}};var oi={root:{transitionDuration:"{transition.duration}"},button:{width:"2.5rem",borderRadius:"{form.field.border.radius}",verticalPadding:"{form.field.padding.y}"},colorScheme:{light:{button:{background:"transparent",hoverBackground:"{surface.100}",activeBackground:"{surface.200}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.border.color}",activeBorderColor:"{form.field.border.color}",color:"{surface.400}",hoverColor:"{surface.500}",activeColor:"{surface.600}"}},dark:{button:{background:"transparent",hoverBackground:"{surface.800}",activeBackground:"{surface.700}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.border.color}",activeBorderColor:"{form.field.border.color}",color:"{surface.400}",hoverColor:"{surface.300}",activeColor:"{surface.200}"}}}};var ri={root:{gap:"0.5rem"},input:{width:"2.5rem",sm:{width:"2rem"},lg:{width:"3rem"}}};var ti={root:{background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledHoverBackground:"{form.field.filled.hover.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",invalidPlaceholderColor:"{form.field.invalid.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}",sm:{fontSize:"{form.field.sm.font.size}",paddingX:"{form.field.sm.padding.x}",paddingY:"{form.field.sm.padding.y}"},lg:{fontSize:"{form.field.lg.font.size}",paddingX:"{form.field.lg.padding.x}",paddingY:"{form.field.lg.padding.y}"}}};var ii={root:{transitionDuration:"{transition.duration}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},value:{background:"{primary.color}"},range:{background:"{content.border.color}"},text:{color:"{text.muted.color}"}};var ni={root:{background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",borderColor:"{form.field.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",shadow:"{form.field.shadow}",borderRadius:"{form.field.border.radius}",transitionDuration:"{form.field.transition.duration}"},list:{padding:"{list.padding}",gap:"{list.gap}",header:{padding:"{list.header.padding}"}},option:{focusBackground:"{list.option.focus.background}",selectedBackground:"{list.option.selected.background}",selectedFocusBackground:"{list.option.selected.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",selectedColor:"{list.option.selected.color}",selectedFocusColor:"{list.option.selected.focus.color}",padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}"},optionGroup:{background:"{list.option.group.background}",color:"{list.option.group.color}",fontWeight:"{list.option.group.font.weight}",padding:"{list.option.group.padding}"},checkmark:{color:"{list.option.color}",gutterStart:"-0.375rem",gutterEnd:"0.375rem"},emptyMessage:{padding:"{list.option.padding}"},colorScheme:{light:{option:{stripedBackground:"{surface.50}"}},dark:{option:{stripedBackground:"{surface.900}"}}}};var ai={root:{background:"{content.background}",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}",color:"{content.color}",gap:"0.5rem",verticalOrientation:{padding:"{navigation.list.padding}",gap:"{navigation.list.gap}"},horizontalOrientation:{padding:"0.5rem 0.75rem",gap:"0.5rem"},transitionDuration:"{transition.duration}"},baseItem:{borderRadius:"{content.border.radius}",padding:"{navigation.item.padding}"},item:{focusBackground:"{navigation.item.focus.background}",activeBackground:"{navigation.item.active.background}",color:"{navigation.item.color}",focusColor:"{navigation.item.focus.color}",activeColor:"{navigation.item.active.color}",padding:"{navigation.item.padding}",borderRadius:"{navigation.item.border.radius}",gap:"{navigation.item.gap}",icon:{color:"{navigation.item.icon.color}",focusColor:"{navigation.item.icon.focus.color}",activeColor:"{navigation.item.icon.active.color}"}},overlay:{padding:"0",background:"{content.background}",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}",color:"{content.color}",shadow:"{overlay.navigation.shadow}",gap:"0.5rem"},submenu:{padding:"{navigation.list.padding}",gap:"{navigation.list.gap}"},submenuLabel:{padding:"{navigation.submenu.label.padding}",fontWeight:"{navigation.submenu.label.font.weight}",background:"{navigation.submenu.label.background.}",color:"{navigation.submenu.label.color}"},submenuIcon:{size:"{navigation.submenu.icon.size}",color:"{navigation.submenu.icon.color}",focusColor:"{navigation.submenu.icon.focus.color}",activeColor:"{navigation.submenu.icon.active.color}"},separator:{borderColor:"{content.border.color}"},mobileButton:{borderRadius:"50%",size:"1.75rem",color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}",hoverBackground:"{content.hover.background}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}}};var si={root:{background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}",shadow:"{overlay.navigation.shadow}",transitionDuration:"{transition.duration}"},list:{padding:"{navigation.list.padding}",gap:"{navigation.list.gap}"},item:{focusBackground:"{navigation.item.focus.background}",color:"{navigation.item.color}",focusColor:"{navigation.item.focus.color}",padding:"{navigation.item.padding}",borderRadius:"{navigation.item.border.radius}",gap:"{navigation.item.gap}",icon:{color:"{navigation.item.icon.color}",focusColor:"{navigation.item.icon.focus.color}"}},submenuLabel:{padding:"{navigation.submenu.label.padding}",fontWeight:"{navigation.submenu.label.font.weight}",background:"{navigation.submenu.label.background}",color:"{navigation.submenu.label.color}"},separator:{borderColor:"{content.border.color}"}};var ci={root:{background:"{content.background}",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}",color:"{content.color}",gap:"0.5rem",padding:"0.5rem 0.75rem",transitionDuration:"{transition.duration}"},baseItem:{borderRadius:"{content.border.radius}",padding:"{navigation.item.padding}"},item:{focusBackground:"{navigation.item.focus.background}",activeBackground:"{navigation.item.active.background}",color:"{navigation.item.color}",focusColor:"{navigation.item.focus.color}",activeColor:"{navigation.item.active.color}",padding:"{navigation.item.padding}",borderRadius:"{navigation.item.border.radius}",gap:"{navigation.item.gap}",icon:{color:"{navigation.item.icon.color}",focusColor:"{navigation.item.icon.focus.color}",activeColor:"{navigation.item.icon.active.color}"}},submenu:{padding:"{navigation.list.padding}",gap:"{navigation.list.gap}",background:"{content.background}",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}",shadow:"{overlay.navigation.shadow}",mobileIndent:"1rem",icon:{size:"{navigation.submenu.icon.size}",color:"{navigation.submenu.icon.color}",focusColor:"{navigation.submenu.icon.focus.color}",activeColor:"{navigation.submenu.icon.active.color}"}},separator:{borderColor:"{content.border.color}"},mobileButton:{borderRadius:"50%",size:"1.75rem",color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}",hoverBackground:"{content.hover.background}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}}};var li={root:{borderRadius:"{content.border.radius}",borderWidth:"1px",transitionDuration:"{transition.duration}"},content:{padding:"0.5rem 0.75rem",gap:"0.5rem",sm:{padding:"0.375rem 0.625rem"},lg:{padding:"0.625rem 0.875rem"}},text:{fontSize:"1rem",fontWeight:"500",sm:{fontSize:"0.875rem"},lg:{fontSize:"1.125rem"}},icon:{size:"1.125rem",sm:{size:"1rem"},lg:{size:"1.25rem"}},closeButton:{width:"1.75rem",height:"1.75rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",offset:"{focus.ring.offset}"}},closeIcon:{size:"1rem",sm:{size:"0.875rem"},lg:{size:"1.125rem"}},outlined:{root:{borderWidth:"1px"}},simple:{content:{padding:"0"}},colorScheme:{light:{info:{background:"color-mix(in srgb, {blue.50}, transparent 5%)",borderColor:"{blue.200}",color:"{blue.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {blue.500}, transparent 96%)",closeButton:{hoverBackground:"{blue.100}",focusRing:{color:"{blue.600}",shadow:"none"}},outlined:{color:"{blue.600}",borderColor:"{blue.600}"},simple:{color:"{blue.600}"}},success:{background:"color-mix(in srgb, {green.50}, transparent 5%)",borderColor:"{green.200}",color:"{green.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {green.500}, transparent 96%)",closeButton:{hoverBackground:"{green.100}",focusRing:{color:"{green.600}",shadow:"none"}},outlined:{color:"{green.600}",borderColor:"{green.600}"},simple:{color:"{green.600}"}},warn:{background:"color-mix(in srgb,{yellow.50}, transparent 5%)",borderColor:"{yellow.200}",color:"{yellow.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {yellow.500}, transparent 96%)",closeButton:{hoverBackground:"{yellow.100}",focusRing:{color:"{yellow.600}",shadow:"none"}},outlined:{color:"{yellow.600}",borderColor:"{yellow.600}"},simple:{color:"{yellow.600}"}},error:{background:"color-mix(in srgb, {red.50}, transparent 5%)",borderColor:"{red.200}",color:"{red.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {red.500}, transparent 96%)",closeButton:{hoverBackground:"{red.100}",focusRing:{color:"{red.600}",shadow:"none"}},outlined:{color:"{red.600}",borderColor:"{red.600}"},simple:{color:"{red.600}"}},secondary:{background:"{surface.100}",borderColor:"{surface.200}",color:"{surface.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.500}, transparent 96%)",closeButton:{hoverBackground:"{surface.200}",focusRing:{color:"{surface.600}",shadow:"none"}},outlined:{color:"{surface.500}",borderColor:"{surface.500}"},simple:{color:"{surface.500}"}},contrast:{background:"{surface.900}",borderColor:"{surface.950}",color:"{surface.50}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.950}, transparent 96%)",closeButton:{hoverBackground:"{surface.800}",focusRing:{color:"{surface.50}",shadow:"none"}},outlined:{color:"{surface.950}",borderColor:"{surface.950}"},simple:{color:"{surface.950}"}}},dark:{info:{background:"color-mix(in srgb, {blue.500}, transparent 84%)",borderColor:"color-mix(in srgb, {blue.700}, transparent 64%)",color:"{blue.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {blue.500}, transparent 96%)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{blue.500}",shadow:"none"}},outlined:{color:"{blue.500}",borderColor:"{blue.500}"},simple:{color:"{blue.500}"}},success:{background:"color-mix(in srgb, {green.500}, transparent 84%)",borderColor:"color-mix(in srgb, {green.700}, transparent 64%)",color:"{green.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {green.500}, transparent 96%)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{green.500}",shadow:"none"}},outlined:{color:"{green.500}",borderColor:"{green.500}"},simple:{color:"{green.500}"}},warn:{background:"color-mix(in srgb, {yellow.500}, transparent 84%)",borderColor:"color-mix(in srgb, {yellow.700}, transparent 64%)",color:"{yellow.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {yellow.500}, transparent 96%)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{yellow.500}",shadow:"none"}},outlined:{color:"{yellow.500}",borderColor:"{yellow.500}"},simple:{color:"{yellow.500}"}},error:{background:"color-mix(in srgb, {red.500}, transparent 84%)",borderColor:"color-mix(in srgb, {red.700}, transparent 64%)",color:"{red.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {red.500}, transparent 96%)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{red.500}",shadow:"none"}},outlined:{color:"{red.500}",borderColor:"{red.500}"},simple:{color:"{red.500}"}},secondary:{background:"{surface.800}",borderColor:"{surface.700}",color:"{surface.300}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.500}, transparent 96%)",closeButton:{hoverBackground:"{surface.700}",focusRing:{color:"{surface.300}",shadow:"none"}},outlined:{color:"{surface.400}",borderColor:"{surface.400}"},simple:{color:"{surface.400}"}},contrast:{background:"{surface.0}",borderColor:"{surface.100}",color:"{surface.950}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.950}, transparent 96%)",closeButton:{hoverBackground:"{surface.100}",focusRing:{color:"{surface.950}",shadow:"none"}},outlined:{color:"{surface.0}",borderColor:"{surface.0}"},simple:{color:"{surface.0}"}}}}};var di={root:{borderRadius:"{content.border.radius}",gap:"1rem"},meters:{background:"{content.border.color}",size:"0.5rem"},label:{gap:"0.5rem"},labelMarker:{size:"0.5rem"},labelIcon:{size:"1rem"},labelList:{verticalGap:"0.5rem",horizontalGap:"1rem"}};var ui={root:{background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledHoverBackground:"{form.field.filled.hover.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",invalidPlaceholderColor:"{form.field.invalid.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}",sm:{fontSize:"{form.field.sm.font.size}",paddingX:"{form.field.sm.padding.x}",paddingY:"{form.field.sm.padding.y}"},lg:{fontSize:"{form.field.lg.font.size}",paddingX:"{form.field.lg.padding.x}",paddingY:"{form.field.lg.padding.y}"}},dropdown:{width:"2.5rem",color:"{form.field.icon.color}"},overlay:{background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}"},list:{padding:"{list.padding}",gap:"{list.gap}",header:{padding:"{list.header.padding}"}},option:{focusBackground:"{list.option.focus.background}",selectedBackground:"{list.option.selected.background}",selectedFocusBackground:"{list.option.selected.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",selectedColor:"{list.option.selected.color}",selectedFocusColor:"{list.option.selected.focus.color}",padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}",gap:"0.5rem"},optionGroup:{background:"{list.option.group.background}",color:"{list.option.group.color}",fontWeight:"{list.option.group.font.weight}",padding:"{list.option.group.padding}"},clearIcon:{color:"{form.field.icon.color}"},chip:{borderRadius:"{border.radius.sm}"},emptyMessage:{padding:"{list.option.padding}"}};var fi={root:{gap:"1.125rem"},controls:{gap:"0.5rem"}};var gi={root:{gutter:"0.75rem",transitionDuration:"{transition.duration}"},node:{background:"{content.background}",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",borderColor:"{content.border.color}",color:"{content.color}",selectedColor:"{highlight.color}",hoverColor:"{content.hover.color}",padding:"0.75rem 1rem",toggleablePadding:"0.75rem 1rem 1.25rem 1rem",borderRadius:"{content.border.radius}"},nodeToggleButton:{background:"{content.background}",hoverBackground:"{content.hover.background}",borderColor:"{content.border.color}",color:"{text.muted.color}",hoverColor:"{text.color}",size:"1.5rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},connector:{color:"{content.border.color}",borderRadius:"{content.border.radius}",height:"24px"}};var pi={root:{outline:{width:"2px",color:"{content.background}"}}};var hi={root:{padding:"0.5rem 1rem",gap:"0.25rem",borderRadius:"{content.border.radius}",background:"{content.background}",color:"{content.color}",transitionDuration:"{transition.duration}"},navButton:{background:"transparent",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}",selectedColor:"{highlight.color}",width:"2.5rem",height:"2.5rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},currentPageReport:{color:"{text.muted.color}"},jumpToPageInput:{maxWidth:"2.5rem"}};var mi={root:{background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}"},header:{background:"transparent",color:"{text.color}",padding:"1.125rem",borderColor:"{content.border.color}",borderWidth:"0",borderRadius:"0"},toggleableHeader:{padding:"0.375rem 1.125rem"},title:{fontWeight:"600"},content:{padding:"0 1.125rem 1.125rem 1.125rem"},footer:{padding:"0 1.125rem 1.125rem 1.125rem"}};var bi={root:{gap:"0.5rem",transitionDuration:"{transition.duration}"},panel:{background:"{content.background}",borderColor:"{content.border.color}",borderWidth:"1px",color:"{content.color}",padding:"0.25rem 0.25rem",borderRadius:"{content.border.radius}",first:{borderWidth:"1px",topBorderRadius:"{content.border.radius}"},last:{borderWidth:"1px",bottomBorderRadius:"{content.border.radius}"}},item:{focusBackground:"{navigation.item.focus.background}",color:"{navigation.item.color}",focusColor:"{navigation.item.focus.color}",gap:"0.5rem",padding:"{navigation.item.padding}",borderRadius:"{content.border.radius}",icon:{color:"{navigation.item.icon.color}",focusColor:"{navigation.item.icon.focus.color}"}},submenu:{indent:"1rem"},submenuIcon:{color:"{navigation.submenu.icon.color}",focusColor:"{navigation.submenu.icon.focus.color}"}};var vi={meter:{background:"{content.border.color}",borderRadius:"{content.border.radius}",height:".75rem"},icon:{color:"{form.field.icon.color}"},overlay:{background:"{overlay.popover.background}",borderColor:"{overlay.popover.border.color}",borderRadius:"{overlay.popover.border.radius}",color:"{overlay.popover.color}",padding:"{overlay.popover.padding}",shadow:"{overlay.popover.shadow}"},content:{gap:"0.5rem"},colorScheme:{light:{strength:{weakBackground:"{red.500}",mediumBackground:"{amber.500}",strongBackground:"{green.500}"}},dark:{strength:{weakBackground:"{red.400}",mediumBackground:"{amber.400}",strongBackground:"{green.400}"}}}};var ki={root:{gap:"1.125rem"},controls:{gap:"0.5rem"}};var yi={root:{background:"{overlay.popover.background}",borderColor:"{overlay.popover.border.color}",color:"{overlay.popover.color}",borderRadius:"{overlay.popover.border.radius}",shadow:"{overlay.popover.shadow}",gutter:"10px",arrowOffset:"1.25rem"},content:{padding:"{overlay.popover.padding}"}};var xi={root:{background:"{content.border.color}",borderRadius:"{content.border.radius}",height:"1.25rem"},value:{background:"{primary.color}"},label:{color:"{primary.contrast.color}",fontSize:"0.75rem",fontWeight:"600"}};var Ci={colorScheme:{light:{root:{colorOne:"{red.500}",colorTwo:"{blue.500}",colorThree:"{green.500}",colorFour:"{yellow.500}"}},dark:{root:{colorOne:"{red.400}",colorTwo:"{blue.400}",colorThree:"{green.400}",colorFour:"{yellow.400}"}}}};var wi={root:{width:"1.25rem",height:"1.25rem",background:"{form.field.background}",checkedBackground:"{primary.color}",checkedHoverBackground:"{primary.hover.color}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.border.color}",checkedBorderColor:"{primary.color}",checkedHoverBorderColor:"{primary.hover.color}",checkedFocusBorderColor:"{primary.color}",checkedDisabledBorderColor:"{form.field.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",shadow:"{form.field.shadow}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}",sm:{width:"1rem",height:"1rem"},lg:{width:"1.5rem",height:"1.5rem"}},icon:{size:"0.75rem",checkedColor:"{primary.contrast.color}",checkedHoverColor:"{primary.contrast.color}",disabledColor:"{form.field.disabled.color}",sm:{size:"0.5rem"},lg:{size:"1rem"}}};var _i={root:{gap:"0.25rem",transitionDuration:"{transition.duration}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},icon:{size:"1rem",color:"{text.muted.color}",hoverColor:"{primary.color}",activeColor:"{primary.color}"}};var Si={colorScheme:{light:{root:{background:"rgba(0,0,0,0.1)"}},dark:{root:{background:"rgba(255,255,255,0.3)"}}}};var Ti={root:{transitionDuration:"{transition.duration}"},bar:{size:"9px",borderRadius:"{border.radius.sm}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},colorScheme:{light:{bar:{background:"{surface.100}"}},dark:{bar:{background:"{surface.800}"}}}};var Bi={root:{background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledHoverBackground:"{form.field.filled.hover.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",invalidPlaceholderColor:"{form.field.invalid.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}",sm:{fontSize:"{form.field.sm.font.size}",paddingX:"{form.field.sm.padding.x}",paddingY:"{form.field.sm.padding.y}"},lg:{fontSize:"{form.field.lg.font.size}",paddingX:"{form.field.lg.padding.x}",paddingY:"{form.field.lg.padding.y}"}},dropdown:{width:"2.5rem",color:"{form.field.icon.color}"},overlay:{background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}"},list:{padding:"{list.padding}",gap:"{list.gap}",header:{padding:"{list.header.padding}"}},option:{focusBackground:"{list.option.focus.background}",selectedBackground:"{list.option.selected.background}",selectedFocusBackground:"{list.option.selected.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",selectedColor:"{list.option.selected.color}",selectedFocusColor:"{list.option.selected.focus.color}",padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}"},optionGroup:{background:"{list.option.group.background}",color:"{list.option.group.color}",fontWeight:"{list.option.group.font.weight}",padding:"{list.option.group.padding}"},clearIcon:{color:"{form.field.icon.color}"},checkmark:{color:"{list.option.color}",gutterStart:"-0.375rem",gutterEnd:"0.375rem"},emptyMessage:{padding:"{list.option.padding}"}};var Ii={root:{borderRadius:"{form.field.border.radius}"},colorScheme:{light:{root:{invalidBorderColor:"{form.field.invalid.border.color}"}},dark:{root:{invalidBorderColor:"{form.field.invalid.border.color}"}}}};var Ri={root:{borderRadius:"{content.border.radius}"},colorScheme:{light:{root:{background:"{surface.200}",animationBackground:"rgba(255,255,255,0.4)"}},dark:{root:{background:"rgba(255, 255, 255, 0.06)",animationBackground:"rgba(255, 255, 255, 0.04)"}}}};var Ai={root:{transitionDuration:"{transition.duration}"},track:{background:"{content.border.color}",borderRadius:"{content.border.radius}",size:"3px"},range:{background:"{primary.color}"},handle:{width:"20px",height:"20px",borderRadius:"50%",background:"{content.border.color}",hoverBackground:"{content.border.color}",content:{borderRadius:"50%",hoverBackground:"{content.background}",width:"16px",height:"16px",shadow:"0px 0.5px 0px 0px rgba(0, 0, 0, 0.08), 0px 1px 1px 0px rgba(0, 0, 0, 0.14)"},focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},colorScheme:{light:{handle:{content:{background:"{surface.0}"}}},dark:{handle:{content:{background:"{surface.950}"}}}}};var Ei={root:{gap:"0.5rem",transitionDuration:"{transition.duration}"}};var Li={root:{borderRadius:"{form.field.border.radius}",roundedBorderRadius:"2rem",raisedShadow:"0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12)"}};var Fi={root:{background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",transitionDuration:"{transition.duration}"},gutter:{background:"{content.border.color}"},handle:{size:"24px",background:"transparent",borderRadius:"{content.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}}};var Hi={root:{transitionDuration:"{transition.duration}"},separator:{background:"{content.border.color}",activeBackground:"{primary.color}",margin:"0 0 0 1.625rem",size:"2px"},step:{padding:"0.5rem",gap:"1rem"},stepHeader:{padding:"0",borderRadius:"{content.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"},gap:"0.5rem"},stepTitle:{color:"{text.muted.color}",activeColor:"{primary.color}",fontWeight:"500"},stepNumber:{background:"{content.background}",activeBackground:"{content.background}",borderColor:"{content.border.color}",activeBorderColor:"{content.border.color}",color:"{text.muted.color}",activeColor:"{primary.color}",size:"2rem",fontSize:"1.143rem",fontWeight:"500",borderRadius:"50%",shadow:"0px 0.5px 0px 0px rgba(0, 0, 0, 0.06), 0px 1px 1px 0px rgba(0, 0, 0, 0.12)"},steppanels:{padding:"0.875rem 0.5rem 1.125rem 0.5rem"},steppanel:{background:"{content.background}",color:"{content.color}",padding:"0",indent:"1rem"}};var zi={root:{transitionDuration:"{transition.duration}"},separator:{background:"{content.border.color}"},itemLink:{borderRadius:"{content.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"},gap:"0.5rem"},itemLabel:{color:"{text.muted.color}",activeColor:"{primary.color}",fontWeight:"500"},itemNumber:{background:"{content.background}",activeBackground:"{content.background}",borderColor:"{content.border.color}",activeBorderColor:"{content.border.color}",color:"{text.muted.color}",activeColor:"{primary.color}",size:"2rem",fontSize:"1.143rem",fontWeight:"500",borderRadius:"50%",shadow:"0px 0.5px 0px 0px rgba(0, 0, 0, 0.06), 0px 1px 1px 0px rgba(0, 0, 0, 0.12)"}};var Pi={root:{transitionDuration:"{transition.duration}"},tablist:{borderWidth:"0 0 1px 0",background:"{content.background}",borderColor:"{content.border.color}"},item:{background:"transparent",hoverBackground:"transparent",activeBackground:"transparent",borderWidth:"0 0 1px 0",borderColor:"{content.border.color}",hoverBorderColor:"{content.border.color}",activeBorderColor:"{primary.color}",color:"{text.muted.color}",hoverColor:"{text.color}",activeColor:"{primary.color}",padding:"1rem 1.125rem",fontWeight:"600",margin:"0 0 -1px 0",gap:"0.5rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},itemIcon:{color:"{text.muted.color}",hoverColor:"{text.color}",activeColor:"{primary.color}"},activeBar:{height:"1px",bottom:"-1px",background:"{primary.color}"}};var Mi={root:{transitionDuration:"{transition.duration}"},tablist:{borderWidth:"0 0 1px 0",background:"{content.background}",borderColor:"{content.border.color}"},tab:{background:"transparent",hoverBackground:"transparent",activeBackground:"transparent",borderWidth:"0 0 1px 0",borderColor:"{content.border.color}",hoverBorderColor:"{content.border.color}",activeBorderColor:"{primary.color}",color:"{text.muted.color}",hoverColor:"{text.color}",activeColor:"{primary.color}",padding:"1rem 1.125rem",fontWeight:"600",margin:"0 0 -1px 0",gap:"0.5rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"}},tabpanel:{background:"{content.background}",color:"{content.color}",padding:"0.875rem 1.125rem 1.125rem 1.125rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"inset {focus.ring.shadow}"}},navButton:{background:"{content.background}",color:"{text.muted.color}",hoverColor:"{text.color}",width:"2.5rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"}},activeBar:{height:"1px",bottom:"-1px",background:"{primary.color}"},colorScheme:{light:{navButton:{shadow:"0px 0px 10px 50px rgba(255, 255, 255, 0.6)"}},dark:{navButton:{shadow:"0px 0px 10px 50px color-mix(in srgb, {content.background}, transparent 50%)"}}}};var Di={root:{transitionDuration:"{transition.duration}"},tabList:{background:"{content.background}",borderColor:"{content.border.color}"},tab:{borderColor:"{content.border.color}",activeBorderColor:"{primary.color}",color:"{text.muted.color}",hoverColor:"{text.color}",activeColor:"{primary.color}"},tabPanel:{background:"{content.background}",color:"{content.color}"},navButton:{background:"{content.background}",color:"{text.muted.color}",hoverColor:"{text.color}"},colorScheme:{light:{navButton:{shadow:"0px 0px 10px 50px rgba(255, 255, 255, 0.6)"}},dark:{navButton:{shadow:"0px 0px 10px 50px color-mix(in srgb, {content.background}, transparent 50%)"}}}};var Oi={root:{fontSize:"0.875rem",fontWeight:"700",padding:"0.25rem 0.5rem",gap:"0.25rem",borderRadius:"{content.border.radius}",roundedBorderRadius:"{border.radius.xl}"},icon:{size:"0.75rem"},colorScheme:{light:{primary:{background:"{primary.100}",color:"{primary.700}"},secondary:{background:"{surface.100}",color:"{surface.600}"},success:{background:"{green.100}",color:"{green.700}"},info:{background:"{sky.100}",color:"{sky.700}"},warn:{background:"{orange.100}",color:"{orange.700}"},danger:{background:"{red.100}",color:"{red.700}"},contrast:{background:"{surface.950}",color:"{surface.0}"}},dark:{primary:{background:"color-mix(in srgb, {primary.500}, transparent 84%)",color:"{primary.300}"},secondary:{background:"{surface.800}",color:"{surface.300}"},success:{background:"color-mix(in srgb, {green.500}, transparent 84%)",color:"{green.300}"},info:{background:"color-mix(in srgb, {sky.500}, transparent 84%)",color:"{sky.300}"},warn:{background:"color-mix(in srgb, {orange.500}, transparent 84%)",color:"{orange.300}"},danger:{background:"color-mix(in srgb, {red.500}, transparent 84%)",color:"{red.300}"},contrast:{background:"{surface.0}",color:"{surface.950}"}}}};var Ui={root:{background:"{form.field.background}",borderColor:"{form.field.border.color}",color:"{form.field.color}",height:"18rem",padding:"{form.field.padding.y} {form.field.padding.x}",borderRadius:"{form.field.border.radius}"},prompt:{gap:"0.25rem"},commandResponse:{margin:"2px 0"}};var ji={root:{background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledHoverBackground:"{form.field.filled.hover.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",invalidPlaceholderColor:"{form.field.invalid.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}",sm:{fontSize:"{form.field.sm.font.size}",paddingX:"{form.field.sm.padding.x}",paddingY:"{form.field.sm.padding.y}"},lg:{fontSize:"{form.field.lg.font.size}",paddingX:"{form.field.lg.padding.x}",paddingY:"{form.field.lg.padding.y}"}}};var Ni={root:{background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}",shadow:"{overlay.navigation.shadow}",transitionDuration:"{transition.duration}"},list:{padding:"{navigation.list.padding}",gap:"{navigation.list.gap}"},item:{focusBackground:"{navigation.item.focus.background}",activeBackground:"{navigation.item.active.background}",color:"{navigation.item.color}",focusColor:"{navigation.item.focus.color}",activeColor:"{navigation.item.active.color}",padding:"{navigation.item.padding}",borderRadius:"{navigation.item.border.radius}",gap:"{navigation.item.gap}",icon:{color:"{navigation.item.icon.color}",focusColor:"{navigation.item.icon.focus.color}",activeColor:"{navigation.item.icon.active.color}"}},submenu:{mobileIndent:"1rem"},submenuIcon:{size:"{navigation.submenu.icon.size}",color:"{navigation.submenu.icon.color}",focusColor:"{navigation.submenu.icon.focus.color}",activeColor:"{navigation.submenu.icon.active.color}"},separator:{borderColor:"{content.border.color}"}};var Wi={event:{minHeight:"5rem"},horizontal:{eventContent:{padding:"1rem 0"}},vertical:{eventContent:{padding:"0 1rem"}},eventMarker:{size:"1.125rem",borderRadius:"50%",borderWidth:"2px",background:"{content.background}",borderColor:"{content.border.color}",content:{borderRadius:"50%",size:"0.375rem",background:"{primary.color}",insetShadow:"0px 0.5px 0px 0px rgba(0, 0, 0, 0.06), 0px 1px 1px 0px rgba(0, 0, 0, 0.12)"}},eventConnector:{color:"{content.border.color}",size:"2px"}};var $i={root:{width:"25rem",borderRadius:"{content.border.radius}",borderWidth:"1px",transitionDuration:"{transition.duration}"},icon:{size:"1.125rem"},content:{padding:"{overlay.popover.padding}",gap:"0.5rem"},text:{gap:"0.5rem"},summary:{fontWeight:"500",fontSize:"1rem"},detail:{fontWeight:"500",fontSize:"0.875rem"},closeButton:{width:"1.75rem",height:"1.75rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",offset:"{focus.ring.offset}"}},closeIcon:{size:"1rem"},colorScheme:{light:{root:{blur:"1.5px"},info:{background:"color-mix(in srgb, {blue.50}, transparent 5%)",borderColor:"{blue.200}",color:"{blue.600}",detailColor:"{surface.700}",shadow:"0px 4px 8px 0px color-mix(in srgb, {blue.500}, transparent 96%)",closeButton:{hoverBackground:"{blue.100}",focusRing:{color:"{blue.600}",shadow:"none"}}},success:{background:"color-mix(in srgb, {green.50}, transparent 5%)",borderColor:"{green.200}",color:"{green.600}",detailColor:"{surface.700}",shadow:"0px 4px 8px 0px color-mix(in srgb, {green.500}, transparent 96%)",closeButton:{hoverBackground:"{green.100}",focusRing:{color:"{green.600}",shadow:"none"}}},warn:{background:"color-mix(in srgb,{yellow.50}, transparent 5%)",borderColor:"{yellow.200}",color:"{yellow.600}",detailColor:"{surface.700}",shadow:"0px 4px 8px 0px color-mix(in srgb, {yellow.500}, transparent 96%)",closeButton:{hoverBackground:"{yellow.100}",focusRing:{color:"{yellow.600}",shadow:"none"}}},error:{background:"color-mix(in srgb, {red.50}, transparent 5%)",borderColor:"{red.200}",color:"{red.600}",detailColor:"{surface.700}",shadow:"0px 4px 8px 0px color-mix(in srgb, {red.500}, transparent 96%)",closeButton:{hoverBackground:"{red.100}",focusRing:{color:"{red.600}",shadow:"none"}}},secondary:{background:"{surface.100}",borderColor:"{surface.200}",color:"{surface.600}",detailColor:"{surface.700}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.500}, transparent 96%)",closeButton:{hoverBackground:"{surface.200}",focusRing:{color:"{surface.600}",shadow:"none"}}},contrast:{background:"{surface.900}",borderColor:"{surface.950}",color:"{surface.50}",detailColor:"{surface.0}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.950}, transparent 96%)",closeButton:{hoverBackground:"{surface.800}",focusRing:{color:"{surface.50}",shadow:"none"}}}},dark:{root:{blur:"10px"},info:{background:"color-mix(in srgb, {blue.500}, transparent 84%)",borderColor:"color-mix(in srgb, {blue.700}, transparent 64%)",color:"{blue.500}",detailColor:"{surface.0}",shadow:"0px 4px 8px 0px color-mix(in srgb, {blue.500}, transparent 96%)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{blue.500}",shadow:"none"}}},success:{background:"color-mix(in srgb, {green.500}, transparent 84%)",borderColor:"color-mix(in srgb, {green.700}, transparent 64%)",color:"{green.500}",detailColor:"{surface.0}",shadow:"0px 4px 8px 0px color-mix(in srgb, {green.500}, transparent 96%)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{green.500}",shadow:"none"}}},warn:{background:"color-mix(in srgb, {yellow.500}, transparent 84%)",borderColor:"color-mix(in srgb, {yellow.700}, transparent 64%)",color:"{yellow.500}",detailColor:"{surface.0}",shadow:"0px 4px 8px 0px color-mix(in srgb, {yellow.500}, transparent 96%)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{yellow.500}",shadow:"none"}}},error:{background:"color-mix(in srgb, {red.500}, transparent 84%)",borderColor:"color-mix(in srgb, {red.700}, transparent 64%)",color:"{red.500}",detailColor:"{surface.0}",shadow:"0px 4px 8px 0px color-mix(in srgb, {red.500}, transparent 96%)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{red.500}",shadow:"none"}}},secondary:{background:"{surface.800}",borderColor:"{surface.700}",color:"{surface.300}",detailColor:"{surface.0}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.500}, transparent 96%)",closeButton:{hoverBackground:"{surface.700}",focusRing:{color:"{surface.300}",shadow:"none"}}},contrast:{background:"{surface.0}",borderColor:"{surface.100}",color:"{surface.950}",detailColor:"{surface.950}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.950}, transparent 96%)",closeButton:{hoverBackground:"{surface.100}",focusRing:{color:"{surface.950}",shadow:"none"}}}}}};var Vi={root:{padding:"0.25rem",borderRadius:"{content.border.radius}",gap:"0.5rem",fontWeight:"500",disabledBackground:"{form.field.disabled.background}",disabledBorderColor:"{form.field.disabled.background}",disabledColor:"{form.field.disabled.color}",invalidBorderColor:"{form.field.invalid.border.color}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}",sm:{fontSize:"{form.field.sm.font.size}",padding:"0.25rem"},lg:{fontSize:"{form.field.lg.font.size}",padding:"0.25rem"}},icon:{disabledColor:"{form.field.disabled.color}"},content:{padding:"0.25rem 0.75rem",borderRadius:"{content.border.radius}",checkedShadow:"0px 1px 2px 0px rgba(0, 0, 0, 0.02), 0px 1px 2px 0px rgba(0, 0, 0, 0.04)",sm:{padding:"0.25rem 0.75rem"},lg:{padding:"0.25rem 0.75rem"}},colorScheme:{light:{root:{background:"{surface.100}",checkedBackground:"{surface.100}",hoverBackground:"{surface.100}",borderColor:"{surface.100}",color:"{surface.500}",hoverColor:"{surface.700}",checkedColor:"{surface.900}",checkedBorderColor:"{surface.100}"},content:{checkedBackground:"{surface.0}"},icon:{color:"{surface.500}",hoverColor:"{surface.700}",checkedColor:"{surface.900}"}},dark:{root:{background:"{surface.950}",checkedBackground:"{surface.950}",hoverBackground:"{surface.950}",borderColor:"{surface.950}",color:"{surface.400}",hoverColor:"{surface.300}",checkedColor:"{surface.0}",checkedBorderColor:"{surface.950}"},content:{checkedBackground:"{surface.800}"},icon:{color:"{surface.400}",hoverColor:"{surface.300}",checkedColor:"{surface.0}"}}}};var Ki={root:{width:"2.5rem",height:"1.5rem",borderRadius:"30px",gap:"0.25rem",shadow:"{form.field.shadow}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"},borderWidth:"1px",borderColor:"transparent",hoverBorderColor:"transparent",checkedBorderColor:"transparent",checkedHoverBorderColor:"transparent",invalidBorderColor:"{form.field.invalid.border.color}",transitionDuration:"{form.field.transition.duration}",slideDuration:"0.2s"},handle:{borderRadius:"50%",size:"1rem"},colorScheme:{light:{root:{background:"{surface.300}",disabledBackground:"{form.field.disabled.background}",hoverBackground:"{surface.400}",checkedBackground:"{primary.color}",checkedHoverBackground:"{primary.hover.color}"},handle:{background:"{surface.0}",disabledBackground:"{form.field.disabled.color}",hoverBackground:"{surface.0}",checkedBackground:"{surface.0}",checkedHoverBackground:"{surface.0}",color:"{text.muted.color}",hoverColor:"{text.color}",checkedColor:"{primary.color}",checkedHoverColor:"{primary.hover.color}"}},dark:{root:{background:"{surface.700}",disabledBackground:"{surface.600}",hoverBackground:"{surface.600}",checkedBackground:"{primary.color}",checkedHoverBackground:"{primary.hover.color}"},handle:{background:"{surface.400}",disabledBackground:"{surface.900}",hoverBackground:"{surface.300}",checkedBackground:"{surface.900}",checkedHoverBackground:"{surface.900}",color:"{surface.900}",hoverColor:"{surface.800}",checkedColor:"{primary.color}",checkedHoverColor:"{primary.hover.color}"}}}};var Qi={root:{background:"{content.background}",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}",color:"{content.color}",gap:"0.5rem",padding:"0.75rem"}};var qi={root:{maxWidth:"12.5rem",gutter:"0.25rem",shadow:"{overlay.popover.shadow}",padding:"0.5rem 0.75rem",borderRadius:"{overlay.popover.border.radius}"},colorScheme:{light:{root:{background:"{surface.700}",color:"{surface.0}"}},dark:{root:{background:"{surface.700}",color:"{surface.0}"}}}};var Yi={root:{background:"{content.background}",color:"{content.color}",padding:"1rem",gap:"2px",indent:"1rem",transitionDuration:"{transition.duration}"},node:{padding:"0.25rem 0.5rem",borderRadius:"{content.border.radius}",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",color:"{text.color}",hoverColor:"{text.hover.color}",selectedColor:"{highlight.color}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"},gap:"0.25rem"},nodeIcon:{color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}",selectedColor:"{highlight.color}"},nodeToggleButton:{borderRadius:"50%",size:"1.75rem",hoverBackground:"{content.hover.background}",selectedHoverBackground:"{content.background}",color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}",selectedHoverColor:"{primary.color}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},loadingIcon:{size:"2rem"},filter:{margin:"0 0 0.5rem 0"}};var Xi={root:{background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledHoverBackground:"{form.field.filled.hover.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",invalidPlaceholderColor:"{form.field.invalid.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}",sm:{fontSize:"{form.field.sm.font.size}",paddingX:"{form.field.sm.padding.x}",paddingY:"{form.field.sm.padding.y}"},lg:{fontSize:"{form.field.lg.font.size}",paddingX:"{form.field.lg.padding.x}",paddingY:"{form.field.lg.padding.y}"}},dropdown:{width:"2.5rem",color:"{form.field.icon.color}"},overlay:{background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}"},tree:{padding:"{list.padding}"},clearIcon:{color:"{form.field.icon.color}"},emptyMessage:{padding:"{list.option.padding}"},chip:{borderRadius:"{border.radius.sm}"}};var Gi={root:{transitionDuration:"{transition.duration}"},header:{background:"{content.background}",borderColor:"{treetable.border.color}",color:"{content.color}",borderWidth:"0 0 1px 0",padding:"0.75rem 1rem"},headerCell:{background:"{content.background}",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",borderColor:"{treetable.border.color}",color:"{content.color}",hoverColor:"{content.hover.color}",selectedColor:"{highlight.color}",gap:"0.5rem",padding:"0.75rem 1rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"}},columnTitle:{fontWeight:"600"},row:{background:"{content.background}",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",color:"{content.color}",hoverColor:"{content.hover.color}",selectedColor:"{highlight.color}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"}},bodyCell:{borderColor:"{treetable.border.color}",padding:"0.75rem 1rem",gap:"0.5rem"},footerCell:{background:"{content.background}",borderColor:"{treetable.border.color}",color:"{content.color}",padding:"0.75rem 1rem"},columnFooter:{fontWeight:"600"},footer:{background:"{content.background}",borderColor:"{treetable.border.color}",color:"{content.color}",borderWidth:"0 0 1px 0",padding:"0.75rem 1rem"},columnResizer:{width:"0.5rem"},resizeIndicator:{width:"1px",color:"{primary.color}"},sortIcon:{color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}",size:"0.875rem"},loadingIcon:{size:"2rem"},nodeToggleButton:{hoverBackground:"{content.hover.background}",selectedHoverBackground:"{content.background}",color:"{text.muted.color}",hoverColor:"{text.color}",selectedHoverColor:"{primary.color}",size:"1.75rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},paginatorTop:{borderColor:"{content.border.color}",borderWidth:"0 0 1px 0"},paginatorBottom:{borderColor:"{content.border.color}",borderWidth:"0 0 1px 0"},colorScheme:{light:{root:{borderColor:"{content.border.color}"},bodyCell:{selectedBorderColor:"{primary.100}"}},dark:{root:{borderColor:"{surface.800}"},bodyCell:{selectedBorderColor:"{primary.900}"}}}};var Ji={loader:{mask:{background:"{content.background}",color:"{text.muted.color}"},icon:{size:"2rem"}}};var Zi=Uo(ee({},Ct),{components:{accordion:vt,autocomplete:kt,avatar:yt,badge:xt,blockui:wt,breadcrumb:_t,button:St,datepicker:Mt,card:Tt,carousel:Bt,cascadeselect:It,checkbox:Rt,chip:At,colorpicker:Et,confirmdialog:Lt,confirmpopup:Ft,contextmenu:Ht,dataview:Pt,datatable:zt,dialog:Dt,divider:Ot,dock:Ut,drawer:jt,editor:Nt,fieldset:Wt,fileupload:$t,iftalabel:qt,floatlabel:Vt,galleria:Kt,iconfield:Qt,image:Yt,imagecompare:Xt,inlinemessage:Gt,inplace:Jt,inputchips:Zt,inputgroup:ei,inputnumber:oi,inputotp:ri,inputtext:ti,knob:ii,listbox:ni,megamenu:ai,menu:si,menubar:ci,message:li,metergroup:di,multiselect:ui,orderlist:fi,organizationchart:gi,overlaybadge:pi,popover:yi,paginator:hi,password:vi,panel:mi,panelmenu:bi,picklist:ki,progressbar:xi,progressspinner:Ci,radiobutton:wi,rating:_i,scrollpanel:Ti,select:Bi,selectbutton:Ii,skeleton:Ri,slider:Ai,speeddial:Ei,splitter:Fi,splitbutton:Li,stepper:Hi,steps:zi,tabmenu:Pi,tabs:Mi,tabview:Di,textarea:ji,tieredmenu:Ni,tag:Oi,terminal:Ui,timeline:Wi,togglebutton:Vi,toggleswitch:Ki,tree:Yi,treeselect:Xi,treetable:Gi,toast:$i,toolbar:Qi,virtualscroller:Ji,tooltip:qi,ripple:Si}});var en=Fr(Zi,{semantic:{primary:{50:"{blue.50}",100:"{blue.100}",200:"{blue.200}",300:"{blue.300}",400:"{blue.400}",500:"{blue.500}",600:"{blue.600}",700:"{blue.700}",800:"{blue.800}",900:"{blue.900}",950:"{blue.950}"}}});var on={providers:[fr({eventCoalescing:!0}),Sr(rt),tt(),vr(yr(),kr([bt])),Hr({theme:{preset:en}}),ot(et({environment:mt,registerLocaleFn:()=>new Promise(r=>{r(null)})})),gt()]};var Pn=["*"];function Mn(r,i){if(r&1&&(C(0,"span",3),ce(1),_()),r&2){let e=g();h(),le(e.label)}}function Dn(r,i){if(r&1&&I(0,"span",5),r&2){let e=g(2);D(e.icon),u("ngClass","p-avatar-icon")}}function On(r,i){if(r&1&&k(0,Dn,1,3,"span",4),r&2){let e=g(),o=Z(5);u("ngIf",e.icon)("ngIfElse",o)}}function Un(r,i){if(r&1){let e=ae();C(0,"img",7),J("error",function(t){F(e);let n=g(2);return H(n.imageError(t))}),_()}if(r&2){let e=g(2);u("src",e.image,Oe),E("aria-label",e.ariaLabel)}}function jn(r,i){if(r&1&&k(0,Un,1,2,"img",6),r&2){let e=g();u("ngIf",e.image)}}var Nn=({dt:r})=>`
.p-avatar {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: ${r("avatar.width")};
    height: ${r("avatar.height")};
    font-size: ${r("avatar.font.size")};
    color: ${r("avatar.color")};
    background: ${r("avatar.background")};
    border-radius: ${r("avatar.border.radius")};
}

.p-avatar-image {
    background: transparent;
}

.p-avatar-circle {
    border-radius: 50%;
}

.p-avatar-circle img {
    border-radius: 50%;
}

.p-avatar-icon {
    font-size: ${r("avatar.icon.size")};
    width: ${r("avatar.icon.size")};
    height: ${r("avatar.icon.size")};
}

.p-avatar img {
    width: 100%;
    height: 100%;
}

.p-avatar-lg {
    width: ${r("avatar.lg.width")};
    height: ${r("avatar.lg.width")};
    font-size: ${r("avatar.lg.font.size")};
}

.p-avatar-lg .p-avatar-icon {
    font-size: ${r("avatar.lg.icon.size")};
    width: ${r("avatar.lg.icon.size")};
    height: ${r("avatar.lg.icon.size")};
}

.p-avatar-xl {
    width: ${r("avatar.xl.width")};
    height: ${r("avatar.xl.width")};
    font-size: ${r("avatar.xl.font.size")};
}

.p-avatar-xl .p-avatar-icon {
    font-size: ${r("avatar.xl.font.size")};
    width: ${r("avatar.xl.icon.size")};
    height: ${r("avatar.xl.icon.size")};
}

.p-avatar-group {
    display: flex;
    align-items: center;
}

.p-avatar-group .p-avatar + .p-avatar {
    margin-inline-start: ${r("avatar.group.offset")};
}

.p-avatar-group .p-avatar {
    border: 2px solid ${r("avatar.group.border.color")};
}

.p-avatar-group .p-avatar-lg + .p-avatar-lg {
    margin-inline-start: ${r("avatar.lg.group.offset")};
}

.p-avatar-group .p-avatar-xl + .p-avatar-xl {
    margin-inline-start: ${r("avatar.xl.group.offset")};
}
`,Wn={root:({props:r})=>["p-avatar p-component",{"p-avatar-image":r.image!=null,"p-avatar-circle":r.shape==="circle","p-avatar-lg":r.size==="large","p-avatar-xl":r.size==="xlarge"}],label:"p-avatar-label",icon:"p-avatar-icon"},rn=(()=>{class r extends Ke{name="avatar";theme=Nn;classes=Wn;static \u0275fac=(()=>{let e;return function(t){return(e||(e=N(r)))(t||r)}})();static \u0275prov=w({token:r,factory:r.\u0275fac})}return r})();var Mo=(()=>{class r extends Qe{label;icon;image;size="normal";shape="square";style;styleClass;ariaLabel;ariaLabelledBy;onImageError=new G;_componentStyle=T(rn);imageError(e){this.onImageError.emit(e)}get hostClass(){return this.styleClass}static \u0275fac=(()=>{let e;return function(t){return(e||(e=N(r)))(t||r)}})();static \u0275cmp=ie({type:r,selectors:[["p-avatar"]],hostVars:19,hostBindings:function(o,t){o&2&&(E("data-pc-name","avatar")("aria-label",t.ariaLabel)("aria-labelledby",t.ariaLabelledBy),cr(t.style),D(t.hostClass),sr("p-avatar",!0)("p-component",!0)("p-avatar-circle",t.shape==="circle")("p-avatar-lg",t.size==="large")("p-avatar-xl",t.size==="xlarge")("p-avatar-image",t.image!=null))},inputs:{label:"label",icon:"icon",image:"image",size:"size",shape:"shape",style:"style",styleClass:"styleClass",ariaLabel:"ariaLabel",ariaLabelledBy:"ariaLabelledBy"},outputs:{onImageError:"onImageError"},features:[We([rn]),Ue],ngContentSelectors:Pn,decls:6,vars:2,consts:[["iconTemplate",""],["imageTemplate",""],["class","p-avatar-text",4,"ngIf","ngIfElse"],[1,"p-avatar-text"],[3,"class","ngClass",4,"ngIf","ngIfElse"],[3,"ngClass"],[3,"src","error",4,"ngIf"],[3,"error","src"]],template:function(o,t){if(o&1&&(lr(),dr(0),k(1,Mn,2,1,"span",2)(2,On,1,2,"ng-template",null,0,O)(4,jn,1,1,"ng-template",null,1,O)),o&2){let n=Z(3);h(),u("ngIf",t.label)("ngIfElse",n)}},dependencies:[de,we,_e,K],encapsulation:2,changeDetection:0})}return r})(),tn=(()=>{class r{static \u0275fac=function(o){return new(o||r)};static \u0275mod=ne({type:r});static \u0275inj=re({imports:[Mo,K,K]})}return r})();var Vn=["pMenuItemContent",""],an=r=>({"p-disabled":r}),ao=r=>({$implicit:r}),Kn=()=>({exact:!1});function Qn(r,i){r&1&&he(0)}function qn(r,i){if(r&1&&(C(0,"a",6),k(1,Qn,1,0,"ng-container",7),_()),r&2){let e=g(2),o=Z(4);u("target",e.item.target)("ngClass",V(9,an,e.item.disabled)),E("title",e.item.title)("href",e.item.url||null,Oe)("data-automationid",e.item.automationId)("tabindex",-1)("data-pc-section","action"),h(),u("ngTemplateOutlet",o)("ngTemplateOutletContext",V(11,ao,e.item))}}function Yn(r,i){r&1&&he(0)}function Xn(r,i){if(r&1&&(C(0,"a",8),k(1,Yn,1,0,"ng-container",7),_()),r&2){let e=g(2),o=Z(4);u("routerLink",e.item.routerLink)("queryParams",e.item.queryParams)("routerLinkActiveOptions",e.item.routerLinkActiveOptions||ur(17,Kn))("target",e.item.target)("ngClass",V(18,an,e.item.disabled))("fragment",e.item.fragment)("queryParamsHandling",e.item.queryParamsHandling)("preserveFragment",e.item.preserveFragment)("skipLocationChange",e.item.skipLocationChange)("replaceUrl",e.item.replaceUrl)("state",e.item.state),E("data-automationid",e.item.automationId)("tabindex",-1)("data-pc-section","action")("title",e.item.title),h(),u("ngTemplateOutlet",o)("ngTemplateOutletContext",V(20,ao,e.item))}}function Gn(r,i){if(r&1&&(je(0),k(1,qn,2,13,"a",4)(2,Xn,2,22,"a",5),Ne()),r&2){let e=g();h(),u("ngIf",!(e.item!=null&&e.item.routerLink)),h(),u("ngIf",e.item==null?null:e.item.routerLink)}}function Jn(r,i){}function Zn(r,i){r&1&&k(0,Jn,0,0,"ng-template")}function ea(r,i){if(r&1&&(je(0),k(1,Zn,1,0,null,7),Ne()),r&2){let e=g();h(),u("ngTemplateOutlet",e.itemTemplate)("ngTemplateOutletContext",V(2,ao,e.item))}}function oa(r,i){if(r&1&&I(0,"span",12),r&2){let e=g(2);D(e.item.iconClass),u("ngClass",e.item.icon)("ngStyle",e.item.iconStyle)}}function ra(r,i){if(r&1&&(C(0,"span",13),ce(1),_()),r&2){let e=g(2);h(),le(e.item.label)}}function ta(r,i){if(r&1&&(I(0,"span",14),po(1,"safeHtml")),r&2){let e=g(2);u("innerHTML",ho(1,1,e.item.label),fo)}}function ia(r,i){if(r&1&&I(0,"p-badge",15),r&2){let e=g(2);u("styleClass",e.item.badgeStyleClass)("value",e.item.badge)}}function na(r,i){if(r&1&&k(0,oa,1,4,"span",9)(1,ra,2,1,"span",10)(2,ta,2,3,"ng-template",null,1,O)(4,ia,1,2,"p-badge",11),r&2){let e=Z(3),o=g();u("ngIf",o.item.icon),h(),u("ngIf",o.item.escape!==!1)("ngIfElse",e),h(3),u("ngIf",o.item.badge)}}var aa=["start"],sa=["end"],ca=["header"],la=["item"],da=["submenuheader"],ua=["list"],fa=["container"],ga=r=>({"p-menu p-component":!0,"p-menu-overlay":r}),pa=(r,i)=>({showTransitionParams:r,hideTransitionParams:i}),ha=r=>({value:"visible",params:r}),ma=(r,i)=>({"p-hidden":r,flex:i}),sn=(r,i)=>({"p-focus":r,"p-disabled":i});function ba(r,i){r&1&&he(0)}function va(r,i){if(r&1&&(C(0,"div",9),k(1,ba,1,0,"ng-container",10),_()),r&2){let e,o=g(2);E("data-pc-section","start"),h(),u("ngTemplateOutlet",(e=o.startTemplate)!==null&&e!==void 0?e:o._startTemplate)}}function ka(r,i){r&1&&I(0,"li",14)}function ya(r,i){if(r&1&&(C(0,"span"),ce(1),_()),r&2){let e=g(3).$implicit;h(),le(e.label)}}function xa(r,i){if(r&1&&(I(0,"span",18),po(1,"safeHtml")),r&2){let e=g(3).$implicit;u("innerHTML",ho(1,1,e.label),fo)}}function Ca(r,i){if(r&1&&(je(0),k(1,ya,2,1,"span",17)(2,xa,2,3,"ng-template",null,2,O),Ne()),r&2){let e=Z(3),o=g(2).$implicit;h(),u("ngIf",o.escape!==!1)("ngIfElse",e)}}function wa(r,i){r&1&&he(0)}function _a(r,i){if(r&1&&(C(0,"li",15),k(1,Ca,4,2,"ng-container",7)(2,wa,1,0,"ng-container",16),_()),r&2){let e,o=g(),t=o.$implicit,n=o.index,a=g(3);u("ngClass",Ce(7,ma,t.visible===!1,t.visible))("tooltipOptions",t.tooltipOptions),E("data-automationid",t.automationId)("id",a.menuitemId(t,a.id,n)),h(),u("ngIf",!a.submenuHeaderTemplate&&!a._submenuHeaderTemplate),h(),u("ngTemplateOutlet",(e=a.submenuHeaderTemplate)!==null&&e!==void 0?e:a._submenuHeaderTemplate)("ngTemplateOutletContext",V(10,ao,t))}}function Sa(r,i){r&1&&I(0,"li",14)}function Ta(r,i){if(r&1){let e=ae();C(0,"li",20),J("onMenuItemClick",function(t){F(e);let n=g(),a=n.$implicit,s=n.index,l=g().index,d=g(3);return H(d.itemClick(t,d.menuitemId(a,d.id,l,s)))}),_()}if(r&2){let e,o=g(),t=o.$implicit,n=o.index,a=g().index,s=g(3);D(t.styleClass),u("pMenuItemContent",t)("itemTemplate",(e=s.itemTemplate)!==null&&e!==void 0?e:s._itemTemplate)("ngClass",Ce(13,sn,s.focusedOptionId()&&s.menuitemId(t,s.id,a,n)===s.focusedOptionId(),s.disabled(t.disabled)))("ngStyle",t.style)("tooltipOptions",t.tooltipOptions),E("data-pc-section","menuitem")("aria-label",s.label(t.label))("data-p-focused",s.isItemFocused(s.menuitemId(t,s.id,a,n)))("data-p-disabled",s.disabled(t.disabled))("aria-disabled",s.disabled(t.disabled))("id",s.menuitemId(t,s.id,a,n))}}function Ba(r,i){if(r&1&&k(0,Sa,1,0,"li",12)(1,Ta,1,16,"li",19),r&2){let e=i.$implicit,o=g().$implicit;u("ngIf",e.separator&&(e.visible!==!1||o.visible!==!1)),h(),u("ngIf",!e.separator&&e.visible!==!1&&(e.visible!==void 0||o.visible!==!1))}}function Ia(r,i){if(r&1&&k(0,ka,1,0,"li",12)(1,_a,3,12,"li",13)(2,Ba,2,2,"ng-template",11),r&2){let e=i.$implicit;u("ngIf",e.separator&&e.visible!==!1),h(),u("ngIf",!e.separator),h(),u("ngForOf",e.items)}}function Ra(r,i){if(r&1&&k(0,Ia,3,3,"ng-template",11),r&2){let e=g(2);u("ngForOf",e.model)}}function Aa(r,i){r&1&&I(0,"li",14)}function Ea(r,i){if(r&1){let e=ae();C(0,"li",20),J("onMenuItemClick",function(t){F(e);let n=g(),a=n.$implicit,s=n.index,l=g(3);return H(l.itemClick(t,l.menuitemId(a,l.id,s)))}),_()}if(r&2){let e,o=g(),t=o.$implicit,n=o.index,a=g(3);D(t.styleClass),u("pMenuItemContent",t)("itemTemplate",(e=a.itemTemplate)!==null&&e!==void 0?e:a._itemTemplate)("ngClass",Ce(13,sn,a.focusedOptionId()&&a.menuitemId(t,a.id,n)===a.focusedOptionId(),a.disabled(t.disabled)))("ngStyle",t.style)("tooltipOptions",t.tooltipOptions),E("data-pc-section","menuitem")("aria-label",a.label(t.label))("data-p-focused",a.isItemFocused(a.menuitemId(t,a.id,n)))("data-p-disabled",a.disabled(t.disabled))("aria-disabled",a.disabled(t.disabled))("id",a.menuitemId(t,a.id,n))}}function La(r,i){if(r&1&&k(0,Aa,1,0,"li",12)(1,Ea,1,16,"li",19),r&2){let e=i.$implicit;u("ngIf",e.separator&&e.visible!==!1),h(),u("ngIf",!e.separator&&e.visible!==!1)}}function Fa(r,i){if(r&1&&k(0,La,2,2,"ng-template",11),r&2){let e=g(2);u("ngForOf",e.model)}}function Ha(r,i){r&1&&he(0)}function za(r,i){if(r&1&&(C(0,"div",21),k(1,Ha,1,0,"ng-container",10),_()),r&2){let e,o=g(2);E("data-pc-section","end"),h(),u("ngTemplateOutlet",(e=o.endTemplate)!==null&&e!==void 0?e:o._endTemplate)}}function Pa(r,i){if(r&1){let e=ae();C(0,"div",4,0),J("click",function(t){F(e);let n=g();return H(n.onOverlayClick(t))})("@overlayAnimation.start",function(t){F(e);let n=g();return H(n.onOverlayAnimationStart(t))})("@overlayAnimation.done",function(t){F(e);let n=g();return H(n.onOverlayAnimationEnd(t))}),k(2,va,2,2,"div",5),C(3,"ul",6,1),J("focus",function(t){F(e);let n=g();return H(n.onListFocus(t))})("blur",function(t){F(e);let n=g();return H(n.onListBlur(t))})("keydown",function(t){F(e);let n=g();return H(n.onListKeyDown(t))}),k(5,Ra,1,1,null,7)(6,Fa,1,1,null,7),_(),k(7,za,2,2,"div",8),_()}if(r&2){let e,o,t=g();D(t.styleClass),u("ngClass",V(18,ga,t.popup))("ngStyle",t.style)("@overlayAnimation",V(23,ha,Ce(20,pa,t.showTransitionOptions,t.hideTransitionOptions)))("@.disabled",t.popup!==!0),E("data-pc-name","menu")("id",t.id),h(2),u("ngIf",(e=t.startTemplate)!==null&&e!==void 0?e:t._startTemplate),h(),E("id",t.id+"_list")("tabindex",t.getTabIndexValue())("data-pc-section","menu")("aria-activedescendant",t.activedescendant())("aria-label",t.ariaLabel)("aria-labelledBy",t.ariaLabelledBy),h(2),u("ngIf",t.hasSubMenu()),h(),u("ngIf",!t.hasSubMenu()),h(),u("ngIf",(o=t.endTemplate)!==null&&o!==void 0?o:t._endTemplate)}}var Ma=({dt:r})=>`
.p-menu {
    background: ${r("menu.background")};
    color: ${r("menu.color")};
    border: 1px solid ${r("menu.border.color")};
    border-radius: ${r("menu.border.radius")};
    min-width: 12.5rem;
}

.p-menu-list {
    margin: 0;
    padding: ${r("menu.list.padding")};
    outline: 0 none;
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: ${r("menu.list.gap")};
}

.p-menu-item-content {
    transition: background ${r("menu.transition.duration")}, color ${r("menu.transition.duration")};
    border-radius: ${r("menu.item.border.radius")};
    color: ${r("menu.item.color")};
}

.p-menu-item-link {
    cursor: pointer;
    display: flex;
    align-items: center;
    text-decoration: none;
    overflow: hidden;
    position: relative;
    color: inherit;
    padding: ${r("menu.item.padding")};
    gap: ${r("menu.item.gap")};
    user-select: none;
    outline: 0 none;
}

.p-menu-item-label {
    line-height: 1;
}

.p-menu-item-icon {
    color: ${r("menu.item.icon.color")};
}

.p-menu-item.p-focus .p-menu-item-content {
    color: ${r("menu.item.focus.color")};
    background: ${r("menu.item.focus.background")};
}

.p-menu-item.p-focus .p-menu-item-icon {
    color: ${r("menu.item.icon.focus.color")};
}

.p-menu-item:not(.p-disabled) .p-menu-item-content:hover {
    color: ${r("menu.item.focus.color")};
    background: ${r("menu.item.focus.background")};
}

.p-menu-item:not(.p-disabled) .p-menu-item-content:hover .p-menu-item-icon {
    color: ${r("menu.item.icon.focus.color")};
}

.p-menu-overlay {
    box-shadow: ${r("menu.shadow")};
}

.p-menu-submenu-label {
    background: ${r("menu.submenu.label.background")};
    padding: ${r("menu.submenu.label.padding")};
    color: ${r("menu.submenu.label.color")};
    font-weight: ${r("menu.submenu.label.font.weight")};
}

.p-menu-separator {
    border-top: 1px solid ${r("menu.separator.border.color")};
}

/* For PrimeNG */
.p-menu-overlay {
    position: absolute;
}
`,Da={root:({props:r})=>["p-menu p-component",{"p-menu-overlay":r.popup}],start:"p-menu-start",list:"p-menu-list",submenuLabel:"p-menu-submenu-label",separator:"p-menu-separator",end:"p-menu-end",item:({instance:r})=>["p-menu-item",{"p-focus":r.id===r.focusedOptionId,"p-disabled":r.disabled()}],itemContent:"p-menu-item-content",itemLink:"p-menu-item-link",itemIcon:"p-menu-item-icon",itemLabel:"p-menu-item-label"},nn=(()=>{class r extends Ke{name="menu";theme=Ma;classes=Da;static \u0275fac=(()=>{let e;return function(t){return(e||(e=N(r)))(t||r)}})();static \u0275prov=w({token:r,factory:r.\u0275fac})}return r})();var cn=(()=>{class r{platformId;sanitizer;constructor(e,o){this.platformId=e,this.sanitizer=o}transform(e){return!e||!Se(this.platformId)?e:this.sanitizer.bypassSecurityTrustHtml(e)}static \u0275fac=function(o){return new(o||r)(xe(er,16),xe(xr,16))};static \u0275pipe=nr({name:"safeHtml",type:r,pure:!0})}return r})(),Oa=(()=>{class r{item;itemTemplate;onMenuItemClick=new G;menu;constructor(e){this.menu=e}onItemClick(e,o){this.onMenuItemClick.emit({originalEvent:e,item:o})}static \u0275fac=function(o){return new(o||r)(xe(Jo(()=>so)))};static \u0275cmp=ie({type:r,selectors:[["","pMenuItemContent",""]],inputs:{item:[0,"pMenuItemContent","item"],itemTemplate:"itemTemplate"},outputs:{onMenuItemClick:"onMenuItemClick"},attrs:Vn,decls:5,vars:3,consts:[["itemContent",""],["htmlLabel",""],[1,"p-menu-item-content",3,"click"],[4,"ngIf"],["class","p-menu-item-link","pRipple","",3,"target","ngClass",4,"ngIf"],["routerLinkActive","p-menu-item-link-active","class","p-menu-item-link","pRipple","",3,"routerLink","queryParams","routerLinkActiveOptions","target","ngClass","fragment","queryParamsHandling","preserveFragment","skipLocationChange","replaceUrl","state",4,"ngIf"],["pRipple","",1,"p-menu-item-link",3,"target","ngClass"],[4,"ngTemplateOutlet","ngTemplateOutletContext"],["routerLinkActive","p-menu-item-link-active","pRipple","",1,"p-menu-item-link",3,"routerLink","queryParams","routerLinkActiveOptions","target","ngClass","fragment","queryParamsHandling","preserveFragment","skipLocationChange","replaceUrl","state"],["class","p-menu-item-icon",3,"ngClass","class","ngStyle",4,"ngIf"],["class","p-menu-item-label",4,"ngIf","ngIfElse"],[3,"styleClass","value",4,"ngIf"],[1,"p-menu-item-icon",3,"ngClass","ngStyle"],[1,"p-menu-item-label"],[1,"p-menu-item-label",3,"innerHTML"],[3,"styleClass","value"]],template:function(o,t){if(o&1){let n=ae();C(0,"div",2),J("click",function(s){return F(n),H(t.onItemClick(s,t.item))}),k(1,Gn,3,2,"ng-container",3)(2,ea,2,4,"ng-container",3)(3,na,5,4,"ng-template",null,0,O),_()}o&2&&(E("data-pc-section","content"),h(),u("ngIf",!t.itemTemplate),h(),u("ngIf",t.itemTemplate))},dependencies:[de,we,_e,ko,vo,yo,wr,_r,Mr,To,Co,Pr,K,cn],encapsulation:2})}return r})(),so=(()=>{class r extends Qe{overlayService;model;popup;style;styleClass;appendTo;autoZIndex=!0;baseZIndex=0;showTransitionOptions=".12s cubic-bezier(0, 0, 0.2, 1)";hideTransitionOptions=".1s linear";ariaLabel;ariaLabelledBy;id;tabindex=0;onShow=new G;onHide=new G;onBlur=new G;onFocus=new G;listViewChild;containerViewChild;container;scrollHandler;documentClickListener;documentResizeListener;preventDocumentDefault;target;visible;focusedOptionId=gr(()=>this.focusedOptionIndex()!==-1?this.focusedOptionIndex():null);focusedOptionIndex=te(-1);selectedOptionIndex=te(-1);focused=!1;overlayVisible=!1;relativeAlign;_componentStyle=T(nn);constructor(e){super(),this.overlayService=e,this.id=this.id||Ar("pn_id_")}toggle(e){this.visible?this.hide():this.show(e),this.preventDocumentDefault=!0}show(e){this.target=e.currentTarget,this.relativeAlign=e.relativeAlign,this.visible=!0,this.preventDocumentDefault=!0,this.overlayVisible=!0,this.cd.markForCheck()}ngOnInit(){super.ngOnInit(),this.popup||this.bindDocumentClickListener()}startTemplate;_startTemplate;endTemplate;_endTemplate;headerTemplate;_headerTemplate;itemTemplate;_itemTemplate;submenuHeaderTemplate;_submenuHeaderTemplate;templates;ngAfterContentInit(){this.templates?.forEach(e=>{switch(e.getType()){case"start":this._startTemplate=e.template;break;case"end":this._endTemplate=e.template;break;case"item":this._itemTemplate=e.template;break;case"submenuheader":this._submenuHeaderTemplate=e.template;break;default:this._itemTemplate=e.template;break}})}getTabIndexValue(){return this.tabindex!==void 0?this.tabindex.toString():null}onOverlayAnimationStart(e){switch(e.toState){case"visible":this.popup&&(this.container=e.element,this.moveOnTop(),this.onShow.emit({}),this.appendOverlay(),this.alignOverlay(),this.bindDocumentClickListener(),this.bindDocumentResizeListener(),this.bindScrollListener(),Be(this.listViewChild.nativeElement));break;case"void":this.onOverlayHide(),this.onHide.emit({});break}}onOverlayAnimationEnd(e){switch(e.toState){case"void":this.autoZIndex&&qe.clear(e.element);break}}alignOverlay(){this.relativeAlign?Br(this.container,this.target):Tr(this.container,this.target)}appendOverlay(){this.appendTo&&(this.appendTo==="body"?this.renderer.appendChild(this.document.body,this.container):Ir(this.appendTo,this.container))}restoreOverlayAppend(){this.container&&this.appendTo&&this.renderer.appendChild(this.el.nativeElement,this.container)}moveOnTop(){this.autoZIndex&&qe.set("menu",this.container,this.baseZIndex+this.config.zIndex.menu)}hide(){this.visible=!1,this.relativeAlign=!1,this.cd.markForCheck()}onWindowResize(){this.visible&&!Rr()&&this.hide()}menuitemId(e,o,t,n){return e?.id??`${o}_${t}${n!==void 0?"_"+n:""}`}isItemFocused(e){return this.focusedOptionId()===e}label(e){return typeof e=="function"?e():e}disabled(e){return typeof e=="function"?e():typeof e>"u"?!1:e}activedescendant(){return this.focused?this.focusedOptionId():void 0}onListFocus(e){this.focused||(this.focused=!0,this.onFocus.emit(e))}onListBlur(e){this.focused&&(this.focused=!1,this.changeFocusedOptionIndex(-1),this.selectedOptionIndex.set(-1),this.focusedOptionIndex.set(-1),this.onBlur.emit(e))}onListKeyDown(e){switch(e.code){case"ArrowDown":this.onArrowDownKey(e);break;case"ArrowUp":this.onArrowUpKey(e);break;case"Home":this.onHomeKey(e);break;case"End":this.onEndKey(e);break;case"Enter":this.onEnterKey(e);break;case"NumpadEnter":this.onEnterKey(e);break;case"Space":this.onSpaceKey(e);break;case"Escape":case"Tab":this.popup&&(Be(this.target),this.hide()),this.overlayVisible&&this.hide();break;default:break}}onArrowDownKey(e){let o=this.findNextOptionIndex(this.focusedOptionIndex());this.changeFocusedOptionIndex(o),e.preventDefault()}onArrowUpKey(e){if(e.altKey&&this.popup)Be(this.target),this.hide(),e.preventDefault();else{let o=this.findPrevOptionIndex(this.focusedOptionIndex());this.changeFocusedOptionIndex(o),e.preventDefault()}}onHomeKey(e){this.changeFocusedOptionIndex(0),e.preventDefault()}onEndKey(e){this.changeFocusedOptionIndex(Te(this.containerViewChild.nativeElement,'li[data-pc-section="menuitem"][data-p-disabled="false"]').length-1),e.preventDefault()}onEnterKey(e){let o=xo(this.containerViewChild.nativeElement,`li[id="${`${this.focusedOptionIndex()}`}"]`),t=o&&xo(o,'a[data-pc-section="action"]');this.popup&&Be(this.target),t?t.click():o&&o.click(),e.preventDefault()}onSpaceKey(e){this.onEnterKey(e)}findNextOptionIndex(e){let t=[...Te(this.containerViewChild.nativeElement,'li[data-pc-section="menuitem"][data-p-disabled="false"]')].findIndex(n=>n.id===e);return t>-1?t+1:0}findPrevOptionIndex(e){let t=[...Te(this.containerViewChild.nativeElement,'li[data-pc-section="menuitem"][data-p-disabled="false"]')].findIndex(n=>n.id===e);return t>-1?t-1:0}changeFocusedOptionIndex(e){let o=Te(this.containerViewChild.nativeElement,'li[data-pc-section="menuitem"][data-p-disabled="false"]');if(o.length>0){let t=e>=o.length?o.length-1:e<0?0:e;t>-1&&this.focusedOptionIndex.set(o[t].getAttribute("id"))}}itemClick(e,o){let{originalEvent:t,item:n}=e;if(this.focused||(this.focused=!0,this.onFocus.emit()),n.disabled){t.preventDefault();return}!n.url&&!n.routerLink&&t.preventDefault(),n.command&&n.command({originalEvent:t,item:n}),this.popup&&this.hide(),!this.popup&&this.focusedOptionIndex()!==o&&this.focusedOptionIndex.set(o)}onOverlayClick(e){this.popup&&this.overlayService.add({originalEvent:e,target:this.el.nativeElement}),this.preventDocumentDefault=!0}bindDocumentClickListener(){if(!this.documentClickListener&&Se(this.platformId)){let e=this.el?this.el.nativeElement.ownerDocument:"document";this.documentClickListener=this.renderer.listen(e,"click",o=>{let t=this.containerViewChild?.nativeElement&&!this.containerViewChild?.nativeElement.contains(o.target),n=!(this.target&&(this.target===o.target||this.target.contains(o.target)));!this.popup&&t&&n&&this.onListBlur(o),this.preventDocumentDefault&&this.overlayVisible&&t&&n&&(this.hide(),this.preventDocumentDefault=!1)})}}unbindDocumentClickListener(){this.documentClickListener&&(this.documentClickListener(),this.documentClickListener=null)}bindDocumentResizeListener(){if(!this.documentResizeListener&&Se(this.platformId)){let e=this.document.defaultView;this.documentResizeListener=this.renderer.listen(e,"resize",this.onWindowResize.bind(this))}}unbindDocumentResizeListener(){this.documentResizeListener&&(this.documentResizeListener(),this.documentResizeListener=null)}bindScrollListener(){!this.scrollHandler&&Se(this.platformId)&&(this.scrollHandler=new zr(this.target,()=>{this.visible&&this.hide()})),this.scrollHandler?.bindScrollListener()}unbindScrollListener(){this.scrollHandler&&(this.scrollHandler.unbindScrollListener(),this.scrollHandler=null)}onOverlayHide(){this.unbindDocumentClickListener(),this.unbindDocumentResizeListener(),this.unbindScrollListener(),this.preventDocumentDefault=!1,this.cd.destroyed||(this.target=null)}ngOnDestroy(){this.popup&&(this.scrollHandler&&(this.scrollHandler.destroy(),this.scrollHandler=null),this.container&&this.autoZIndex&&qe.clear(this.container),this.restoreOverlayAppend(),this.onOverlayHide()),this.popup||this.unbindDocumentClickListener(),super.ngOnDestroy()}hasSubMenu(){return this.model?.some(e=>e.items)??!1}isItemHidden(e){return e.separator?e.visible===!1||e.items&&e.items.some(o=>o.visible!==!1):e.visible===!1}static \u0275fac=function(o){return new(o||r)(xe(Er))};static \u0275cmp=ie({type:r,selectors:[["p-menu"]],contentQueries:function(o,t,n){if(o&1&&(se(n,aa,4),se(n,sa,4),se(n,ca,4),se(n,la,4),se(n,da,4),se(n,Lr,4)),o&2){let a;W(a=$())&&(t.startTemplate=a.first),W(a=$())&&(t.endTemplate=a.first),W(a=$())&&(t.headerTemplate=a.first),W(a=$())&&(t.itemTemplate=a.first),W(a=$())&&(t.submenuHeaderTemplate=a.first),W(a=$())&&(t.templates=a)}},viewQuery:function(o,t){if(o&1&&(go(ua,5),go(fa,5)),o&2){let n;W(n=$())&&(t.listViewChild=n.first),W(n=$())&&(t.containerViewChild=n.first)}},inputs:{model:"model",popup:[2,"popup","popup",mo],style:"style",styleClass:"styleClass",appendTo:"appendTo",autoZIndex:[2,"autoZIndex","autoZIndex",mo],baseZIndex:[2,"baseZIndex","baseZIndex",bo],showTransitionOptions:"showTransitionOptions",hideTransitionOptions:"hideTransitionOptions",ariaLabel:"ariaLabel",ariaLabelledBy:"ariaLabelledBy",id:"id",tabindex:[2,"tabindex","tabindex",bo]},outputs:{onShow:"onShow",onHide:"onHide",onBlur:"onBlur",onFocus:"onFocus"},features:[We([nn]),Ue],decls:1,vars:1,consts:[["container",""],["list",""],["htmlSubmenuLabel",""],[3,"ngClass","class","ngStyle","click",4,"ngIf"],[3,"click","ngClass","ngStyle"],["class","p-menu-start",4,"ngIf"],["role","menu",1,"p-menu-list","p-reset",3,"focus","blur","keydown"],[4,"ngIf"],["class","p-menu-end",4,"ngIf"],[1,"p-menu-start"],[4,"ngTemplateOutlet"],["ngFor","",3,"ngForOf"],["class","p-menu-separator","role","separator",4,"ngIf"],["class","p-menu-submenu-label","pTooltip","","role","none",3,"ngClass","tooltipOptions",4,"ngIf"],["role","separator",1,"p-menu-separator"],["pTooltip","","role","none",1,"p-menu-submenu-label",3,"ngClass","tooltipOptions"],[4,"ngTemplateOutlet","ngTemplateOutletContext"],[4,"ngIf","ngIfElse"],[3,"innerHTML"],["class","p-menu-item","pTooltip","","role","menuitem",3,"pMenuItemContent","itemTemplate","ngClass","ngStyle","class","tooltipOptions","onMenuItemClick",4,"ngIf"],["pTooltip","","role","menuitem",1,"p-menu-item",3,"onMenuItemClick","pMenuItemContent","itemTemplate","ngClass","ngStyle","tooltipOptions"],[1,"p-menu-end"]],template:function(o,t){o&1&&k(0,Pa,8,25,"div",3),o&2&&u("ngIf",!t.popup||t.visible)},dependencies:[de,we,pr,_e,ko,vo,yo,Oa,To,Or,Co,K,cn],encapsulation:2,data:{animation:[Dr("overlayAnimation",[So(":enter",[_o({opacity:0,transform:"scaleY(0.8)"}),wo("{{showTransitionParams}}")]),So(":leave",[wo("{{hideTransitionParams}}",_o({opacity:0}))])])]},changeDetection:0})}return r})(),ln=(()=>{class r{static \u0275fac=function(o){return new(o||r)};static \u0275mod=ne({type:r});static \u0275inj=re({imports:[so,K,K]})}return r})();function ja(r,i){r&1&&(C(0,"span",10),I(1,"img",11),_())}function Na(r,i){if(r&1&&(C(0,"a",12),I(1,"span"),C(2,"span",13),ce(3),_()()),r&2){let e=i.$implicit;h(),D(e.icon),h(2),le(e.label)}}var co=class r{constructor(){this.title="admin";this.router=T(ue);this.items=[{label:"BucketFile",icon:"pi pi-file",command:()=>this.router.navigate(["/bucket-file"])},{label:"Folder & File",icon:"pi pi-folder",command:()=>this.router.navigate(["/folder"])},{label:"Article",icon:"pi pi-file-pdf"},{label:"Ebook",icon:"pi pi-book",command:()=>this.router.navigate(["/ebook"])},{label:"Chapter",icon:"pi pi-align-left",command:()=>this.router.navigate(["/chapter"])},{label:"Album",icon:"pi pi-tiktok",command:()=>this.router.navigate(["/album"])},{label:"Collection",icon:"pi pi-database",command:()=>this.router.navigate(["/collection"])},{label:"Channel",icon:"pi pi-objects-column",command:()=>this.router.navigate(["/channel"])}]}static{this.\u0275fac=function(e){return new(e||r)}}static{this.\u0275cmp=ie({type:r,selectors:[["app-root"]],decls:13,vars:1,consts:[["start",""],["item",""],[1,"flex","p-6","h-screen"],["styleClass","w-full md:w-60",1,"flex","justify-center",3,"model"],[1,"flex-1","ml-3","flex","flex-col"],[1,"flex","justify-end","items-center","gap-2","border-b","pb-3"],[1,"pi","pi-bell"],[1,"pi","pi-language"],["image","https://primefaces.org/cdn/primeng/images/demo/avatar/amyelsner.png","shape","circle",1,"mr-2"],[1,"flex-1","overflow-auto"],[1,"inline-flex","items-center","gap-1","px-2","py-2"],["src","logo.svg","alt","",1,"w-8"],["pRipple","",1,"flex","items-center","p-menu-item-link"],[1,"ml-2"]],template:function(e,o){e&1&&(C(0,"div",2)(1,"p-menu",3),k(2,ja,2,0,"ng-template",null,0,O)(4,Na,4,3,"ng-template",null,1,O),_(),C(6,"div",4)(7,"div",5),I(8,"i",6)(9,"i",7)(10,"p-avatar",8),_(),C(11,"div",9),I(12,"router-outlet"),_()()()),e&2&&(h(),u("model",o.items))},dependencies:[Cr,ln,so,tn,Mo],encapsulation:2})}};mr(co,on).catch(r=>console.error(r));
