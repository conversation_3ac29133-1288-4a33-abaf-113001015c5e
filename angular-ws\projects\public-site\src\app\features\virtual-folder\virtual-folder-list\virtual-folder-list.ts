import { ReadOnlyChannelService } from '@/proxy/holy-bless/channels';
import {
  ReadOnlyVirtualFolderService,
} from '@/proxy/holy-bless/virtual-folders';
import { VirtualFolderDto } from '@/proxy/holy-bless/virtual-folders/dtos';
import { ChannelIdContentCodeService } from '@/services/channelIdContentCode.service';
import { I18nService } from '@/services/i18n.service';
import { LoadingService } from '@/services/loading.service';
import { MobileService } from '@/services/mobile.service';
import { CommonModule } from '@angular/common';
import { Component, inject, signal } from '@angular/core';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { CardModule } from 'primeng/card';
import { PaginatorModule } from 'primeng/paginator';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-virtual-folder-list',
  standalone: true,
  imports: [CommonModule, RouterModule, CardModule, PaginatorModule],
  templateUrl: './virtual-folder-list.html',
  styleUrls: ['./virtual-folder-list.scss'],
})
export class VirtualFolderListComponent {
  #ReadOnlyVirtualFolderService = inject(ReadOnlyVirtualFolderService);
  #ReadOnlyChannelService = inject(ReadOnlyChannelService);
  mobileService = inject(MobileService);
  router = inject(Router);
  route = inject(ActivatedRoute);
  i18nService = inject(I18nService);
  channelIdContentCodeService = inject(ChannelIdContentCodeService);
  subs = new Subscription();
  loadingService = inject(LoadingService);

  first = signal(0);
  rows = signal(10);
  totalRecords = signal(0);

  items = signal<VirtualFolderDto[]>([]);
  channelId: number | undefined = undefined;

  ngOnInit() {
    this.route.params.subscribe((params) => {
      const channelId = params['channelId'];
      this.channelId = channelId;
      if (channelId) {
        this.changeLanguage(channelId);
        this.loadVirtualFolders();
      }
    });
  }

  changeLanguage(channelId: number) {
    this.subs.unsubscribe();
    this.subs = new Subscription();
    const sub = this.i18nService.language$.subscribe(() => {
      const contentCode =
        this.channelIdContentCodeService.getChannelIdContentCode(channelId);
      if (contentCode) {
        this.#ReadOnlyChannelService.getMatchedChannelByContentCode(contentCode).subscribe({
          next: (channel) => {
            if (channel.id !== channelId) {
              this.router.navigateByUrl(`/virtual-folder/list/${channel.id}`);
            }
          },
        });
      }
    });
    this.subs.add(sub);
  }

  loadVirtualFolders() {
    if (!this.channelId) return;
    this.loadingService.show();
    this.#ReadOnlyVirtualFolderService
      .getList({
        channelId: this.channelId!,
        skipCount: this.first(),
        maxResultCount: this.rows(),
      })
      .subscribe({
        next: (res) => {
          this.items.set(res.items || []);
          this.totalRecords.set(res.totalCount || 0);
          this.loadingService.hide();
        },
        error: (error) => {
          console.error('获取虚拟文件夹列表失败:', error);
          this.loadingService.hide();
        },
      });
  }

  onPageChange(event: any) {
    this.first.set(event.first);
    this.rows.set(event.rows);
    this.loadVirtualFolders();
  }

  navigateToFolderDetail(folderId: number | undefined) {
    if (!folderId) return;
    this.router.navigateByUrl(`/virtual-folder/folder-detail/${folderId}`);
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
