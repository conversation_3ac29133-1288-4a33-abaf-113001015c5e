{"App": {"SelfUrl": "https://admin.holyblesspan.com/", "AngularUrl": "https://holybless-admin.pages.dev", "CorsOrigins": "https://holybless-admin.pages.dev/,https://*.holybless-admin.pages.dev/,https://*.HolyBless.com,http://localhost:4200", "RedirectAllowedUrls": "http://localhost:4200,https://holybless-admin.pages.dev/,https://*.holybless-admin.pages.dev/", "DisablePII": false, "HealthCheckUrl": "/health-status"}, "ConnectionStrings": {"Default": "Host=localhost;Port=5432;Database=holybless;User ID=postgres;Password=************************;Ssl Mode=Disable;Trust Server Certificate=true;"}, "AuthServer": {"Authority": "https://admin.holyblesspan.com", "RequireHttpsMetadata": true, "SwaggerClientId": "<PERSON><PERSON><PERSON>_Swagger_Admin", "CertificatePassPhrase": "fne9h5gs"}, "StringEncryption": {"DefaultPassPhrase": "YGVQoCMR9EJKx7Gm"}, "AppConfigs": {"Environment": "<PERSON>", "ExposeWritableApi": true, "EnableBackgroundWorkers": false}}