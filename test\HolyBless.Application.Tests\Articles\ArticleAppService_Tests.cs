using HolyBless.Articles.Dtos;
using HolyBless.Entities.Articles;
using HolyBless.Tags;
using HolyBless.Tags.Dtos;
using Shouldly;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;
using Volo.Abp.Validation;
using Xunit;

namespace HolyBless.Articles
{
    public abstract class ArticleAppService_Tests<TStartupModule> : HolyBlessTestBase<TStartupModule>
        where TStartupModule : IAbpModule
    {
        private readonly IArticleAppService _articleAppService;
        private readonly ITagAppService _tagAppService;
        private readonly IRepository<Article, int> _articleRepository;

        protected ArticleAppService_Tests()
        {
            _articleAppService = GetRequiredService<IArticleAppService>();
            _tagAppService = GetRequiredService<ITagAppService>();
            // _contentCodeRepository removed
            _articleRepository = GetRequiredService<IRepository<Article, int>>();
        }

        [Fact]
        public async Task Should_Get_List_Of_Articles()
        {
            var result = await _articleAppService.GetListAsync(
                new ArticleAdminSearchDto()
            );

            result.TotalCount.ShouldBeGreaterThan(0);
            result.Items.ShouldContain(x => x.Title != null);
        }

        [Fact]
        public async Task Should_Sort_Articles_By_Title()
        {
            // Act
            var ascResult = await _articleAppService.GetListAsync(
                new ArticleAdminSearchDto
                {
                    Sorting = "Title",
                    MaxResultCount = 10
                }
            );

            var descResult = await _articleAppService.GetListAsync(
                new ArticleAdminSearchDto
                {
                    Sorting = "Title DESC",
                    MaxResultCount = 10
                }
            );

            // Assert
            ascResult.TotalCount.ShouldBeGreaterThan(0);
            descResult.TotalCount.ShouldBeGreaterThan(0);

            // Check ascending order
            for (int i = 0; i < ascResult.Items.Count - 1; i++)
            {
                string.Compare(ascResult.Items[i].Title, ascResult.Items[i + 1].Title).ShouldBeLessThanOrEqualTo(0);
            }

            // Check descending order
            for (int i = 0; i < descResult.Items.Count - 1; i++)
            {
                string.Compare(descResult.Items[i].Title, descResult.Items[i + 1].Title).ShouldBeGreaterThanOrEqualTo(0);
            }
        }

        [Fact]
        public async Task Should_Create_A_Valid_Article()
        {
            var result = await _articleAppService.CreateAsync(
                new CreateUpdateArticleDto
                {
                    Title = "Test Article",
                    LanguageCode = "zh-CN"
                }
            );

            result.Id.ShouldBeGreaterThan(0);
            result.Title.ShouldBe("Test Article");
        }

        [Fact]
        public async Task Should_Not_Create_Article_Without_Title()
        {
            // Act & Assert
            var exception = await Assert.ThrowsAsync<AbpValidationException>(async () =>
            {
                await _articleAppService.CreateAsync(
                    new CreateUpdateArticleDto
                    {
                        Title = "",  // Empty title should fail validation
                        LanguageCode = "en"
                    }
                );
            });

            exception.ValidationErrors.ShouldContain(e => e.MemberNames.Contains("Title"));
        }

        [Fact]
        public async Task Should_Update_Existing_Article()
        {
            // Arrange
            var article = await _articleAppService.CreateAsync(
                new CreateUpdateArticleDto
                {
                    Title = "Original Title",
                    LanguageCode = "en",
                    Description = "Original description"
                }
            );

            // Act
            var updatedArticle = await _articleAppService.UpdateAsync(
                article.Id,
                new CreateUpdateArticleDto
                {
                    Title = "Updated Title",
                    LanguageCode = "en",
                    Description = "Updated description"
                }
            );

            // Assert
            updatedArticle.Id.ShouldBe(article.Id);
            updatedArticle.Title.ShouldBe("Updated Title");
            updatedArticle.Description.ShouldBe("Updated description");
        }

        [Fact]
        public async Task Should_Delete_Article()
        {
            // Arrange
            var article = await _articleAppService.CreateAsync(
                new CreateUpdateArticleDto
                {
                    Title = "Article to Delete",
                    LanguageCode = "en"
                }
            );

            // Act
            await _articleAppService.DeleteAsync(article.Id);

            // Assert
            await Assert.ThrowsAsync<EntityNotFoundException>(async () =>
            {
                await _articleRepository.GetAsync(article.Id);
            });
        }

        [Fact]
        public async Task Should_Get_Article_By_Id()
        {
            // Arrange
            var newArticle = await _articleAppService.CreateAsync(
                new CreateUpdateArticleDto
                {
                    Title = "Article to Retrieve",
                    LanguageCode = "en",
                    Description = "Test description",
                    Keywords = "test, retrieve, abp"
                }
            );

            // Act
            var retrievedArticle = await _articleAppService.GetAsync(newArticle.Id);

            // Assert
            retrievedArticle.ShouldNotBeNull();
            retrievedArticle.Id.ShouldBe(newArticle.Id);
            retrievedArticle.Title.ShouldBe("Article to Retrieve");
            retrievedArticle.Description.ShouldBe("Test description");
            retrievedArticle.Keywords.ShouldBe("test, retrieve, abp");
        }

        [Fact]
        public async Task Should_Not_Get_Non_Existing_Article()
        {
            // Act & Assert
            await Assert.ThrowsAsync<EntityNotFoundException>(async () =>
            {
                await _articleAppService.GetAsync(99999);
            });
        }

        [Fact]
        public async Task Should_Add_Tags_To_Article()
        {
            // Arrange - Create an article
            var article = await _articleAppService.CreateAsync(
                new CreateUpdateArticleDto
                {
                    Title = "Article for Tags",
                    LanguageCode = "en"
                }
            );

            // Create some tags
            var tag1 = await _tagAppService.CreateAsync(
                new CreateUpdateTagDto
                {
                    TagName = "Test Tag 1",
                    LanguageCode = "en"
                }
            );

            var tag2 = await _tagAppService.CreateAsync(
                new CreateUpdateTagDto
                {
                    TagName = "Test Tag 2",
                    LanguageCode = "en"
                }
            );

            // Act - Add tags to the article
            var tagIds = new List<int> { tag1.Id, tag2.Id };
            var result = await _articleAppService.AddTagsAsync(article.Id, tagIds);

            // Assert
            result.Count.ShouldBe(2);
            result.ShouldContain(at => at.TagId == tag1.Id && at.ArticleId == article.Id);
            result.ShouldContain(at => at.TagId == tag2.Id && at.ArticleId == article.Id);
        }

        [Fact]
        public async Task Should_Not_Add_Duplicate_Tags()
        {
            // Arrange - Create an article
            var article = await _articleAppService.CreateAsync(
                new CreateUpdateArticleDto
                {
                    Title = "Article for Duplicate Tags",
                    LanguageCode = "en"
                }
            );

            // Create a tag
            var tag = await _tagAppService.CreateAsync(
                new CreateUpdateTagDto
                {
                    TagName = "Duplicate Tag",
                    LanguageCode = "en"
                }
            );

            // Add tag once
            await _articleAppService.AddTagsAsync(article.Id, new List<int> { tag.Id });

            // Act - Try to add the same tag again
            var result = await _articleAppService.AddTagsAsync(article.Id, new List<int> { tag.Id });

            // Assert - No tags should be added the second time
            result.Count.ShouldBe(0);

            // Verify only one association exists
            var tags = await _articleAppService.GetTagsAsync(article.Id);
            tags.Count.ShouldBe(1);
        }

        [Fact]
        public async Task Should_Get_Tags_Of_Article()
        {
            // Arrange - Create an article
            var article = await _articleAppService.CreateAsync(
                new CreateUpdateArticleDto
                {
                    Title = "Article for Get Tags",
                    LanguageCode = "en"
                }
            );

            // Create tag
            var tag = await _tagAppService.CreateAsync(
                new CreateUpdateTagDto
                {
                    TagName = "Get Test Tag",
                    LanguageCode = "en"
                }
            );

            // Add tag to article
            await _articleAppService.AddTagsAsync(article.Id, new List<int> { tag.Id });

            // Act - Get tags of the article
            var tags = await _articleAppService.GetTagsAsync(article.Id);

            // Assert
            tags.Count.ShouldBe(1);
            tags[0].Id.ShouldBe(tag.Id);
            tags[0].TagName.ShouldBe("Get Test Tag");
        }

        [Fact]
        public async Task Should_Return_Empty_List_For_Article_Without_Tags()
        {
            // Arrange - Create an article
            var article = await _articleAppService.CreateAsync(
                new CreateUpdateArticleDto
                {
                    Title = "Article without Tags",
                    LanguageCode = "en"
                }
            );

            // Act - Get tags of the article
            var tags = await _articleAppService.GetTagsAsync(article.Id);

            // Assert
            tags.Count.ShouldBe(0);
        }

        [Fact]
        public async Task Should_Throw_Exception_When_Article_Not_Found()
        {
            // Act & Assert
            await Assert.ThrowsAsync<EntityNotFoundException>(async () =>
            {
                await _articleAppService.GetTagsAsync(999999);
            });

            await Assert.ThrowsAsync<EntityNotFoundException>(async () =>
            {
                await _articleAppService.AddTagsAsync(999999, new List<int> { 1 });
            });
        }
    }
}