import type { VirtualFolderDto, VirtualFolderFileDto, VirtualFolderFileSearchDto, VirtualFolderTreeDto } from './dtos/models';
import type { CreateUpdateVirtualFolderDto, VirtualFolderSearchDto } from './models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class VirtualFolderService {
  apiName = 'Default';
  

  create = (input: CreateUpdateVirtualFolderDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, VirtualFolderDto>({
      method: 'POST',
      url: '/api/app/virtual-folder',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/virtual-folder/${id}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, VirtualFolderDto>({
      method: 'GET',
      url: `/api/app/virtual-folder/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getAllFolderFileUrlsByFolderId = (folderId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string[]>({
      method: 'GET',
      url: `/api/app/virtual-folder/folder-file-urls/${folderId}`,
    },
    { apiName: this.apiName,...config });
  

  getAllFolderFilesByFolderId = (folderId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, VirtualFolderFileDto[]>({
      method: 'GET',
      url: `/api/app/virtual-folder/folder-files/${folderId}`,
    },
    { apiName: this.apiName,...config });
  

  getAllVirtualFolders = (languageCode?: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, VirtualFolderDto[]>({
      method: 'GET',
      url: '/api/app/virtual-folder/virtual-folders',
      params: { languageCode },
    },
    { apiName: this.apiName,...config });
  

  getFolderFiles = (input: VirtualFolderFileSearchDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<VirtualFolderFileDto>>({
      method: 'GET',
      url: '/api/app/virtual-folder/folder-files',
      params: { folderId: input.folderId, sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getFolderTreeJsonByRootFolderId = (rootFolderId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string>({
      method: 'GET',
      responseType: 'text',
      url: `/api/app/virtual-folder/folder-tree-json/${rootFolderId}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: VirtualFolderSearchDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<VirtualFolderDto>>({
      method: 'GET',
      url: '/api/app/virtual-folder',
      params: { channelId: input.channelId, sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getVirtualFolderTree = (folderId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, VirtualFolderTreeDto[]>({
      method: 'GET',
      url: `/api/app/virtual-folder/virtual-folder-tree/${folderId}`,
    },
    { apiName: this.apiName,...config });
  

  linkToChannelByChannelIdAndVirtualFolderId = (channelId: number, virtualFolderId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/virtual-folder/link-to-channel',
      params: { channelId, virtualFolderId },
    },
    { apiName: this.apiName,...config });
  

  linkToChannelByChannelIdAndVirtualFolderIds = (channelId: number, virtualFolderIds: number[], config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: `/api/app/virtual-folder/link-to-channel/${channelId}`,
      body: virtualFolderIds,
    },
    { apiName: this.apiName,...config });
  

  moveVirtualFolder = (folderId: number, toParentId: number, beforeId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/virtual-folder/move-virtual-folder',
      params: { folderId, toParentId, beforeId },
    },
    { apiName: this.apiName,...config });
  

  unlinkFromChannelByVirtualFolderId = (virtualFolderId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: `/api/app/virtual-folder/unlink-from-channel/${virtualFolderId}`,
    },
    { apiName: this.apiName,...config });
  

  unlinkFromChannelByVirtualFolderIds = (virtualFolderIds: number[], config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/virtual-folder/unlink-from-channel',
      body: virtualFolderIds,
    },
    { apiName: this.apiName,...config });
  

  update = (id: number, input: CreateUpdateVirtualFolderDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, VirtualFolderDto>({
      method: 'PUT',
      url: `/api/app/virtual-folder/${id}`,
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
