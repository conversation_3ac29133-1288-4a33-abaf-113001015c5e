using System.Collections.Generic;
using HolyBless.TreeJsonSnapshots;

namespace HolyBless.VirtualFolders.Dtos
{
    /// <summary>
    /// DTO for virtual folder tree structure (without files, just folder hierarchy)
    /// Similar to CollectionTreeDto
    /// </summary>
    public class VirtualFolderTreeDto : ITreeDto<VirtualFolderTreeDto>
    {
        public int Id { get; set; }
        public string FolderName { get; set; } = string.Empty;
        public int? ParentFolderId { get; set; }
        public int Views { get; set; } = 0;
        public int Weight { get; set; } = 0;
        public int? ChannelId { get; set; }
        public bool IsRoot { get; set; } = false; // Indicates if this is a root folder
        public List<VirtualFolderTreeDto> Children { get; set; } = new List<VirtualFolderTreeDto>();
    }
}
