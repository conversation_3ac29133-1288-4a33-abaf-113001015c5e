import{a as se}from"./chunk-DRIYSUPZ.js";import{a as oe,b as re}from"./chunk-4ZKPBQ3G.js";import{b as ae}from"./chunk-IWTIDBZ7.js";import{a as W}from"./chunk-33OP5RZM.js";import{a as de,b as pe}from"./chunk-G4FA4R76.js";import{a as le}from"./chunk-SPDIY6EA.js";import{a as ie,b as ne}from"./chunk-E6VTCYRR.js";import{a as Z,b as $,c as I}from"./chunk-W4NOLTEH.js";import{A as X,B as Y,C as ee,D as te,p as H,q as O,r as q,s as G,t as J,u as K,y as Q,z as U}from"./chunk-NSHV5XN7.js";import{C as A,E as z,F as j,H as R}from"./chunk-GXYB24H2.js";import{$a as s,$b as v,Bb as B,Ca as c,Ec as P,Fb as o,Gb as r,Hb as M,Lb as y,Qb as w,Rb as C,Zb as b,a as x,b as L,bc as h,cc as u,dc as g,fc as F,ga as S,jb as N,na as d,oa as p,pb as D,rc as T,wb as m,zb as V}from"./chunk-AKB6GSCA.js";var ce=()=>({width:"50vw"}),he=()=>({width:"50vw",height:"90vh"});function ue(_,l){if(_&1){let t=y();o(0,"div",7)(1,"div",25)(2,"label"),b(3,"Name"),r(),o(4,"input",26),g("ngModelChange",function(i){d(t);let a=C();return u(a.selectData().folderName,i)||(a.selectData().folderName=i),p(i)}),r()(),o(5,"div",25)(6,"label"),b(7,"Channel"),r(),o(8,"p-treeselect",27),g("ngModelChange",function(i){d(t);let a=C();return u(a.selectData().channel,i)||(a.selectData().channel=i),p(i)}),r()(),o(9,"div",25)(10,"label"),b(11,"Spoken language"),r(),o(12,"p-select",28),g("ngModelChange",function(i){d(t);let a=C();return u(a.selectData().spokenLangCode,i)||(a.selectData().spokenLangCode=i),p(i)}),r()()()}if(_&2){let t=C();s(4),h("ngModel",t.selectData().folderName),s(4),h("ngModel",t.selectData().channel),m("options",t.channelTree()),s(4),m("options",t.spokenLangList),h("ngModel",t.selectData().spokenLangCode)}}function ge(_,l){if(_&1){let t=y();o(0,"div",29)(1,"p-checkbox",30),g("ngModelChange",function(i){d(t);let a=C();return u(a.selectedItems,i)||(a.selectedItems=i),p(i)}),r(),o(2,"label",31),b(3),r()()}if(_&2){let t=l.$implicit,e=C();s(),m("inputId",t.value)("value",t),h("ngModel",e.selectedItems),s(),m("for",t.value),s(),v(" ",t.label," ")}}function _e(_,l){if(_&1){let t=y();o(0,"div",29)(1,"p-checkbox",30),g("ngModelChange",function(i){d(t);let a=C();return u(a.selectedItems,i)||(a.selectedItems=i),p(i)}),r(),o(2,"label",31),b(3),r()()}if(_&2){let t=l.$implicit,e=C();s(),m("inputId",t.value)("value",t),h("ngModel",e.selectedItems),s(),m("for",t.value),s(),v(" ",t.label," ")}}var me=class _{constructor(){this.#e=S(se);this.#t=S(le);this.langOptions=[{label:"zh-Hans"},{label:"zh-Hant"},{label:"en"}];this.lang=c("zh-Hans");this.data=c([]);this.cols=[{field:"id",header:"ID"},{field:"folderName",header:"Folder name"},{field:"channelName",header:"Channel name"},{field:"spokenLangCode",header:"Spoken language",type:"select",selectData:W}];this.mode=c("create");this.selectData=c(null);this.channelTree=c([]);this.channelMap=new Map;this.spokenLangList=W;this.drawerVisible=c(!1);this.fileDialogVisible=c(!1);this.items=c([]);this.filterItems=P(()=>this.items().filter(l=>{let t=l.value.includes(this.searchId()),e=l.label.toLowerCase().includes(this.searchName().toLowerCase()),i=this.searchDate()?this.searchDate()?.toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit"}).replace(/\//g,"-")===l.date:!0;return t&&e&&i}));this.selectedItems=c([]);this.searchId=c("");this.searchName=c("");this.searchDate=c(null)}#e;#t;ngOnInit(){this.loadData(),this.loadChannelData();for(let l=1;l<=1e3;l++)this.items().push({label:`name${l}`,value:`${l}`,date:"2023-01-01"})}loadData(){this.#e.getAllVirtualFolders(this.lang()).subscribe({next:l=>{this.data.set(l)}})}loadChannelData(){this.#t.getAllChannels(this.lang()).subscribe({next:l=>{this.channelTree.set(this.buildTreeData(l,void 0,void 0,void 0,this.channelMap))}})}buildTreeData(l,t="parentChannelId",e="id",i="name",a=new Map){let n=[];return l.forEach(f=>{a.set(f[e],L(x({},f),{key:f[e],label:f[i],children:[]}))}),l.forEach(f=>{let k=a.get(f[e]);if(!f[t])n.push(k);else{let E=a.get(f[t]);E&&E.children.push(k)}}),n}handleDrop({channelId:l,toParentId:t,beforeId:e}){this.#e.moveVirtualFolder(l,t,e).subscribe({next:i=>{this.loadData()}})}handleCreate(){this.mode.set("create"),this.selectData.set({})}handleEdit(l){this.mode.set("edit");let t=I(l);t.channel={key:t.channelId,label:this.channelMap.get(t.channelId)?.name},this.selectData.set(t)}handleAdd(l){this.drawerVisible.set(!0)}handleAddFile(){this.fileDialogVisible.set(!0)}updateSelectChannel(){let l=I(this.selectData());l.channel&&(l.channelId=l.channel.id,delete l.channel),this.mode()==="edit"?this.#e.update(l.id,l).subscribe({next:t=>{this.loadData()}}):this.#e.create(l).subscribe({next:t=>{this.loadData()}})}handleDelete(l){this.#e.delete(l.id).subscribe({next:t=>{this.loadData()}})}resetFilters(){this.searchName.set(""),this.searchDate.set(null)}static{this.\u0275fac=function(t){return new(t||_)}}static{this.\u0275cmp=N({type:_,selectors:[["app-folder"]],decls:29,vars:28,consts:[["item",""],[1,"p-4"],[1,"flex","justify-between"],["optionLabel","label","optionValue","label",3,"ngModelChange","onChange","options","ngModel"],["label","Create",3,"onClick"],["parentKey","parentFolderId",3,"onDrop","onEdit","onAdd","onDelete","data","cols"],[3,"visibleChange","header","modal","visible"],[1,"form"],[1,"w-full","grid","grid-cols-2","gap-2","mt-4"],["styleClass","w-full","severity","secondary","label","Cancel"],["styleClass","w-full","label","Confirm",3,"onClick"],["position","right",3,"visibleChange","visible"],["icon","pi pi-file","label","Add",3,"onClick","text"],["icon","pi pi-trash","label","Remove","severity","danger",3,"text"],["header","Add file","styleClass","overflow-hidden",3,"visibleChange","modal","visible"],[1,"flex","gap-4"],["type","text","pInputText","","placeholder","ID",3,"ngModelChange","ngModel"],["type","text","pInputText","","placeholder","name",3,"ngModelChange","ngModel"],["placeholder","date","dateFormat","yy-mm-dd",3,"ngModelChange","ngModel","showIcon","showClear"],["label","Reset",3,"onClick"],[1,"grid","grid-cols-2","gap-4","flex-1","mt-4"],["styleClass","border h-full",3,"items","itemSize"],[1,"flex","justify-end","gap-2","mt-4"],["label","Cancel","severity","secondary"],["label","Save"],[1,"form-item"],["type","text","pInputText","","placeholder","Folder name",3,"ngModelChange","ngModel"],["containerStyleClass","w-full","placeholder","Select a channel",3,"ngModelChange","ngModel","options"],["optionLabel","key","optionValue","value","placeholder","Select a spoken language","appendTo","body",3,"ngModelChange","options","ngModel"],[1,"p-2"],["name","group",3,"ngModelChange","inputId","value","ngModel"],[1,"ml-2",3,"for"]],template:function(t,e){if(t&1){let i=y();o(0,"div",1)(1,"div",2)(2,"p-selectbutton",3),g("ngModelChange",function(n){return d(i),u(e.lang,n)||(e.lang=n),p(n)}),w("onChange",function(){return d(i),p(e.loadData())}),r(),o(3,"p-button",4),w("onClick",function(){return d(i),p(e.handleCreate())}),r()(),o(4,"c-tree-table",5),w("onDrop",function(n){return d(i),p(e.handleDrop(n))})("onEdit",function(n){return d(i),p(e.handleEdit(n))})("onAdd",function(n){return d(i),p(e.handleAdd(n))})("onDelete",function(n){return d(i),p(e.handleDelete(n))}),r()(),o(5,"p-dialog",6),g("visibleChange",function(n){return d(i),u(e.selectData,n)||(e.selectData=n),p(n)}),D(6,ue,13,5,"div",7),o(7,"div",8),M(8,"p-button",9),o(9,"p-button",10),w("onClick",function(){return d(i),p(e.updateSelectChannel())}),r()()(),o(10,"p-drawer",11),g("visibleChange",function(n){return d(i),u(e.drawerVisible,n)||(e.drawerVisible=n),p(n)}),o(11,"p-button",12),w("onClick",function(){return d(i),p(e.handleAddFile())}),r(),M(12,"p-button",13),r(),o(13,"p-dialog",14),g("visibleChange",function(n){return d(i),u(e.fileDialogVisible,n)||(e.fileDialogVisible=n),p(n)}),o(14,"div",15)(15,"input",16),g("ngModelChange",function(n){return d(i),u(e.searchId,n)||(e.searchId=n),p(n)}),r(),o(16,"input",17),g("ngModelChange",function(n){return d(i),u(e.searchName,n)||(e.searchName=n),p(n)}),r(),o(17,"p-datepicker",18),g("ngModelChange",function(n){return d(i),u(e.searchDate,n)||(e.searchDate=n),p(n)}),r(),o(18,"p-button",19),w("onClick",function(){return d(i),p(e.resetFilters())}),r()(),o(19,"div",20)(20,"p-virtualscroller",21),D(21,ge,4,5,"ng-template",null,0,T),r(),o(23,"p-virtualscroller",21),D(24,_e,4,5,"ng-template",null,0,T),r()(),o(26,"div",22),M(27,"p-button",23)(28,"p-button",24),r()()}t&2&&(s(2),m("options",e.langOptions),h("ngModel",e.lang),s(2),m("data",e.data)("cols",e.cols),s(),V(F(26,ce)),m("header",e.mode()==="edit"?"Edit":"Create")("modal",!0),h("visible",e.selectData),s(),B(e.selectData()?6:-1),s(4),h("visible",e.drawerVisible),s(),m("text",!0),s(),m("text",!0),s(),V(F(27,he)),m("modal",!0),h("visible",e.fileDialogVisible),s(2),h("ngModel",e.searchId),s(),h("ngModel",e.searchName),s(),h("ngModel",e.searchDate),m("showIcon",!0)("showClear",!0),s(3),m("items",e.filterItems())("itemSize",50),s(3),m("items",e.selectedItems())("itemSize",50))},dependencies:[ae,O,H,G,q,R,A,z,j,$,Z,re,oe,U,Q,te,ee,ne,ie,K,J,pe,de,Y,X],encapsulation:2})}};export{me as FolderComponent};
