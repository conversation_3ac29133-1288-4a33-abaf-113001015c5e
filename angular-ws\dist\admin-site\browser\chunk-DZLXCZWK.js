import{lb as o}from"./chunk-GXYB24H2.js";import{a as t,aa as m,fa as l}from"./chunk-AKB6GSCA.js";var s=class r{constructor(e){this.restService=e;this.apiName="Default";this.addFilesToAlbum=(e,a,i)=>this.restService.request({method:"POST",url:`/api/app/album/files-to-album/${e}`,body:a},t({apiName:this.apiName},i));this.create=(e,a)=>this.restService.request({method:"POST",url:"/api/app/album",body:e},t({apiName:this.apiName},a));this.delete=(e,a)=>this.restService.request({method:"DELETE",url:`/api/app/album/${e}`},t({apiName:this.apiName},a));this.get=(e,a)=>this.restService.request({method:"GET",url:`/api/app/album/${e}`},t({apiName:this.apiName},a));this.getAlbumFiles=(e,a)=>this.restService.request({method:"GET",url:`/api/app/album/album-files/${e}`},t({apiName:this.apiName},a));this.getAlbumsByChannel=(e,a)=>this.restService.request({method:"GET",url:`/api/app/album/albums-by-channel/${e}`},t({apiName:this.apiName},a));this.getAllAlbums=(e,a)=>this.restService.request({method:"GET",url:"/api/app/album/albums",params:{languageCode:e}},t({apiName:this.apiName},a));this.getList=(e,a)=>this.restService.request({method:"GET",url:"/api/app/album",params:{channelId:e.channelId,albumType:e.albumType,channelContentCode:e.channelContentCode,sorting:e.sorting,skipCount:e.skipCount,maxResultCount:e.maxResultCount}},t({apiName:this.apiName},a));this.linkToChannelByChannelIdAndAlbumId=(e,a,i)=>this.restService.request({method:"POST",url:"/api/app/album/link-to-channel",params:{channelId:e,albumId:a}},t({apiName:this.apiName},i));this.linkToChannelByChannelIdAndAlbumIds=(e,a,i)=>this.restService.request({method:"POST",url:`/api/app/album/link-to-channel/${e}`,body:a},t({apiName:this.apiName},i));this.removeFileFromAlbum=(e,a,i)=>this.restService.request({method:"DELETE",url:"/api/app/album/file-from-album",params:{albumId:e,fileId:a}},t({apiName:this.apiName},i));this.reorderAlbumFiles=(e,a,i)=>this.restService.request({method:"POST",url:`/api/app/album/reorder-album-files/${e}`,body:a},t({apiName:this.apiName},i));this.unlinkFromChannelByAlbumId=(e,a)=>this.restService.request({method:"POST",url:`/api/app/album/unlink-from-channel/${e}`},t({apiName:this.apiName},a));this.unlinkFromChannelByAlbumIds=(e,a)=>this.restService.request({method:"POST",url:"/api/app/album/unlink-from-channel",body:e},t({apiName:this.apiName},a));this.update=(e,a,i)=>this.restService.request({method:"PUT",url:`/api/app/album/${e}`,body:a},t({apiName:this.apiName},i));this.updateAlbumFile=(e,a,i,u)=>this.restService.request({method:"PUT",url:"/api/app/album/album-file",params:{albumId:e,fileId:a},body:i},t({apiName:this.apiName},u))}static{this.\u0275fac=function(a){return new(a||r)(l(o))}}static{this.\u0275prov=m({token:r,factory:r.\u0275fac,providedIn:"root"})}};export{s as a};
