import type { ChapterTreeDto, CreateUpdateEBookDto, EBookDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedAndSortedResultRequestDto, PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class EBookService {
  apiName = 'Default';
  

  create = (input: CreateUpdateEBookDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, EBookDto>({
      method: 'POST',
      url: '/api/app/e-book',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/e-book/${id}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, EBookDto>({
      method: 'GET',
      url: `/api/app/e-book/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getAllEBooks = (languageCode?: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, EBookDto[]>({
      method: 'GET',
      url: '/api/app/e-book/e-books',
      params: { languageCode },
    },
    { apiName: this.apiName,...config });
  

  getChapterTreeByEBookId = (eBookId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ChapterTreeDto[]>({
      method: 'GET',
      url: `/api/app/e-book/chapter-tree-by-eBook-id/${eBookId}`,
    },
    { apiName: this.apiName,...config });
  

  getEBooksByChannelId = (channelId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, EBookDto[]>({
      method: 'GET',
      url: `/api/app/e-book/e-books-by-channel-id/${channelId}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedAndSortedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<EBookDto>>({
      method: 'GET',
      url: '/api/app/e-book',
      params: { sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  linkToChannelByChannelIdAndEBookId = (channelId: number, eBookId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/e-book/link-to-channel',
      params: { channelId, eBookId },
    },
    { apiName: this.apiName,...config });
  

  linkToChannelByChannelIdAndEBookIds = (channelId: number, eBookIds: number[], config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: `/api/app/e-book/link-to-channel/${channelId}`,
      body: eBookIds,
    },
    { apiName: this.apiName,...config });
  

  unlinkFromChannelByEBookId = (eBookId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: `/api/app/e-book/unlink-from-channel/${eBookId}`,
    },
    { apiName: this.apiName,...config });
  

  unlinkFromChannelByEBookIds = (eBookIds: number[], config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/e-book/unlink-from-channel',
      body: eBookIds,
    },
    { apiName: this.apiName,...config });
  

  update = (id: number, input: CreateUpdateEBookDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, EBookDto>({
      method: 'PUT',
      url: `/api/app/e-book/${id}`,
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
