using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using HolyBless.Buckets;
using HolyBless.Entities.VirtualFolders;
using HolyBless.Enums;
using HolyBless.Services;
using HolyBless.TreeJsonSnapshots;
using HolyBless.VirtualFolders.Dtos;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.VirtualFolders
{
    [AllowAnonymous]
    public class ReadOnlyVirtualFolderAppService : HolyBlessAppService, IReadOnlyVirtualFolderAppService
    {
        protected readonly IRepository<VirtualDiskFolder, int> _virtualFolderRepository;
        protected readonly IRepository<FolderToFile> _folderToBucketFileRepository;
        protected readonly ITreeJsonSnapshotAppService _treeSnapshotAppService;

        private static readonly System.Text.Json.JsonSerializerOptions JsonOptions = new()
        {
            PropertyNameCaseInsensitive = true
        };

        public ReadOnlyVirtualFolderAppService(
             IRepository<VirtualDiskFolder, int> virtualFolderRepository,
             IRepository<FolderToFile> folderToBucketFileRepository,
             ITreeJsonSnapshotAppService treeSnapshotAppService,
             IRequestContextService requestContextService,
             ICachedFileUrlAppService cachedFileUrlAppService
            ) : base(cachedFileUrlAppService, requestContextService)
        {
            _folderToBucketFileRepository = folderToBucketFileRepository;
            _treeSnapshotAppService = treeSnapshotAppService;
            _virtualFolderRepository = virtualFolderRepository;
        }

        /// <summary>
        /// Get a paginated list of virtual folders based on search criteria.
        /// Usage: Virtual folder list page API
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<VirtualFolderDto>> GetListAsync(VirtualFolderSearchDto input)
        {
            var queryable = await _virtualFolderRepository.GetQueryableAsync();
            var lang = _requestContextService!.GetLanguageCode();
            var query = queryable
                .WhereIf(input.ChannelId.HasValue, x => x.ChannelId == input.ChannelId);

            var totalCount = await query.CountAsync();

            var virtualFolders = await query
                .OrderBy(input.Sorting ?? "Weight desc, CreationTime desc")
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount)
                .ToListAsync();

            var virtualFolderDtos = ObjectMapper.Map<List<VirtualDiskFolder>, List<VirtualFolderDto>>(virtualFolders);

            return new PagedResultDto<VirtualFolderDto>(totalCount, virtualFolderDtos);
        }

        /// <summary>
        /// list of files within a virtual folder without pagination
        /// </summary>
        /// <param name="folderId"></param>
        /// <returns></returns>
        public async Task<List<VirtualFolderFileDto>> GetAllFolderFiles(int folderId)
        {
            var queryable = await _folderToBucketFileRepository.GetQueryableAsync();
            var folderFiles = await queryable
                .Include(x => x.BucketFile)
                .Where(x => x.FolderId == folderId)
                .OrderBy("BucketFile.DeliveryDate")
                .ToListAsync();

            var folderFileDtos = folderFiles.Select(ftf => new VirtualFolderFileDto
            {
                FileId = ftf.BucketFileId,
                FileName = ftf.BucketFile.FileName,
                Title = ftf.Title,
                MediaType = ftf.BucketFile.MediaType,
                LastModificationTime = ftf.BucketFile.LastModificationTime ?? ftf.BucketFile.CreationTime,
            }).ToList();
            await FillFileUrls(folderFileDtos);
            return folderFileDtos;
        }

        /// <summary>
        /// Get All file URLs within a virtual folder without pagination
        /// </summary>
        /// <param name="folderId"></param>
        /// <returns></returns>
        public async Task<List<string>> GetAllFolderFileUrls(int folderId)
        {
            var queryable = await _folderToBucketFileRepository.GetQueryableAsync();
            var folderFiles = await queryable
                .Where(x => x.FolderId == folderId)
                .Select(x => new VirtualFolderFileDto { FileId = x.BucketFileId })
                .ToListAsync();

            await FillFileUrls(folderFiles);
            return folderFiles.Select(x => x.FileUrl ?? "").ToList();
        }

        /// <summary>
        /// Get a paginated list of files within a virtual folder with sorting support.
        /// Similar to GetAlbumFilesAsync but with pagination and sorting.
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<VirtualFolderFileDto>> GetFolderFilesAsync(VirtualFolderFileSearchDto input)
        {
            var queryable = await _folderToBucketFileRepository.GetQueryableAsync();
            var lang = _requestContextService!.GetLanguageCode();
            var query = queryable
                .Include(x => x.BucketFile)
                .WhereIf(input.FolderId.HasValue, x => x.FolderId == input.FolderId);

            var totalCount = await query.CountAsync();

            var folderFiles = await query
                .OrderBy(input.Sorting ?? "BucketFile.DeliveryDate")
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount)
                .ToListAsync();

            var folderFileDtos = folderFiles.Select(ftf => new VirtualFolderFileDto
            {
                FileId = ftf.BucketFileId,
                FileName = ftf.BucketFile.FileName,
                Title = ftf.Title,
                MediaType = ftf.BucketFile.MediaType,
                LastModificationTime = ftf.BucketFile.LastModificationTime ?? ftf.BucketFile.CreationTime,
            }).ToList();

            await FillFileUrls(folderFileDtos);

            return new PagedResultDto<VirtualFolderFileDto>(totalCount, folderFileDtos);
        }

        /// <summary>
        /// Get the virtual folder tree structure for a specific folder.
        /// Usage: For folder navigation, folder detail page left side menu
        /// </summary>
        /// <param name="folderId"></param>
        /// <returns></returns>
        public async Task<List<VirtualFolderTreeDto>> GetVirtualFolderTreeAsync(int folderId)
        {
            // Try to read cached snapshot first
            var cachedResult = await TryGetCachedVirtualFolderTreeAsync(folderId);
            if (cachedResult != null && cachedResult.Count > 0)
            {
                return cachedResult;
            }

            // Build from database
            var allFolders = await GetAllDescendantFoldersAsync(folderId);

            if (!allFolders.Any())
                return [];

            // Ensure no duplicates in source data
            var uniqueFolders = allFolders.GroupBy(f => f.Id).Select(g => g.First()).ToList();

            // Build the tree using a completely different algorithm: Recursive assembly
            var dtoCache = new Dictionary<int, VirtualFolderTreeDto>();

            // Recursive function to build node and its subtree
            VirtualFolderTreeDto BuildNode(VirtualDiskFolder folder)
            {
                // Check cache first to prevent duplicate processing
                if (dtoCache.TryGetValue(folder.Id, out var cachedDto))
                {
                    return cachedDto;
                }

                // Create new DTO
                var dto = ObjectMapper.Map<VirtualDiskFolder, VirtualFolderTreeDto>(folder);
                dto.Children.Clear(); // Ensure clean state

                // Cache it immediately to prevent infinite recursion
                dtoCache[folder.Id] = dto;

                // Find and build children
                var children = uniqueFolders.Where(f => f.ParentFolderId == folder.Id).ToList();

                foreach (var child in children.OrderByDescending(c => c.Weight).ThenBy(c => c.FolderName))
                {
                    var childDto = BuildNode(child);
                    dto.Children.Add(childDto);
                }

                return dto;
            }

            // Build root folders (direct children of folderId)
            var rootFolders = uniqueFolders
                .Where(f => f.ParentFolderId == folderId)
                .OrderByDescending(f => f.Weight)
                .ThenBy(f => f.FolderName)
                .Select(f =>
                {
                    var dto = BuildNode(f);
                    dto.IsRoot = true;
                    return dto;
                })
                .ToList();

            // Update snapshot after reading from database
            await UpdateVirtualFolderTreeSnapshotAsync(folderId, rootFolders);

            return rootFolders;
        }

        // Ultra-optimized: Single query with projection for minimal memory usage (FIXED)
        // GUARANTEED duplicate-free version
        private async Task<List<VirtualDiskFolder>> GetAllDescendantFoldersAsync(int folderId)
        {
            var queryable = await _virtualFolderRepository.GetQueryableAsync();

            // Get hierarchy structure with minimal data
            var folderHierarchy = await queryable
                .Where(x => x.ParentFolderId.HasValue)
                .Select(x => new { x.Id, ParentId = x.ParentFolderId!.Value, x.Weight })
                .ToListAsync();

            // Build parent-child mapping
            var childrenMap = folderHierarchy
                .GroupBy(x => x.ParentId)
                .ToDictionary(g => g.Key, g => g.OrderByDescending(x => x.Weight)
                                              .Select(x => x.Id)
                                              .ToList());

            // Find ALL descendant IDs using BFS - this prevents duplicates
            var descendantIds = new HashSet<int>(); // Use HashSet to absolutely prevent duplicates
            var queue = new Queue<int>();
            var visited = new HashSet<int>();

            queue.Enqueue(folderId);
            visited.Add(folderId); // Don't include the root folder itself

            while (queue.Count > 0)
            {
                var currentId = queue.Dequeue();

                if (childrenMap.TryGetValue(currentId, out var childIds))
                {
                    foreach (var childId in childIds)
                    {
                        // This is the KEY: only add if we haven't seen it before
                        if (visited.Add(childId)) // Add returns false if already exists
                        {
                            descendantIds.Add(childId); // Add to result set
                            queue.Enqueue(childId);     // Continue traversing from this node
                        }
                        // If visited.Add returns false, we skip this node entirely
                    }
                }
            }

            // Convert HashSet to List and batch load entities
            if (descendantIds.Count == 0)
                return new List<VirtualDiskFolder>();

            var descendantIdsList = descendantIds.ToList();

            var result = await queryable
                .Where(x => descendantIdsList.Contains(x.Id))
                .OrderBy(x => x.ParentFolderId)
                .ThenBy(x => x.FolderName)
                .ToListAsync();

            return result;
        }

        /// <summary>
        /// Try to get cached virtual folder tree from snapshot.
        /// </summary>
        /// <param name="folderId">The folder ID used as root</param>
        /// <returns>Cached virtual folder tree or null if not available</returns>
        private async Task<List<VirtualFolderTreeDto>?> TryGetCachedVirtualFolderTreeAsync(int folderId)
        {
            try
            {
                var json = await _treeSnapshotAppService.GetTreeJsonAsync(TreeType.VirtualDiskFolder, folderId);
                if (!string.IsNullOrWhiteSpace(json))
                {
                    var cached = System.Text.Json.JsonSerializer.Deserialize<List<VirtualFolderTreeDto>>(json, JsonOptions);
                    return cached;
                }
            }
            catch
            {
                // ignore and return null to build from DB
            }

            return null;
        }

        /// <summary>
        /// Update the virtual folder tree snapshot with the latest data.
        /// </summary>
        /// <param name="folderId">The folder ID used as root</param>
        /// <param name="folderTree">The virtual folder tree data to cache</param>
        private async Task UpdateVirtualFolderTreeSnapshotAsync(int folderId, List<VirtualFolderTreeDto> folderTree)
        {
            try
            {
                var json = System.Text.Json.JsonSerializer.Serialize(folderTree);
                await _treeSnapshotAppService.UpdateTreeJsonAsync(TreeType.VirtualDiskFolder, folderId, json);
            }
            catch
            {
                // ignore snapshot update failures
            }
        }

      
    }
}