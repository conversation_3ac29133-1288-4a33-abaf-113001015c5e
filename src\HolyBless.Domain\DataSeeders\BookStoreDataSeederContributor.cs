﻿using System;
using System.Threading.Tasks;
using HolyBless.Books;
using HolyBless.Entities.Books;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.DataSeeders;

public class BookStoreDataSeederContributor
    : IDataSeedContributor, ITransientDependency
{
    private readonly IRepository<EBook, int> _bookRepository;

    public static EBook[] EbookList = new EBook[]
    {
        new EBook(1)
        {
            Title = "教法导读",
            Description = "教法导读",
            Weight = 1,
            ChannelId = 3
        }
    };

    public BookStoreDataSeederContributor(IRepository<EBook, int> bookRepository)
    {
        _bookRepository = bookRepository;
    }

    public async Task SeedAsync(DataSeedContext context)
    {
        foreach (var book in EbookList)
        {
            if (await _bookRepository.FindAsync(book.Id) == null)
            {
                await _bookRepository.InsertAsync(book, autoSave: true);
            }
        }
    }
}