import { Component, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TreeModule } from 'primeng/tree';
import { AccordionModule } from 'primeng/accordion';
import { ButtonModule } from 'primeng/button';
import { TreeNode } from 'primeng/api';
import { ActivatedRoute, Router } from '@angular/router';
import { ReadOnlyEBookService } from '@/proxy/holy-bless/books';
import { ChapterTreeDto, EBookDto } from '@/proxy/holy-bless/books/dtos';
import { ReadOnlyArticleService } from '@/proxy/holy-bless/articles';
import { ArticleAggregateResult } from '@/proxy/holy-bless/results';
import { skip, Subscription } from 'rxjs';
import { I18nService } from '@/services/i18n.service';
import { ReadOnlyChannelService } from '@/proxy/holy-bless/channels';
import { LoadingService } from '@/services/loading.service';
import { PlayerService } from '@/services/player.service';
import { MobileService } from '@/services/mobile.service';
import { MobileCategoryFilesDrawerComponent } from '@/components/mobile-catagory-files-drawer/mobile-catagory-files-drawer';

@Component({
  selector: 'app-artical-detail',
  standalone: true,
  imports: [
    CommonModule,
    TreeModule,
    AccordionModule,
    ButtonModule,
    MobileCategoryFilesDrawerComponent,
  ],
  templateUrl: './book-detail.html',
  styleUrls: ['./book-detail.scss'],
})
export class BookDetailComponent {
  route = inject(ActivatedRoute);
  #ReadOnlyEBookService = inject(ReadOnlyEBookService);
  #ReadOnlyArticleService = inject(ReadOnlyArticleService);
  #ReadOnlyChannelService = inject(ReadOnlyChannelService);
  items = signal<ArticleAggregateResult[]>([]);
  files = signal<TreeNode[]>([]);
  subs = new Subscription();
  i18nService = inject(I18nService);
  router = inject(Router);
  loadingService = inject(LoadingService);
  playerService = inject(PlayerService);
  mobileService = inject(MobileService);
  leftDrawerVisible = signal(false);
  bookDetail = signal<EBookDto>({} as EBookDto);

  selectedFile!: TreeNode;
  ngOnInit() {
    this.route.params.subscribe((params) => {
      const eBookId = params['eBookId'];
      if (eBookId) {
        this.loadArticlesByChapterId(+eBookId);
        this.loadBookDetail(+eBookId);
        this.changeLanguage(+eBookId);
      }
    });
  }

  changeLanguage(eBookId: number) {
    this.subs.unsubscribe();
    this.subs = new Subscription();
    const sub = this.i18nService.language$.pipe(skip(1)).subscribe((lang) => {
      this.#ReadOnlyChannelService
        .getMatchedChannelByBookId(eBookId)
        .subscribe({
          next: (channel) => {
            if (!channel) {
              this.router.navigateByUrl('/landing');
              return;
            }
            this.router.navigateByUrl(`/ebooks/ebook-card/${channel.id}`);
          },
        });
    });
    this.subs.add(sub);
  }

  loadBookDetail(eBookId: number) {
    if (!eBookId) return;
    //TODO: When switch language, need to redirect to book list page
    //Since delivery date is removed from EBook, so that there is no match method
    /*this.loadingService.show();
    this.#ReadOnlyEBookService.getMatchedEBook(eBookId).subscribe({
      next: (res) => {
        this.bookDetail.set(res);
      }
    })
    */
  }

  loadArticlesByChapterId(eBookId: number) {
    if (!eBookId) return;
    this.loadingService.show();
    this.#ReadOnlyEBookService.getChapterTreeByEBookId(eBookId).subscribe({
      next: (data) => {
        this.files.set(data.map((chapter) => this.buildTreeNode(chapter)));
        this.loadingService.hide();
      },
      error: (error) => {
        console.error('获取电子书章节树失败:', error);
        this.loadingService.hide();
      },
    });
  }

  buildTreeNode(node: ChapterTreeDto): TreeNode {
    return {
      key: node.id + '',
      label: node.title,
      data: node,
      children: node.children
        ? node.children.map((child) => this.buildTreeNode(child))
        : [],
    };
  }

  onFileSelect(node: TreeNode) {
    if (!node.key) return;
    this.loadingService.show();
    this.leftDrawerVisible.set(false);
    this.#ReadOnlyArticleService
      .getArticleAggregatesByChapterId(node.data.id)
      .subscribe({
        next: (data) => {
          this.items.set(data);
          this.loadingService.hide();
        },
        error: (error) => {
          console.error('获取文章详情失败:', error);
          this.loadingService.hide();
        },
      });
  }

  onPlayClick(event: Event, item: any) {
    event.stopPropagation(); // 阻止事件冒泡
    event.preventDefault(); // 阻止默认行为（可选）
    console.log('item', item);
    this.playerService.multiPlayVideo(item.articleFiles);
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
