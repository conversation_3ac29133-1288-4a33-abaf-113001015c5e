 import { Environment } from '@abp/ng.core';

const baseUrl = 'http://localhost:4200';

const oAuthConfig = {
  issuer: 'https://admin.holyblesspan.com/',//'https://localhost:44362/',
  redirectUri: baseUrl,
  clientId: 'holybless_App_Admin', //If run against local BE, remove _Admin
  responseType: 'code',
  scope: 'offline_access holybless',
  requireHttps: true,
};

export const environment = {
  production: false,
  application: {
    baseUrl,
    name: 'holybless',
  },
  oAuthConfig,
  apis: {
    default: {
      url: 'https://admin.holyblesspan.com',  //can not have ending /
      rootNamespace: 'Holybless',
    },
    AbpAccountPublic: {
      url: oAuthConfig.issuer,
      rootNamespace: 'AbpAccountPublic',
    },
  },
} as Environment;
